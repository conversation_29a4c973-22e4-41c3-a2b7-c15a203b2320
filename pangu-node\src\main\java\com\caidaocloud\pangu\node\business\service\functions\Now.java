package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.*;
import lombok.val;

import java.util.Map;

public class Now extends AbstractFunction {
    @Override
    public String getName() {
        return "NOW";
    }

    @Override
    public AviatorObject call(Map<String, Object> env) {
        val now = new AviatorTimestamp(System.currentTimeMillis());
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("()=").append(now.getValue(env)).toString());
        }
        return now;
    }
}

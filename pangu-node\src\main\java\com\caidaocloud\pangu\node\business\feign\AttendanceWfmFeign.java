package com.caidaocloud.pangu.node.business.feign;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.node.business.dto.ArrangeMonthAnalyze;
import com.caidaocloud.pangu.node.common.annotations.FeignThrow;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "${feign.rename.caidaocloud-attendance-service:caidaocloud-attendance-service}",
        fallback = AttendanceWfmFeignFallback.class, configuration = FeignConfiguration.class)
public interface AttendanceWfmFeign {

    @GetMapping("/api/attendance/wfm/statistic/v1/getEmpArrangeMonthAnalyzeList")
    @FeignThrow
    Result<PageResult<ArrangeMonthAnalyze>> getEmpArrangeMonthAnalyzeList(@RequestParam long startDate,
                                                                          @RequestParam long endDate,
                                                                          @RequestParam int pageNo,
                                                                          @RequestParam int pageSize,
                                                                          @RequestParam String accountId);





    @GetMapping("/api/attendance/wfm/statistic/v1/getEmpNonProcessArrangeMonthAnalyzeList")
    @FeignThrow
    Result<PageResult<ArrangeMonthAnalyze>> getEmpNonProcessArrangeMonthAnalyzeList(@RequestParam long startDate,
                                                                          @RequestParam long endDate,
                                                                          @RequestParam int pageNo,
                                                                          @RequestParam int pageSize,
                                                                          @RequestParam String accountId);



    @GetMapping("/api/attendance/wfm/statistic/v1/getArrangeMonthAnalyzeList")
    @FeignThrow
    Result<PageResult<ArrangeMonthAnalyze>> getArrangeMonthAnalyzeList(@RequestParam long startDate,
                                                                       @RequestParam long endDate,
                                                                       @RequestParam int pageNo,
                                                                       @RequestParam int pageSize,
                                                                       @RequestParam String accountId);
}

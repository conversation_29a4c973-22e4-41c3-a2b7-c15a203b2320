package com.caidaocloud.pangu.interfaces;

import javax.servlet.http.HttpServletResponse;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.application.service.BonusReportService;
import com.caidaocloud.pangu.infrastructure.util.FilterUtil;
import com.caidaocloud.pangu.interfaces.vo.BonusReportPageVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Slf4j
@RestController
@RequestMapping("/api/pangu/v1/bonus/report")
@Api(value = "/api/pangu/v1/bonus/report", tags = "奖金报表")
public class BonusReportController {
	@Autowired
	private BonusReportService bonusReportService;

	@PostMapping()
	@ApiOperation("奖金报表")
	public Result<BonusReportPageVo> list(@RequestBody BonusReportDto dto) {
		return Result.ok(bonusReportService.loadReport(dto));
	}


	@PostMapping("export")
	@ApiOperation("奖金报表导出")
	public void export(@RequestBody BonusReportDto dto, HttpServletResponse response) {
		bonusReportService.exportReport(dto, response);
	}

}

package com.caidaocloud.pangu.application.dto.emp;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
@ApiModel("员工信息")
public class EmpInfoDto {
	@ApiModelProperty("员工工号")
	private String workno;

	@ApiModelProperty("员工姓名")
	private String name;

	@ApiModelProperty("英文名")
	private String enName;

	@ApiModelProperty("岗位")
	private String post;

	@ApiModelProperty("岗位名称")
	private String postTxt;

	@ApiModelProperty("所属组织")
	private String organize;

	@ApiModelProperty("所属组织名称")
	private String organizeTxt;

	@ApiModelProperty("公司邮箱")
	private String companyEmail;

	@ApiModelProperty("员工id")
	private String empId;

	@ApiModelProperty("手机号")
	private String phone;

	@ApiModelProperty("工作地")
	private String workplace;
	@ApiModelProperty("工作地名称")
	private String workplaceTxt;

	@ApiModelProperty("公司")
	private String company;
	@ApiModelProperty("公司名称")
	private String companyTxt;

	/**
	 * 入职日期
	 */
	private Long hireDate;

	/**
	 * 用工类型\员工类型
	 */
	private DictSimple empType;


	/**
	 * 离职日期
	 */
	private Long leaveDate;

	/**
	 * 员工状态
	 */
	private EnumSimple empStatus;
}

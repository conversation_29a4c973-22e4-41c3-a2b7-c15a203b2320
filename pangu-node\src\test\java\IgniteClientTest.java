import java.sql.Types;

import org.apache.ignite.Ignition;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.ClientCacheConfiguration;
import org.apache.ignite.client.ClientException;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.ClientConfiguration;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2025/1/4
 */

public class IgniteClientTest {

	static ClientCacheConfiguration empCacheCfg = new ClientCacheConfiguration();
	static String cache_name = "empCacheWithPgsqlv3";
	static String table_name = "employees";
	static {
		empCacheCfg.setName(cache_name);
		CacheJdbcPojoStoreFactory<Integer, Employee> factory = new CacheJdbcPojoStoreFactory<>();
		factory.setDialect(new BasicJdbcDialect());
		// 指定 datasource，与服务端相同
		factory.setDataSourceBean("pgDataSource");
		JdbcType employeeType = getJdbcType();
		factory.setTypes(employeeType);
		// empCacheCfg.setca
		// empCacheCfg.setCacheStoreFactory(factory);
		// 开启读穿透
		// empCacheCfg.setReadThrough(true);
	}

	// jdbc的字段、表映射关系
	private static JdbcType getJdbcType() {
		JdbcType employeeType = new JdbcType();
		employeeType.setCacheName(cache_name);
		employeeType.setDatabaseTable(table_name);
		employeeType.setKeyType(Integer.class);
		employeeType.setKeyFields(new JdbcTypeField(Types.INTEGER, "id", Integer.class, "id"));
				employeeType.setValueFields(
				new JdbcTypeField(Types.INTEGER, "id", Integer.class, "id"),
				new JdbcTypeField(Types.VARCHAR, "name", String.class, "name"),
				new JdbcTypeField(Types.VARCHAR, "email", String.class, "email"),
				new JdbcTypeField(Types.INTEGER, "salary", Integer.class, "salary"),
				new JdbcTypeField(Types.VARCHAR, "addr", String.class, "addr")
		);

		employeeType.setValueType(Employee.class);

		return employeeType;
	}

	@Test
	public void  ignite_pojo_test(){
		Employee employee = new Employee();
		employee.setEmpId(2L);
		employee.setSalary(100);
		employee.setAddr(new Address());
		ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
		try (IgniteClient igniteClient = Ignition.startClient(cfg)) {


			ClientCache<Long, Employee> cache = igniteClient.getOrCreateCache("empClientCache");

			System.out.format(">>> Created cache [%s].\n", cache.getName());

			cache.put(employee.getEmpId(), employee);

			System.out.format(">>> Saved [%s] in the cache.\n", employee);

			Employee cachedVal = cache.get(employee.getEmpId());

			System.out.format(">>> Loaded [%s] from the cache.\n", cachedVal);
			Assert.assertEquals(cachedVal, employee);

			// IgniteCache<Integer, EmployeeDbVer> employeeCache = igniteClient.getOrCreateCache(empCacheCfg);
			// Iterator<Cache.Entry<Integer, EmployeeDbVer>> iterator = employeeCache.iterator();
			//
			// System.out.println(">>>>>>>>>>>>>>>>>>>>>>>");
			// if (!iterator.hasNext()) {
			// 	System.out.println("No value found ");
			// }
			// iterator.forEachRemaining(d -> {
			// 	System.out.printf("value for key %s is %s \n", d.getKey(), d.getValue());
			// });
			//
			// System.out.println("Employee value is " + employeeCache.get(6));
			// igniteClient.close();
		}
	}

	@Test
	public void  ignite_pojo_test2(){
		ClientCacheEntity entity = new ClientCacheEntity();
		entity.setId(2);
		entity.setName("client_entity");
		ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
		try (IgniteClient igniteClient = Ignition.startClient(cfg)) {

			ClientCache cache = igniteClient.getOrCreateCache("EntityClientCache");

			System.out.format(">>> Created cache [%s].\n", cache.getName());

			// cache.put(entity.getId(), entity);

			// System.out.format(">>> Saved [%s] in the cache.\n", entity);

			ClientCacheEntity cachedVal = (ClientCacheEntity) cache.get(entity.getId());

			System.out.format(">>> Loaded [%s] from the cache.\n", cachedVal);
			Assert.assertEquals(cachedVal, entity);

			// IgniteCache<Integer, EmployeeDbVer> employeeCache = igniteClient.getOrCreateCache(empCacheCfg);
			// Iterator<Cache.Entry<Integer, EmployeeDbVer>> iterator = employeeCache.iterator();
			//
			// System.out.println(">>>>>>>>>>>>>>>>>>>>>>>");
			// if (!iterator.hasNext()) {
			// 	System.out.println("No value found ");
			// }
			// iterator.forEachRemaining(d -> {
			// 	System.out.printf("value for key %s is %s \n", d.getKey(), d.getValue());
			// });
			//
			// System.out.println("Employee value is " + employeeCache.get(6));
			// igniteClient.close();
		}
	}

	@Test
	public void ignite(){
		ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
				// .getcon;


		try (IgniteClient igniteClient = Ignition.startClient(cfg)) {
			System.out.println();
			System.out.println(">>> Thin client put-get example started.");

			final String CACHE_NAME = "put-get-example";

			ClientCache<Integer, String> cache = igniteClient.getOrCreateCache(CACHE_NAME);

			System.out.format(">>> Created cache [%s].\n", CACHE_NAME);

			Integer key = 1;
			String val = "!23";

			cache.put(key, val);

			System.out.format(">>> Saved [%s] in the cache.\n", val);

			String cachedVal = cache.get(key);

			System.out.format(">>> Loaded [%s] from the cache.\n", cachedVal);
		}
		catch (ClientException e) {
			System.err.println(e.getMessage());
			throw e;
		}
	}
}

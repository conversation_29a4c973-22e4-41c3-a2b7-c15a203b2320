package com.caidaocloud.pangu.infrastructure.repository;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.pangu.domain.entity.ApproveConfigDo;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.repository.IBonusSchemaRepository;
import com.caidaocloud.pangu.infrastructure.repository.po.BonusSchemaPo;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Repository
public class BonusSchemaRepositoryImpl implements IBonusSchemaRepository  {
	@Override
	public ApproveConfigDo loadApproveConfig(String bid) {
		return DataQuery.identifier(ApproveConfigDo.APPROVE_CONFIG_IDENTIFIER).oneOrNull(bid, ApproveConfigDo.class);
	}

	@Override
	public BonusSchema insert(BonusSchema data) {
		BonusSchemaPo po = BonusSchemaPo.fromEntity(data);
		String dataId = DataInsert.identifier(BonusSchemaPo.BONUS_SCHEMA_IDENTIFIER).insert(po);
		data.setBid(dataId);
		return data;
	}

	@Override
	public BonusSchema selectById(String bid, String bonusSchemaIdentifier) {
		return Optional.ofNullable(DataQuery.identifier(BonusSchemaPo.BONUS_SCHEMA_IDENTIFIER).decrypt()
				.specifyLanguage().queryInvisible()
				.oneOrNull(bid, BonusSchemaPo.class)).map(BonusSchemaPo::toEntity).orElse(null);
	}

	@Override
	public List<BonusSchema> selectList(String identifier, String tenantId) {
		PageResult<BonusSchemaPo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage()
				.limit(-1, 1)
				.queryInvisible().filter(DataFilter.eq("tenantId", tenantId)
						.andNe("deleted", Boolean.TRUE.toString()), BonusSchemaPo.class);

		return null != pageResult ? Sequences.sequence(pageResult.getItems()).map(BonusSchemaPo::toEntity)
				.toList() : Lists.newArrayList();
	}

	@Override
	public int delete(BonusSchema bonusSchema) {
		DataDelete.identifier(bonusSchema.getIdentifier()).softDelete(bonusSchema.getBid());
		return 0;
	}

	@Override
	public int updateById(BonusSchema data) {
		DataUpdate.identifier(data.getIdentifier()).update(BonusSchemaPo.fromEntity(data));
		return 0;
	}

	@Override
	public PageResult<BonusSchema> selectPage(BasePage basePage) {
		PageResult<BonusSchemaPo> result = DataQuery.identifier(BonusSchema.BONUS_SCHEMA_IDENTIFIER)
				.limit(basePage.getPageSize(), basePage.getPageNo())
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()), BonusSchemaPo.class);
		List<BonusSchema> list = Sequences.sequence(result.getItems()).map(BonusSchemaPo::toEntity).toList();
		return new PageResult<>(list, result.getPageNo(), result.getPageSize(), result.getTotal());
	}

	@Override
	public ApproveConfigDo loadApproveConfigBySchemaId(String schemaId) {
		PageResult<ApproveConfigDo> result = DataQuery.identifier(ApproveConfigDo.APPROVE_CONFIG_IDENTIFIER).filter(DataFilter.eq("schemaId", schemaId).andNe("deleted", Boolean.TRUE.toString()), ApproveConfigDo.class);
		if (result.getItems().isEmpty()) {
			return null;
		}
		ApproveConfigDo approveConfigDo = result.getItems().get(0);
		approveConfigDo.setEnabled(approveConfigDo.getFormId() != null);
		return approveConfigDo;
	}
	// ApproveConfigDo loadApporveConfig(String bid);
	//
	// PageResult<BonusSchema> selectPage(BasePage basePage);
}

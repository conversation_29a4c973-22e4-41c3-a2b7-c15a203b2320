package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.util.Map;

public class StrContains extends AbstractFunction {
    @Override
    public String getName() {
        return "STR_CONTAINS";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val first = FunctionUtils.getStringValue(aviatorObject1, env);
        val second = FunctionUtils.getStringValue(aviatorObject2, env);
        val result = first.contains(second);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(first).append(",").append(second).append(")=").append(result).toString());
        }
        return FunctionUtils.wrapReturn(result);
    }
}

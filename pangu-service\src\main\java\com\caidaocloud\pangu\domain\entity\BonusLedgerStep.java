package com.caidaocloud.pangu.domain.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import java.util.Date;
import java.util.Map;

/**
 * 奖金账套
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class BonusLedgerStep extends DataSimple {

	/**
	 * 账套id
	 */
	private String ledgerId;

	/**
	 * 编排id
	 */
	private String composeId;

	/**
	 * 执行id
	 */
	private String execId;


	/**
	 * 状态
	 */
	private EnumSimple status;

	private String nextStepId;
	private Long execTime;


	/**
	 * 步骤排序
	 */
	private Integer sort;

	private Integer total;
	private Integer succeed;

	public static final String BONUS_LEDGER_STEP_IDENTIFIER = "entity.bonus.BonusLedgerStep";

	public BonusLedgerStep() {
		setIdentifier(BONUS_LEDGER_STEP_IDENTIFIER);
	}

	public BonusLedgerStep(String ledgerId, String composeId) {
		this();
		this.ledgerId = ledgerId;
		this.composeId = composeId;
		EnumSimple simple = new EnumSimple();
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setCreateTime(getUpdateTime());
		setCreateBy(getUpdateBy());
		simple.setValue(String.valueOf(BonusLedgerStepStatus.NOT_EXECUTED.getValue()));
		this.status = simple;
		this.sort = 0; // Default value
	}

	public static BonusLedgerStep loadByExecId(String execId) {
		return SpringUtil.getBean(IBonusLedgerRepository.class).loadStepByExecId(execId);
	}

	public void exec(Map<String,String> context) {
		// EnumSimple status = new EnumSimple();
		// status.setValue(String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue()));
		// setStatus(status);
		// setExecTime(System.currentTimeMillis());

		SpringUtil.getBean(IBonusLedgerRepository.class).exec(this,context);
	}

	public void update() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		SpringUtil.getBean(IBonusLedgerRepository.class).updateStep(this);
	}
}

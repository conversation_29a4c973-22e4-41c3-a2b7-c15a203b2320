package com.caidaocloud.pangu.infrastructure.repository;

import java.util.Map;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDataMapDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.pangu.domain.repository.IFormRepository;
import com.caidaocloud.web.Result;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
@Repository
public class FormRepositoryImpl implements IFormRepository {
	@Autowired
	private FormFeignClient formFeignClient;

	@Override
	public FormDefDto getFormDefById(String formId) {
		Result<FormDefDto> def = formFeignClient.getFormDefById(formId);
		return def.getData();
	}

	@Override
	public String saveFormData(String formId, Map<String, Object> formData) {
		FormDataDto dataDto = new FormDataDto();
		dataDto.setPropertiesMap(formData);
		Result<String> result = formFeignClient.saveFormData(formId, dataDto);
		if (null == result || !result.isSuccess()) {
			throw new ServerException("保存表单数据失败");
		}
		return result.getData();
	}

	@Override
	public void updateFormData(String formId, String formDataId, Map<String, Object> formData) {
		FormDataDto dataDto = new FormDataDto();
		dataDto.setId(formDataId);
		dataDto.setPropertiesMap(formData);
		formFeignClient.update(formId, dataDto);
		if (StringUtils.isEmpty(formDataId)) {
			throw new ServerException("保存表单数据失败");
		}
	}

	@Override
	public Map<String, Object> getFormDataById(String formId, String formDataId) {
		Result<FormDataMapDto> result = formFeignClient.getFormDataMap(formId, formDataId);
		if (null == result || !result.isSuccess()) {
			throw new ServerException("获取表单数据失败");
		}
		return result.getData().getPropertiesMap();
	}

}

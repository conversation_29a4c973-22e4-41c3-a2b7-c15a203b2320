package com.caidaocloud.pangu.infrastructure.repository;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ComponentPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.MdDataQuery;
import com.caidaocloud.masterdata.entity.emp.EmpInfoEntity;
import com.caidaocloud.pangu.domain.repository.IEmpRepository;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Triple;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/8/3
 */
@Repository
public class EmpRepository implements IEmpRepository {
	@Override
	public EmpSimple loadEmp(String empId) {
		PageResult<EmpInfoEntity> emp = MdDataQuery.identifier(EmpInfoEntity.EMP_IDENTIFIER)
				.specifyLanguage().decrypt().queryInvisible()
				.filter(DataFilter.eq("empId", empId), EmpInfoEntity.class, System.currentTimeMillis());
		if (emp.getItems().isEmpty()) {
			return null;
		}
		return ObjectConverter.convert(emp.getItems().get(0),EmpSimple.class);
	}

	@Override
	public Map<String, Map<String, Object>> loadEmps(List<String> empIds, Map<String, List<Triple<String, String, String>>> identifierPropertyGroup) {
		if (empIds == null || empIds.isEmpty()) {
			return Maps.map();
		}

		Map<String, Map<String, Object>> resultMap = new HashMap<>();

		for (Map.Entry<String, List<Triple<String, String, String>>> entry : identifierPropertyGroup.entrySet()) {
			String identifier = entry.getKey();
			List<Triple<String, String, String>> properties = entry.getValue();
			List<Map<String, Object>> empInfoList = empInfo(identifier, empIds,properties);

			// Combine results into a single map for each empId
			for (Map<String, Object> empInfo : empInfoList) {
				String empId = (String) empInfo.get("empId"); // Assuming empId is part of the result
				resultMap.merge(empId, empInfo, (existingMap, newMap) -> {
					existingMap.putAll(newMap); // Merge with existing entry
					return existingMap;
				});
			}
		}

		return resultMap;
	}

	private List<Map<String, Object>> empInfo(String identifier,List<String> empIds,List<Triple<String, String, String>> fields) {
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString()).andIn("empId", empIds);
		List<DataSimple> list = DataQuery.identifier(identifier).limit(-1, 1)
				.filter(filter, DataSimple.class).getItems();

		// New code to transform DataSimple list into List<Map<String, Object>>
		return list.stream().map(dataSimple -> {
			Map<String, Object> resultMap = new HashMap<>();
			resultMap.put("empId", ((SimplePropertyValue) dataSimple.getProperties().get("empId")).getValue());
			fields.forEach(field -> {
				Object value = dataSimple.getProperties().get(field.second());
				if (value != null) {
					resultMap.put(field.third(), value instanceof SimplePropertyValue ? ((SimplePropertyValue) value).getValue() : ((ComponentPropertyValue) value).toText());
				}
			});
			return resultMap;
		}).collect(Collectors.toList());
	}

}

package com.caidaocloud.pangu.infrastructure.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.repository.IBonusStructItemRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepositoryImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
@Repository
public class BonusStructItemRepositoryImpl  extends BaseRepositoryImpl<BonusStructItem> implements IBonusStructItemRepository {

	@Override
	public PageResult<BonusStructItem> selectPage(BasePage page, String keywords, String structId) {
		return DataQuery.identifier(BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER)
				.limit(page.getPageSize(), page.getPageNo())
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()).andEq("structId", structId).andRegexIf("name", keywords, () -> StringUtils.isNotEmpty(keywords)), BonusStructItem.class);
	}

	@Override
	public List<BonusStructItem> selectList(String structId) {
		return DataQuery.identifier(BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER)
				.limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()).andEq("structId", structId), BonusStructItem.class).getItems();
	}

	@Override
	public List<BonusStructItem> selectListByStructIds(List<String> idList) {
		return DataQuery.identifier(BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER)
				.limit(-1, 1)
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()).andIn("structId", idList), BonusStructItem.class).getItems();

	}
}

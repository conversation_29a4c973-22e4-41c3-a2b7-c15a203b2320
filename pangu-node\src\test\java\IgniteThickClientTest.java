import java.sql.Types;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

import javax.cache.Cache;
import javax.cache.configuration.Factory;
import javax.sql.DataSource;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.CachePeekMode;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.QueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcType<PERSON>ield;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.client.ClientCache;
import org.apache.ignite.client.ClientException;
import org.apache.ignite.client.IgniteClient;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.ClientConfiguration;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/4
 */

public class IgniteThickClientTest {

	static CacheConfiguration empCacheCfg = new CacheConfiguration();
	static String cache_name = "employees_ignite_cahce";
	static String table_name = "employees_ignite";
	static {
		empCacheCfg.setName(cache_name);

		// 开启读穿透
		empCacheCfg.setWriteThrough(true);
		empCacheCfg.setReadThrough(true);

		CacheJdbcPojoStoreFactory<Long, Employee> factory = new CacheJdbcPojoStoreFactory<>();
		factory.setDialect(new BasicJdbcDialect());
		factory.setDataSourceFactory(IgniteThickClientTest.getDataSourceFactory());

		JdbcType employeeType = getJdbcType();
		factory.setTypes(employeeType);
		// empCacheCfg.setca
		empCacheCfg.setCacheStoreFactory(factory);

		QueryEntity qryEntity = new QueryEntity();
		// qryEntity.setTableName(table_name);
		qryEntity.setKeyType(Long.class.getName());
		qryEntity.setValueType(Employee.class.getName());
		qryEntity.setKeyFieldName("id");

		Set<String> keyFields = new HashSet<>();
		keyFields.add("id");
		qryEntity.setKeyFields(keyFields);

		LinkedHashMap<String, String> fields = new LinkedHashMap<>();
		fields.put("id", "java.lang.Long");
		fields.put("name", "java.lang.String");

		qryEntity.setFields(fields);

		empCacheCfg.setQueryEntities(Collections.singletonList(qryEntity));
	}

	// jdbc的字段、表映射关系
	private static JdbcType getJdbcType() {
		JdbcType employeeType = new JdbcType();
		employeeType.setCacheName(cache_name);
		employeeType.setDatabaseTable(table_name.toUpperCase());
		employeeType.setKeyType(Long.class);
		employeeType.setValueType(Employee.class);

		employeeType.setKeyFields(new JdbcTypeField(Types.BIGINT, "id", Long.class, "empId"));
		employeeType.setValueFields(
				new JdbcTypeField(Types.BIGINT, "id", Long.class, "empId"),
				new JdbcTypeField(Types.VARCHAR, "name", String.class, "name"),
				new JdbcTypeField(Types.VARCHAR, "email", String.class, "email"),
				new JdbcTypeField(Types.BIGINT, "salary", Long.class, "salary"),
				new JdbcTypeField(Types.VARCHAR, "addr", String.class, "addr")
		);

		return employeeType;
	}

	@Test
	public void  ignite_pojo_test(){
		Employee employee = new Employee();
		employee.setEmpId(2L);
		employee.setSalary(100L);
		employee.setAddr(new Address());
		// ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
		IgniteConfiguration cfg = new IgniteConfiguration();
		// cfg.getDiscoverySpi()
		// cfg.setClientMode(true);
		try (Ignite igniteClient = Ignition.start(cfg)) {

			IgniteCache<Long, Employee> cache = igniteClient.getOrCreateCache("empClientCache");

			System.out.format(">>> Created cache [%s].\n", cache.getName());

			cache.put(employee.getEmpId(), employee);

			System.out.format(">>> Saved [%s] in the cache.\n", employee);

			Employee cachedVal = cache.get(employee.getEmpId());

			System.out.format(">>> Loaded [%s] from the cache.\n", cachedVal);
			Assert.assertEquals(cachedVal, employee);

			// IgniteCache<Integer, EmployeeDbVer> employeeCache = igniteClient.getOrCreateCache(empCacheCfg);
			// Iterator<Cache.Entry<Integer, EmployeeDbVer>> iterator = employeeCache.iterator();
			//
			// System.out.println(">>>>>>>>>>>>>>>>>>>>>>>");
			// if (!iterator.hasNext()) {
			// 	System.out.println("No value found ");
			// }
			// iterator.forEachRemaining(d -> {
			// 	System.out.printf("value for key %s is %s \n", d.getKey(), d.getValue());
			// });
			//
			// System.out.println("Employee value is " + employeeCache.get(6));
			// igniteClient.close();
		}
	}

	@Test
	public void  ignite_pojo_test2(){
		ClientCacheEntity entity = new ClientCacheEntity();
		entity.setId(2);
		entity.setName("client_entity");
		// ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
		IgniteConfiguration configuration = new IgniteConfiguration();
		configuration.setClientMode(true);
		// configuration.setPeerClassLoadingEnabled(true);
		// configuration.setDeploymentMode(DeploymentMode.CONTINUOUS);
		try (Ignite igniteClient = Ignition.start(configuration)) {
			igniteClient.destroyCache(cache_name);
			IgniteCache<Long, Employee> employeeCache = igniteClient.createCache(empCacheCfg);
			Employee employee = employeeCache.get(1L);
			employeeCache.loadCache(null);
			Iterator<Cache.Entry<Long, Employee>> iterator = employeeCache.iterator();

			System.out.println(">>>>>>>>>>>>>>>>>>>>>>>");
			if (!iterator.hasNext()) {
				System.out.println("No value found ");
				return;
			}
			iterator.forEachRemaining(d -> {
				System.out.printf("value for key %s is %s \n", d.getKey(), d.getValue());
			});

			System.out.println("Employee value is " + employeeCache.get(6L));
			igniteClient.destroyCache(cache_name);
		}
		catch (Exception e) {
			throw e;
		}
	}



	@Test
	public void  ignite_pojo_test3(){
		IgniteConfiguration configuration = new IgniteConfiguration();
		// configuration.setCacheConfiguration(empCacheCfg);
		// configuration.setClientMode(true);
		// configuration.setPeerClassLoadingEnabled(true);
		// configuration.setDeploymentMode(DeploymentMode.CONTINUOUS);
		try (Ignite igniteClient = Ignition.start(configuration)) {
			try (IgniteCache<Long, Employee> employeeCache = igniteClient.getOrCreateCache(empCacheCfg)) {
				employeeCache.loadCache(null);
				// employeeCache.putAll();
				// employeeCache.clearAll();
				Iterator<Cache.Entry<Long, Employee>> iterator = employeeCache.iterator();

				System.out.println(">>>>>>>>>>>>>>>>>>>>>>>");
				if (!iterator.hasNext()) {
					System.out.println("No value found ");
					return;
				}
				iterator.forEachRemaining(d -> {
					System.out.printf("value for key %s is %s \n", d.getKey(),d.getValue());
				});

				SqlFieldsQuery sql = new SqlFieldsQuery(
						"select id from Employee").setLocal(true);
				List<List<?>> all = employeeCache.query(sql).getAll();
				Assert.assertEquals(5,all.size());
				System.out.println("Employee value is " + employeeCache.get(6L));
			}finally {
				igniteClient.destroyCache(cache_name);
			}

		}
		catch (Exception e) {
			throw e;
		}
	}
	@Test
	public void ignite(){
		ClientConfiguration cfg = new ClientConfiguration().setAddresses("127.0.0.1:10999");
				// .getcon;


		try (IgniteClient igniteClient = Ignition.startClient(cfg)) {
			System.out.println();
			System.out.println(">>> Thin client put-get example started.");

			final String CACHE_NAME = "put-get-example";

			ClientCache<Integer, String> cache = igniteClient.getOrCreateCache(CACHE_NAME);

			System.out.format(">>> Created cache [%s].\n", CACHE_NAME);

			Integer key = 1;
			String val = "!23";

			cache.put(key, val);

			System.out.format(">>> Saved [%s] in the cache.\n", val);

			String cachedVal = cache.get(key);

			System.out.format(">>> Loaded [%s] from the cache.\n", cachedVal);
		}
		catch (ClientException e) {
			System.err.println(e.getMessage());
			throw e;
		}
	}

	@Test
	public void sql_tedst_2(){
		try (Ignite ignite=Ignition.start(IgniteSqlTest.igniteCfg)) {

			try (IgniteCache<Integer, Person2> cache = ignite.cache(IgniteSqlTest.cache_name);) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				cache.loadCache(null);
				Assert.assertEquals(5,cache.size(CachePeekMode.ALL));

				SqlFieldsQuery sql = new SqlFieldsQuery(
						"select concat(id, ' ', name) from Person");

// Iterate over the result set.
				try (QueryCursor<List<?>> cursor = cache.query(sql)) {
					for (List<?> row : cursor)
						System.out.println("personName=" + row.get(0));
				}
				sql = new SqlFieldsQuery(
						"select * from Person");
				List<List<?>> all = cache.query(sql).getAll();
				Assert.assertEquals(5, all.size());
				for (List<?> row : all)
					System.out.println("personName=" + row.get(1));
			}finally {
				ignite.destroyCache(cache_name);
			}

		}
	}

	public static Factory<DataSource> getDataSourceFactory() {
		return () -> {
			HikariDataSource driverManagerDataSource = new HikariDataSource();
			driverManagerDataSource.setDriverClassName("org.postgresql.Driver");
			driverManagerDataSource.setJdbcUrl("**********************************************************************");
			driverManagerDataSource.setUsername("postgres");
			driverManagerDataSource.setPassword("Caidao01");
			return driverManagerDataSource;
		};
	}
}

package com.caidaocloud.pangu.domain.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.pangu.domain.repository.IBonusStructComposeRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 * 奖金结构--编排配置
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
public class BonusStructCompose extends DataSimple {
	private String structId;
	private String composeId;
	private Integer sort;
	public static final String BONUS_STRUCT_COMPOSE_IDENTIFIER = "entity.bonus.BonusStructCompose";

	public BonusStructCompose() {
		setIdentifier(BONUS_STRUCT_COMPOSE_IDENTIFIER);
		this.sort = 65535;
	}

	public BonusStructCompose(String structId, String composeId) {
		this(); // 调用无参构造函数
		this.structId = structId;
		this.composeId = composeId;
	}

	public String create() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setCreateTime(getUpdateTime());
		setCreateBy(getUpdateBy());
		SpringUtil.getBean(IBonusStructComposeRepository.class).insert(this);
		return this.getBid();
	}

	public void update(){
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		SpringUtil.getBean(IBonusStructComposeRepository.class).updateById(this);
	}

	public void delete() {
		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		SpringUtil.getBean(IBonusStructComposeRepository.class).delete(this);

	}
}

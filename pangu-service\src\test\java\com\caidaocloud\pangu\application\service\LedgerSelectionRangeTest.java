package com.caidaocloud.pangu.application.service;

import java.time.LocalDateTime;
import java.time.ZoneId;

import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.enums.MonthPeriod;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2025/6/16
 */
public class LedgerSelectionRangeTest {
	@Test
	public void special_current_month_test() {
		// Create a BonusLedger instance for testing
		BonusLedger ledger = new BonusLedger();

		// Test case 1: Special condition enabled, current month
		ledger.setMonth(1735660800000L); // 2025-01-01 00:00:00
		ledger.setSpecial(true);
		ledger.setHireDateMonth(MonthPeriod.CURRENT_MONTH);
		ledger.setHireDateDay(15);
		ledger.setTerminationDateMonth(MonthPeriod.CURRENT_MONTH);
		ledger.setTerminationDateDay(31);

		BonusLedger.SelectionDateRange range1 = ledger.calculateSelectionRange();

		// Verify dates - should be 2025-01-15 and 2025-01-31
		LocalDateTime expectedHireDate1 = LocalDateTime.of(2025, 1, 15, 0, 0);
		LocalDateTime expectedTermDate1 = LocalDateTime.of(2025, 1, 31, 0, 0);

		long expectedHireTimestamp1 = expectedHireDate1.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		long expectedTermTimestamp1 = expectedTermDate1.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

		Assert.assertEquals(expectedHireTimestamp1, range1.getHireDate().longValue());
		Assert.assertEquals(expectedTermTimestamp1, range1.getTerminationDate().longValue());


	}

	@Test
	public void special_privous_month_test(){
		BonusLedger ledger = new BonusLedger();
// Test case 2: Special condition enabled, previous month
		ledger.setSpecial(true);
		ledger.setMonth(1735660800000L); // 2025-01-01 00:00:00
		ledger.setHireDateMonth(MonthPeriod.PREVIOUS_MONTH);
		ledger.setHireDateDay(15);
		ledger.setTerminationDateMonth(MonthPeriod.PREVIOUS_MONTH);
		ledger.setTerminationDateDay(31);

		BonusLedger.SelectionDateRange range2 = ledger.calculateSelectionRange();

		// Verify dates - should be 2024-12-15 and 2024-12-31
		LocalDateTime expectedHireDate2 = LocalDateTime.of(2024, 12, 15, 0, 0);
		LocalDateTime expectedTermDate2 = LocalDateTime.of(2024, 12, 31, 0, 0);

		long expectedHireTimestamp2 = expectedHireDate2.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		long expectedTermTimestamp2 = expectedTermDate2.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

		Assert.assertEquals(expectedHireTimestamp2, range2.getHireDate().longValue());
		Assert.assertEquals(expectedTermTimestamp2, range2.getTerminationDate().longValue());


	}
	
	@Test
	public void feb_month_test(){
		BonusLedger ledger = new BonusLedger();
// Test case 2: Special condition enabled, previous month
		ledger.setSpecial(true);
		ledger.setMonth(1739676406000L); // 2025-02-16 11:26:46
		ledger.setHireDateMonth(MonthPeriod.CURRENT_MONTH);
		ledger.setHireDateDay(15);
		ledger.setTerminationDateMonth(MonthPeriod.CURRENT_MONTH);
		ledger.setTerminationDateDay(31);


		BonusLedger.SelectionDateRange range2 = ledger.calculateSelectionRange();

		// Verify dates - should be 2024-12-15 and 2024-12-31
		LocalDateTime expectedHireDate2 = LocalDateTime.of(2025, 2, 15, 0, 0);
		LocalDateTime expectedTermDate2 = LocalDateTime.of(2025, 2, 28, 0, 0);

		long expectedHireTimestamp2 = expectedHireDate2.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		long expectedTermTimestamp2 = expectedTermDate2.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

		Assert.assertEquals(expectedHireTimestamp2, range2.getHireDate().longValue());
		Assert.assertEquals(expectedTermTimestamp2, range2.getTerminationDate().longValue());

	}

	@Test
	public void selection_test(){
		BonusLedger ledger = new BonusLedger();
// Test case 3: Special condition disabled
		ledger.setSpecial(false);
		ledger.setSelectionStartDate(1735660800000L); // 2025-01-01 00:00:00
		ledger.setSelectionEndDate(1738339200000L); // 2025-01-31 00:00:00

		BonusLedger.SelectionDateRange range3 = ledger.calculateSelectionRange();

		// Verify dates - should use the directly set values
		Assert.assertEquals(ledger.getSelectionEndDate(), range3.getHireDate());
		Assert.assertEquals(ledger.getSelectionStartDate(), range3.getTerminationDate());
	}
}

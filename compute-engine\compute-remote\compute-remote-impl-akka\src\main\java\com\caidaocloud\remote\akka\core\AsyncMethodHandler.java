package com.caidaocloud.remote.akka.core;

import java.time.Duration;
import java.util.concurrent.CompletionStage;

import akka.actor.ActorNotFound;
import akka.actor.ActorRef;
import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import lombok.SneakyThrows;

import static com.caidaocloud.remote.akka.constant.AkkaConstant.REMOTE_REQUEST_TIMEOUT;

/**
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
public class AsyncMethodHandler extends AkkaMethodHandler {


	public AsyncMethodHandler(ActorSystem actorSystem, MethodMetadata methodMetadata) {
		super(actorSystem, methodMetadata);
	}

	@SneakyThrows
	@Override
	Object doExecute(ActorSelection actorSelection, ActorMessageWrapper request) {
		CompletionStage<ActorRef> future = actorSelection.resolveOne(Duration.ofMillis(REMOTE_REQUEST_TIMEOUT));
		try {
			ActorRef actorRef = future.toCompletableFuture().get();
			actorRef.tell(request, ActorRef.noSender());
		}
		catch (ActorNotFound e) {
			throw new ServerException("Actor path '" + actorSelection.pathString() + "' not exist", e);
		}
		return null;
	}
}

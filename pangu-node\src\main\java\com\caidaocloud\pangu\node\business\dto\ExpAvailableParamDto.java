package com.caidaocloud.pangu.node.business.dto;

import lombok.Data;
import lombok.val;

@Data
public class ExpAvailableParamDto {

    private String name;

    private String property;

    public enum ParamType{
        env,source,work,privacy,task
    }

    public static ExpAvailableParamDto instance(String property, String name){
        val instance = new ExpAvailableParamDto();
        instance.property = property;
        instance.name = name;
        return instance;
    }

}

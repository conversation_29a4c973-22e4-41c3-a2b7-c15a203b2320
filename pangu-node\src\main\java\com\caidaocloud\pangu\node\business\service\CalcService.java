package com.caidaocloud.pangu.node.business.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SimpleDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.pangu.core.dto.IgniteCacheValue;
import com.caidaocloud.pangu.core.dto.NodeExec;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.core.ignite.KeyValueDto;
import com.caidaocloud.pangu.node.business.enums.EmpKeyType;
import com.caidaocloud.pangu.node.business.enums.ValueFormat;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.service.functions.FunctionLogTool;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CalcService {

    @Autowired
    private IgniteUtil igniteUtil;

    @Autowired
    private MetadataOperatorService metadataOperatorService;

    @Autowired
    private Locker locker;

    public void calcGroup(NodeExec.SubNodeExec execDto, List<TaskDef.Rule> ruleList,
                          Map<String, String> context, TaskDef.Detail taskDefDetail) {
        var from = execDto.getFrom();
        ruleList.sort(Comparator.comparing(TaskDef.Rule::getRuleId));
        val rule = ruleList.get(from);
        ExpressionTool.initGroup();
        try {
            ExpressionTool.exec(rule.getRuleVid(), rule.getExpression(), context);
            log.info("group rule name " + rule.getName());
            val groupResults = ExpressionTool.getGroupResult();
            groupResults.forEach(groupResult -> {
                List<Pair<TaskDef.Rule, Object>> ruleToValues = Lists.list();
                ruleToValues.add(Pair.pair(rule, groupResult.getValue()));
                String cacheKey = groupResult.getKeys().stream().map(it -> String.valueOf(it.getValue())).collect(Collectors.joining("-"));
                writeCacheResult(execDto.getResultCacheId(), cacheKey, ruleToValues, true);
                if (taskDefDetail.isOutput()) {
                    groupResult.getKeys().forEach(key -> {
                        String newKey = taskDefDetail.getKeyMappings().stream().filter(keyMapping -> keyMapping.getInput().equals(key.getText()))
                                .findFirst().get().getOutput();
                        key.setText(newKey);
                    });
                    groupResult.getKeys()
                            .addAll(taskDefDetail.getKeyMappings().stream().filter(it->it.getInput().startsWith("envContext-")).map(it->
                                    new KeyValue(it.getOutput(), context.get("$.env." + it.getInput()
                                            .replace("envContext-", "")))).collect(Collectors.toList()));
                    val lock = locker.getLock("writeGroupResult_"+execDto.getExecNodeSeqId());
                    lock.lock();
                    try{
                        writeModelResult(taskDefDetail.getOutputIdentifier(), groupResult.getKeys(), ruleToValues);
                    }finally {
                        lock.unlock();
                    }
                }
            });
        } finally {
            ExpressionTool.removeGroup();
        }
    }

    public void calc(NodeExec.SubNodeExec execDto, List<TaskDef.Rule> ruleList,
                     Map<String, String> context, TaskDef.Detail taskDefDetail) {
        List<List<KeyValueDto>> sourceDataList = igniteUtil.getContext(execDto.getCacheId())
                .loadProperties(null, Lists.list(), Lists.list("bid"), execDto.getFrom(), execDto.getTo() - execDto.getFrom() + 1);
        log.info("sourceDataList"+FastjsonUtil.toJson(sourceDataList));
        if(sourceDataList.isEmpty()){
            return;
        }
        MetadataVo sourceMetadata = null;
        if(taskDefDetail.getDsType().equals(TaskDef.SourceType.PAAS)){
            sourceMetadata = metadataOperatorService.load((String) sourceDataList.get(0).stream().filter(it -> it.getKey().equals("identifier")).findAny().get().getValue());
        }
        for (List<KeyValueDto> source : sourceDataList) {
            Map<String, Object> newContext = Maps.map();
            newContext.putAll(context);
            fillInEnvironmentContext(sourceMetadata, source, newContext, "$.source.", ruleList);
            if (taskDefDetail.isEmpKeyOpen()) {
                val workMetadata = metadataOperatorService.load("entity.hr.EmpWorkInfo");
                val privateMetadata = metadataOperatorService.load("entity.hr.EmpPrivateInfo");
                if (taskDefDetail.getEmpKeyType().equals(EmpKeyType.EMP)) {
                    List<KeyValueDto> workInfo = (List<KeyValueDto>) igniteUtil.getContext("work." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("empId",
                                    (String) newContext.get("$.source." + taskDefDetail.getEmpKey() + "$empId")), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(workMetadata, workInfo, newContext, "$.work.", ruleList);
                    List<KeyValueDto> privateInfo = (List<KeyValueDto>) igniteUtil.getContext("private." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("empId",
                                    (String) newContext.get("$.work.empId")), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(privateMetadata, privateInfo, newContext, "$.privacy.", ruleList);
                } else if (taskDefDetail.getEmpKeyType().equals(EmpKeyType.EMP_ID)) {
                    List<KeyValueDto> workInfo = (List<KeyValueDto>) igniteUtil.getContext("work." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("empId",
                                    (String) newContext.get("$.source." + taskDefDetail.getEmpKey())), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(workMetadata, workInfo, newContext, "$.work.", ruleList);
                    List<KeyValueDto> privateInfo = (List<KeyValueDto>) igniteUtil.getContext("private." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("empId",
                                    (String) newContext.get("$.source." + taskDefDetail.getEmpKey())), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(privateMetadata, privateInfo, newContext, "$.privacy.", ruleList);
                } else if (taskDefDetail.getEmpKeyType().equals(EmpKeyType.WORK_NO)) {
                    List<KeyValueDto> workInfo = (List<KeyValueDto>) igniteUtil.getContext("work." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("workno",
                                    (String) newContext.get("$.source." + taskDefDetail.getEmpKey())), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(workMetadata, workInfo, newContext, "$.work.", ruleList);
                    List<KeyValueDto> privateInfo = (List<KeyValueDto>) igniteUtil.getContext("private." + execDto.getCacheId())
                            .loadProperties(DataFilter.eq("empId",
                                    (String) newContext.get("$.work.empId")), Lists.list(),
                                    Lists.list("bid"), 0, 1).stream().findAny().orElse(null);
                    fillInEnvironmentContext(privateMetadata, privateInfo, newContext, "$.privacy.", ruleList);
                }
            }
            val inputKeys = taskDefDetail.getKeyMappings().stream().map(it -> {
                Object value;
                if(it.getInput().startsWith("envContext-")){
                    value = newContext.get("$.env." + it.getInput().replace("envContext-", ""));
                }else{
                    value = newContext.get("$.source." + it.getInput());
                }
                return new KeyValue(it.getInput(), value);
            }).collect(Collectors.toList());
            String cacheKey = inputKeys.stream().map(it -> String.valueOf(it.getValue())).collect(Collectors.joining("-"));
            List<String> sortedBids = Lists.list();
            Map<String, List<String>> reference = Maps.map();
            for(TaskDef.Rule rule : ruleList){
                val ruleId = rule.getRuleId();
                reference.put(ruleId, ruleList.stream().filter(it->rule.getExpression().contains("$.task."+it.getRuleId())).map(it->it.getRuleId()).collect(Collectors.toList()));
            }
            log.info(FastjsonUtil.toJson(reference));
            int total = ruleList.size();
            int cursor = 0;
            int failed = 0;
            while(true){
                val rule = ruleList.get(cursor);
                val reliedOn = reference.get(rule.getRuleId());
                if(sortedBids.containsAll(reliedOn) || reliedOn.isEmpty()){
                    cursor++;
                    sortedBids.add(rule.getRuleId());
                    failed = 0;
                }else{
                    ruleList.remove(cursor);
                    ruleList.add(rule);
                    failed ++;
                    if(cursor + failed >= total){
                        throw new ServerException("奖金项循环依赖");
                    }
                }
                if(cursor >= total){
                    break;
                }
            }
            ruleList.forEach(rule->{
                log.info(rule.getRuleId());
            });
            val ruleToValues = ruleList.stream().map(rule -> {
                newContext.put("$.env.ruleDataType", rule.getRuleDataType());
                Object calcResult;
                try{
                    FunctionLogTool.init(cacheKey, execDto.getExecSeqId(), execDto.getExecNodeSeqId(), rule);
                    calcResult = ExpressionTool.exec(rule.getRuleVid(), rule.getExpression(), newContext);
                    newContext.put("$.task." + rule.getRuleId(), formatRuleCalcResult(rule.getRuleDataType(), calcResult, ValueFormat.CONTEXT));
                    log.error("newContext" + rule.getRuleId());
                }finally {
                    FunctionLogTool.flush();
                }
                return Pair.pair(rule, calcResult);
            }).collect(Collectors.toList());
            writeCacheResult(execDto.getResultCacheId(), cacheKey, ruleToValues, false);
            if (taskDefDetail.isOutput()) {
                val outputKeys = taskDefDetail.getKeyMappings().stream().map(it -> {
                    Object value;
                    if(it.getInput().startsWith("envContext-")){
                        value = newContext.get("$.env." + it.getInput().replace("envContext-", ""));
                    }else{
                        value = newContext.get("$.source." + it.getInput());
                    }
                    return new KeyValue(it.getOutput(), value);
                }).collect(Collectors.toList());
                writeModelResult(taskDefDetail.getOutputIdentifier(), outputKeys, ruleToValues);
            }
        }
    }

    public static void main(String[] args) {
        Map<String, List<String>> reference = FastjsonUtil.convertObject("{\"cbdjddhahdebgbji\":[],\"cbdjddhahdddechi\":[],\"cbdjddhahdcfcdfi\":[],\"cbdjddhahdbhigda\":[],\"cbdjddhahdaiifbi\":[],\"cbdjddhahcjjacbe\":[],\"cbdjddhahdhjdada\":[\"cbdjddhahdebgbji\",\"cbdjddhahdgeffhe\",\"cbdjddhahdeijjcg\",\"cbdjddhahdfgdgfe\"],\"cbdjddhahebjeedi\":[\"cbdjddhahdhjdada\",\"cbdjddhahebbcfbi\",\"cbdjddhahdjfgiha\"],\"cbdjddhahdihejfa\":[],\"cbdjddhahebbcfbi\":[\"cbdjddhaheadafji\",\"cbdjddhahdhbjdac\"],\"cbdjddhaheadafji\":[],\"cbdjddhahdjfgiha\":[\"cbdjddhahdihejfa\"],\"cbdjddhahdhbjdac\":[],\"cbdjddhahdgeffhe\":[],\"cbdjddhahdeijjcg\":[],\"cbdjddhahdfgdgfe\":[]}", Map.class);
        val ruleList = Lists.list(reference.keySet());
        List<String> sortedBids = Lists.list();
        int total = ruleList.size();
        int cursor = 0;
        int failed = 0;
        while(true){
            val item = ruleList.get(cursor);
            val reliedOn = reference.get(item);
            if(sortedBids.containsAll(reliedOn) || reliedOn.isEmpty()){
                cursor++;
                sortedBids.add(item);
                failed = 0;
            }else{
                ruleList.remove(cursor);
                ruleList.add(item);
                failed ++;
                if(cursor + failed >= total){
                    throw new ServerException("奖金项循环依赖");
                }
            }
            if(cursor >= total){
                break;
            }
        }
        System.out.println("----------");


    }

    public void writeCacheResult(String cacheId, String key, List<Pair<TaskDef.Rule, Object>> ruleToValues, boolean group) {
        val cache = igniteUtil.getContext(cacheId);
        List<IgniteCacheValue> values = Lists.list();
        ruleToValues.forEach(ruleToValue -> {
            val rule = ruleToValue.getKey();
            val cacheValue = new IgniteCacheValue();
            switch (rule.getRuleDataType()) {
                case Timestamp:
                case Int:
                case Number:
                    cacheValue.setType(BigDecimal.class);
                    cacheValue.setValue(formatRuleCalcResult(rule.getRuleDataType(), ruleToValue.getValue(), ValueFormat.CACHE));
                    break;
                case String:
                    cacheValue.setType(String.class);
                    cacheValue.setValue(formatRuleCalcResult(rule.getRuleDataType(), ruleToValue.getValue(), ValueFormat.CACHE));
                    break;
            }
            cacheValue.setProperty(rule.getRuleId());
            values.add(cacheValue);
        });
        if(group){
            val cacheValue = new IgniteCacheValue();
            cacheValue.setValue(key);
            cacheValue.setProperty("key");
            cacheValue.setType(String.class);
            values.add(cacheValue);
        }
        if(group){
            cache.putValue(key+"-"+values.get(0).getProperty(), values);
        }else{
            cache.putValue(key, values);
        }
    }

    public void writeModelResult(String identifier, List<KeyValue> keys, List<Pair<TaskDef.Rule, Object>> ruleToValues) {
        val filter = new MultiDataFilter(FilterOperator.AND, keys.stream().map(it -> DataFilter.eq(it.getText(), it.getValue() == null ? null :
                it.getValue().toString())).collect(Collectors.toList()).toArray(new SimpleDataFilter[keys.size()]));
        val dataList = DataQuery.identifier(identifier).filter(filter, DataSimple.class).getItems();
        if (dataList.isEmpty()) {
            val data = new DataSimple();
            val properties = data.getProperties();
            keys.forEach(key -> {
                properties.add(key.getText(), new SimplePropertyValue(key.getValue() == null ? null : key.getValue().toString()));
            });
            ruleToValues.forEach(ruleToValue -> {
                if (StringUtils.isNotEmpty(ruleToValue.first().getOutput())) {
                    val value = formatRuleCalcResult(ruleToValue.first().getRuleDataType(), ruleToValue.getValue(), ValueFormat.DB);
                    properties.add(ruleToValue.first().getOutput(), new SimplePropertyValue((String)value));
                }
            });
            data.setCreateTime(System.currentTimeMillis());
            data.setUpdateTime(data.getCreateTime());
            data.setDataStartTime(DateUtil.getMidnightTimestamp());
            DataInsert.identifier(identifier).insert(data);
        } else {
            dataList.forEach(it -> {
                val properties = it.getProperties();
                ruleToValues.forEach(ruleToValue -> {
                    if (StringUtils.isNotEmpty(ruleToValue.first().getOutput())) {
                        properties.remove(ruleToValue.first().getOutput());
                        val value = formatRuleCalcResult(ruleToValue.first().getRuleDataType(), ruleToValue.getValue(), ValueFormat.DB);
                        properties.add(ruleToValue.first().getOutput(), new SimplePropertyValue((String)value));
                    }
                });
                it.setUpdateTime(System.currentTimeMillis());
                DataUpdate.identifier(identifier).update(it);
            });
        }
    }

    private Object formatRuleCalcResult(TaskDef.RuleDataType type, Object value, ValueFormat format){
        switch (type) {
            case Timestamp:
                if(null == value){
                    switch (format){
                        case DB:
                            return "0";
                        case CACHE:
                            return new BigDecimal(0);
                        case CONTEXT:
                            return LocalDateTime.ofInstant(Instant.ofEpochSecond(0), ZoneId.systemDefault());
                    }
                }else{
                    if(value instanceof LocalDateTime){
                        val timestamp = ((LocalDateTime)value).toEpochSecond(OffsetDateTime.now().getOffset()) * 1000;
                        switch (format){
                            case DB:
                                return String.valueOf(timestamp);
                            case CACHE:
                                return new BigDecimal(timestamp);
                            case CONTEXT:
                                return value;
                        }
                    }else{
                        switch (format){
                            case DB:
                                return value.toString();
                            case CACHE:
                                return new BigDecimal(value.toString());
                            case CONTEXT:
                                return LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.valueOf(value.toString())/1000), ZoneId.systemDefault());
                        }
                    }
                }
            case Int:
            case Number:
                if(null == value){
                    switch (format){
                        case DB: return "0";
                        case CACHE:
                        case CONTEXT:
                            return new BigDecimal(0);
                    }
                }else{
                    switch (format){
                        case DB: return new BigDecimal(value.toString()).stripTrailingZeros().toPlainString();
                        case CACHE:
                        case CONTEXT:
                            return new BigDecimal(value.toString());
                    }
                }
            case String:
            default:
                if(null == value){
                    return "";
                }else{
                    return value.toString();
                }

        }
    }

    private static void fillInEnvironmentContext(MetadataVo metadata, List<KeyValueDto> info, Map<String, Object> context, String contextPrefix, List<TaskDef.Rule> ruleList) {
        for (KeyValueDto infoProperty : info) {
            val property = (String) infoProperty.getKey();
            val value = infoProperty.getValue();
            val actualProperty = property.contains("$") ? StringUtils.substringBefore(property, "$") : property;
            PropertyDataType propertyDataType;
            if(null == metadata){
                propertyDataType = ruleList.stream().filter(it->it.getRuleId().equals(actualProperty))
                        .map(it->it.getRuleDataType()).map(it->{
                            switch (it){
                                case String: return PropertyDataType.String;
                                case Int: return PropertyDataType.Integer;
                                case Timestamp: return PropertyDataType.Timestamp;
                                case Number: return PropertyDataType.Number;
                                default: return null;
                            }
                }).findFirst().get();
            }else{
                val allProperties = Sequences.sequence(metadata.getInheritedStandardProperties())
                        .join(metadata.fetchAllProperties());
                val propertyMetadata = allProperties.filter(it -> it.getProperty().equals(actualProperty)).first();
                propertyDataType = propertyMetadata.getDataType();
            }

            switch (propertyDataType) {
                case String:
                case Enum: {
                    context.put(contextPrefix + property, infoProperty.getValue() == null ? "" : value);
                    break;
                }
                case Number:
                case Integer:{
                    context.put(contextPrefix + property, infoProperty.getValue() == null ? 0 : value);
                    break;
                }
                case Timestamp: {
                    val notNullValue = infoProperty.getValue() == null ? 0 : value;
                    context.put(contextPrefix + property, LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.valueOf(notNullValue.toString())/1000), ZoneId.systemDefault()));
                    break;
                }
                case Boolean: {
                    context.put(contextPrefix + property, infoProperty.getValue() == null ? false : value);
                    break;
                }
                case Dict: {
                    context.put(contextPrefix + property, infoProperty.getValue() == null ? "" : value);
                    if (property.endsWith("dict$value")) {
                        val dict = DictSimple.doDictSimple(infoProperty.getValue() == null ? "" : value.toString());
                        context.put(contextPrefix + actualProperty, dict.getText());
                    }
                    break;
                }
                case Emp: {
                    context.put(contextPrefix + infoProperty.getKey(), infoProperty.getValue() == null ? "" : value);
                    if (property.endsWith("empId")) {
                        context.put(contextPrefix + actualProperty, infoProperty.getValue() == null ? "" : value);
                    }
                    break;
                }
            }
        }
    }
}

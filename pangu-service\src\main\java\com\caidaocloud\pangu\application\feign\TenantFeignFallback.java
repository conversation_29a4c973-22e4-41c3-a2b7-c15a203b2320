package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataDto;
import com.caidaocloud.pangu.application.dto.tenant.Tenant;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;


@Component
public class TenantFeignFallback implements TenantFeign {
    @Override
    public Result<List<Tenant>> tenantList() {
        return null;
    }
}

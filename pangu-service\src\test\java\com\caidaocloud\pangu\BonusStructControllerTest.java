package com.caidaocloud.pangu;

import com.caidaocloud.pangu.application.dto.BonusSchemaDto;
import com.caidaocloud.pangu.application.dto.BonusStructReportSettingDto;
import com.caidaocloud.pangu.application.service.BonusStructService;
import com.caidaocloud.pangu.application.dto.BonusStructDto;
import com.caidaocloud.pangu.application.dto.BonusStructQueryDto;
import com.caidaocloud.pangu.application.dto.BonusStructItemDto;
import com.caidaocloud.pangu.application.dto.BonusStructItemQueryDto;
import com.caidaocloud.pangu.application.dto.BonusStructDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusItemDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusStructComposeDto;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.interfaces.BonusSchemaController;
import com.caidaocloud.pangu.interfaces.BonusStructController;
import com.caidaocloud.pangu.interfaces.vo.BonusStructComposeVo;
import com.caidaocloud.pangu.interfaces.vo.BonusStructVo;
import com.caidaocloud.pangu.interfaces.vo.BonusStructItemVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguApplication.class)
public class BonusStructControllerTest {

    private MockMvc mockMvc;

    @SpyBean
    private BonusStructService bonusStructService;

    @SpyBean
    private BonusStructController bonusStructController;
    @SpyBean
    private BonusSchemaController bonusSchemaController;

    private final String structId = "2118088356829184";
    private final String itemId = "2118108830840832";
    @Before
    public void setup() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        mockMvc = MockMvcBuilders.standaloneSetup(bonusStructController,bonusSchemaController).build();
    }

    @Test
    public void load_ValidBid_ReturnsBonusStructVo() throws Exception {
        String bid = "2118088356829184";
        BonusStructVo bonusStructVo = new BonusStructVo();
        bonusStructVo.setBid(bid);
        bonusStructVo.setName("Updated Bonus Structure");


        mockMvc.perform(get("/api/pangu/v1/bonus/struct/one")
                        .param("bid", bid)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.bid").value(bid))
                .andExpect(jsonPath("$.data.name").value(bonusStructVo.getName()));
    }

    @Test
    public void load_InvalidBid_ReturnsNull() throws Exception {
        String bid = "invalidBid";

        when(bonusStructService.load(bid)).thenReturn(null);

        mockMvc.perform(get("/api/pangu/v1/bonus/struct/one")
                        .param("bid", bid)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").doesNotExist());
    }

    @Test
    public void load_ServiceThrowsException_ReturnsError() throws Exception {
        String bid = "errorBid";

        when(bonusStructService.load(bid)).thenThrow(new RuntimeException("Service error"));

        mockMvc.perform(get("/api/pangu/v1/bonus/struct/one")
                        .param("bid", bid)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    public void page_ValidQuery_ReturnsPageResult() throws Exception {
        BonusStructQueryDto queryDto = new BonusStructQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(10);


        // when(bonusStructService.page(queryDto)).thenReturn(pageResult);

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(queryDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.items[0].bid").exists())
                .andExpect(jsonPath("$.data.items[0].bid").value("2118088356829184"));
    }

    @Test
    public void create_ValidStruct_ReturnsSuccess() throws Exception {
        BonusStructDto struct = new BonusStructDto();
        struct.setI18nName(Maps.map("default","New Bonus Structure"));

        // when(bonusStructService.create(struct)).thenReturn("createdBid");

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(struct))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                // .andExpect(jsonPath("$.data").value("createdBid"));
    }

    @Test
    public void update_ValidStruct_ReturnsSuccess() throws Exception {
        BonusStructDto struct = new BonusStructDto();
        struct.setBid("2118088356829184");
        struct.setI18nName(Maps.map("default","Updated Bonus Structure"));

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(struct))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void delete_ValidBid_ReturnsSuccess() throws Exception {
        BonusStructDeleteDto deleteDto = new BonusStructDeleteDto();
        deleteDto.setBid("2118088356829184");

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"bid\":\"2118088356829184\"}")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void pageItems_ValidQuery_ReturnsPageResult() throws Exception {
        BonusStructItemQueryDto queryDto = new BonusStructItemQueryDto();
        queryDto.setStructId(structId);
        queryDto.setPageNo(1);
        queryDto.setPageSize(10);

        PageResult<BonusStructItemVo> pageResult = new PageResult<>();
        //
        // when(bonusStructService.pageItems(queryDto)).thenReturn(pageResult);

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/item/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(queryDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.items[0].bid").value(itemId));

    }

    @Test
    public void addItem_ValidItem_ReturnsSuccess() throws Exception {
        BonusStructItemDto item = new BonusStructItemDto();
        item.setStructId(structId);
        item.setName("New Bonus Item");
        // item.setCode("item code");
item.setType("519");
item.setValueIdentifier("entity.hr.EmpWorkInfo");
item.setValueProperty("empId");
item.setWorknoProperty("workno");
item.setMonthProperty("leaveDate");
        // item = FastjsonUtil.toObject("{\n"
        //         + "    \"name\": \"我是新的奖金项\",\n"
        //         + "    \"valueIdentifier\": \"entity.hr.EmpRuleSet\",\n"
        //         + "    \"valueProperty\": \"worknoStartValue\",\n"
        //         + "    \"worknoProperty\": \"worknoAutoCreate\",\n"
        //         + "    \"monthProperty\": \"worknoLength\",\n"
        //         + "    \"remark\": \"12341231\",\n"
        //         + "    \"type\": \"519\",\n"
        //         + "    \"structBid\": \"2118225408456704\"\n"
        //         + "} ", BonusStructItemDto.class);

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/item/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(item))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));

    }

    @Test
    public void updateItem_ValidItem_ReturnsSuccess() throws Exception {
        BonusStructItemDto item = new BonusStructItemDto();
        item.setBid("2118108830840832");
        item.setStructId(structId);
        item.setName("Update Bonus Item");
        // item.setCode("Update item code");
        item.setType("519");
        item.setValueIdentifier("entity.hr.EmpWorkInfo");
        item.setValueProperty("empId");
        item.setWorknoProperty("workno");
        item.setMonthProperty("leaveDate");

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/item/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(item))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));

    }

    @Test
    public void deleteItem_ValidItem_ReturnsSuccess() throws Exception {
        BonusItemDeleteDto deleteDto = new BonusItemDeleteDto();
        deleteDto.setStructBid(structId);
        deleteDto.setItemBid("2118108830840832");

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/item/delete")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(deleteDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void loadItem_ValidItemBid_ReturnsBonusStructItemVo() throws Exception {
        String itemBid = itemId;
        BonusStructItemVo itemVo = new BonusStructItemVo();
        itemVo.setStructId(structId);
        itemVo.setName("Update Bonus Item");


        mockMvc.perform(get("/api/pangu/v1/bonus/struct/item/one")
                        .param("itemBid", itemBid)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.bid").value(itemBid))
                .andExpect(jsonPath("$.data.structId").value(structId))
                .andExpect(jsonPath("$.data.name").value(itemVo.getName()));
    }

    @Test
    public void reportSetting_ValidStructId_ReturnsBonusReportSettingVo() throws Exception {
        String structId = "2139473605933056";

        mockMvc.perform(get("/api/pangu/v1/bonus/struct/report/setting")
                        .param("structId", structId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                // .andExpect(jsonPath("$.data.structId").value(structId))
                // .andExpect(jsonPath("$.data.name").value("Test Report Setting"));
    }

    @Test
    public void reportSettingAll_ValidStructId_ReturnsBonusReportSettingAllVo() throws Exception {
        String structId = "validStructId";


        mockMvc.perform(get("/api/pangu/v1/bonus/struct/report/setting/all")
                        .param("structId", structId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.structId").value(structId))
                .andExpect(jsonPath("$.data.options").isArray())
                .andExpect(jsonPath("$.data.options[0]").value("Option1"))
                .andExpect(jsonPath("$.data.options[1]").value("Option2"));
    }

    // @Test
    // public void saveReportSetting_ValidDto_ReturnsSuccess() throws Exception {
    //     BonusStructReportSettingDto dto = new BonusStructReportSettingDto();
    //     dto.setStructId("validStructId");
    //     // dto.setName("New Report Setting");
    //
    //     mockMvc.perform(post("/api/pangu/v1/bonus/struct/report/setting")
    //                     .contentType(MediaType.APPLICATION_JSON)
    //                     .content("{\"structId\":\"validStructId\",\"name\":\"New Report Setting\"}")
    //                     .accept(MediaType.APPLICATION_JSON))
    //             .andExpect(status().isOk())
    //             .andExpect(jsonPath("$.data").isEmpty());
    // }

    // @Test
    // public void saveReportSettingOrder_ValidDto_ReturnsSuccess() throws Exception {
    //     BonusStructReportSettingDto dto = new BonusStructReportSettingDto();
    //     dto.setStructId("validStructId");
    //     dto.setName("New Report Setting Order");
    //
    //     mockMvc.perform(post("/api/pangu/v1/bonus/struct/report/setting/order")
    //                     .contentType(MediaType.APPLICATION_JSON)
    //                     .content("{\"structId\":\"validStructId\",\"name\":\"New Report Setting Order\"}")
    //                     .accept(MediaType.APPLICATION_JSON))
    //             .andExpect(status().isOk())
    //             .andExpect(jsonPath("$.data").isEmpty());
    // }

    @Test
    public void modifyExecutionType_ValidStructId_ReturnsSuccess() throws Exception {
        String structId = "validStructId";
        ExecutionType executionType = ExecutionType.SINGLE;

        mockMvc.perform(put("/api/pangu/v1/bonus/struct/compose")
                        .param("structId", structId)
                        .param("executionType", executionType.name())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void pageComposeItems_ValidStructId_ReturnsBonusStructComposeVoList() throws Exception {
        when(bonusStructService.listComposeItems(structId)).thenAnswer((a) -> {
            Object res = a.callRealMethod();
            System.out.println(FastjsonUtil.toJson(res));
            return res;
        });
       mockMvc.perform(get("/api/pangu/v1/bonus/struct/compose/item")
                        .param("structId", this.structId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();

        // .andExpect(jsonPath("$.data[0].structId").value(structId))
    }

    @Test
    public void saveComposeItem_ValidDto_ReturnsSuccess() throws Exception {
        BonusStructComposeDto dto = new BonusStructComposeDto();
        dto.setStructId(structId);
        dto.setComposeId("123");

        mockMvc.perform(post("/api/pangu/v1/bonus/struct/compose/item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void updateComposeItem_ValidDto_ReturnsSuccess() throws Exception {
        BonusStructComposeDto dto = new BonusStructComposeDto();
        dto.setStructId("validStructId");
        dto.setComposeId("Updated Compose Item");

        mockMvc.perform(put("/api/pangu/v1/bonus/struct/compose/item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"structId\":\"validStructId\",\"name\":\"Updated Compose Item\"}")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void deleteComposeItem_ValidComposeId_ReturnsSuccess() throws Exception {
        String composeId = "2118819389995008";

        mockMvc.perform(delete("/api/pangu/v1/bonus/struct/compose/item")
                        .param("composeItemId", composeId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void addSchema() throws Exception {
        String json = "{\"name\":\"我是谁\",\"bonusStructBid\":\"2118088356829184\",\"coverage\":{\"id\":\"1739609764890_54\",\"type\":\"group\",\"relation\":\"and\",\"children\":[{\"id\":\"1739609772078_734\",\"type\":\"single\",\"relation\":null,\"children\":null,\"condition\":{\"name\":\"entity.hr.EmpWorkInfo#siling_calc\",\"symbol\":\"GT\",\"value\":\"0\",\"simpleValue\":\"0\",\"componentType\":\"STRING_INPUT\"}}],\"identifierEmpIdKey\":{}},\"month\":1,\"day\":1,\"remark\":null,\"editId\":\"2125359*********\",\"approveConfig\":{\"enabled\":true,\"formId\":\"1950450879723523\",\"item\":[\"2118268884760576\"]},\"bid\":\"2125359*********\"}";

        mockMvc.perform(post("/api/pangu/v1/bonus/schema/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void loadSchema() throws Exception {
        // String json = "{\"name\":\"我是谁\",\"bonusStructBid\":\"2118088356829184\",\"coverage\":{\"id\":\"1739609764890_54\",\"type\":\"group\",\"relation\":\"and\",\"children\":[{\"id\":\"1739609772078_734\",\"type\":\"single\",\"relation\":null,\"children\":null,\"condition\":{\"name\":\"entity.hr.EmpWorkInfo#siling_calc\",\"symbol\":\"GT\",\"value\":\"0\",\"simpleValue\":\"0\",\"componentType\":\"STRING_INPUT\"}}],\"identifierEmpIdKey\":{}},\"month\":1,\"day\":1,\"remark\":null,\"editId\":\"2125359*********\",\"approveConfig\":{\"enabled\":true,\"formId\":\"1950450879723523\",\"item\":[\"2118268884760576\"]},\"bid\":\"2125359*********\"}";

        mockMvc.perform(get("/api/pangu/v1/bonus/schema/one")
                        .param("bid", "2125359*********")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.approveConfig.item").exists())
                .andExpect(jsonPath("$.data.approveConfig.item[0]").value("2118268884760576"));
    }

    @Test
    public void test(){
        String json = "{\"basic\":[\"entity.hr.EmpWorkInfo@companyTxt\",\"entity.hr.EmpWorkInfo@postTxt\",\"entity.hr.EmpWorkInfo@name\",\"entity.hr.EmpWorkInfo@workno\",\"entity.hr.EmpWorkInfo@organizeTxt\",\"entity.hr.EmpWorkInfo@empType\",\"entity.hr.EmpWorkInfo@hireDate\",\"entity.hr.EmpWorkInfo@confirmationDate\",\"entity.hr.EmpWorkInfo@leaveDate\"],\"item\":[\"2143096944760832\"],\"structId\":\"2139473605933056\"}";
        BonusStructReportSettingDto dto = FastjsonUtil.toObject(json, BonusStructReportSettingDto.class);
        bonusStructService.saveReportSetting(dto);
    }

    @Test
    public  void sync(){

    }
}

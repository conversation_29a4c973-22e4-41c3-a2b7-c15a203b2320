package com.caidaocloud.pangu.manager.feign;

import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.compute.remote.framework.actor.RemoteAddress;
import com.caidaocloud.compute.remote.framework.actor.RemoteRequest;
import com.caidaocloud.pangu.core.dto.NodeExec;

@RemoteRequest(value = "arrangementClient")
public interface ArrangementClient {

    @RemoteRequest
    boolean startNode(@RemoteAddress Address address, NodeExec execDto);

    @RemoteRequest
    boolean dispatchSubTask(@RemoteAddress Address address, NodeExec.SubNodeExec execDto);
}

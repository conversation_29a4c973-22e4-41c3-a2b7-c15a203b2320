package com.caidaocloud.pangu.interfaces;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.application.dto.*;
import com.caidaocloud.pangu.application.service.BonusStructService;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.infrastructure.util.LangUtil;
import com.caidaocloud.pangu.interfaces.vo.*;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/pangu/v1/bonus/struct")
@Api(value ="/api/pangu/v1/bonus/struct", tags = "奖金结构")
public class BonusStructController {

    @Autowired
    private BonusStructService bonusStructService;

    @GetMapping("/one")
    @ApiOperation("奖金结构查看")
    public Result<BonusStructVo> load(@RequestParam String bid){
        BonusStructVo bonusStructVo = bonusStructService.load(bid);
        bonusStructVo.setName(LangUtil.getCurrentLangVal(bonusStructVo.getI18nName()));
        return Result.ok(bonusStructVo);
    }

    @PostMapping("/page")
    @ApiOperation("奖金结构列表")
    public Result<PageResult<BonusStructVo>> page(@RequestBody BonusStructQueryDto queryDto){
        PageResult<BonusStructVo> result = bonusStructService.page(queryDto);
        for (BonusStructVo item : result.getItems()) {
            item.setName(LangUtil.getCurrentLangVal(item.getI18nName()));
        }
        return Result.ok(result);
    }
    @PostMapping("/create")
    @ApiOperation("新增奖金结构")
    public Result<String> create(@RequestBody BonusStructDto struct){
        return Result.ok(bonusStructService.create(struct));
    }

    @PostMapping("/update")
    @ApiOperation("更新奖金结构")
    public Result<Boolean> update(@RequestBody BonusStructDto struct){
        bonusStructService.update(struct);
        return Result.ok();
    }

    @PostMapping("/copy")
    @ApiOperation("复制奖金结构")
    public Result<String> copy(@RequestBody BonusStructDto struct){
        return Result.ok(bonusStructService.copy(struct));
    }

    @PostMapping("/delete")
    @ApiOperation("删除奖金结构")
    public Result<Boolean> delete(@RequestBody BonusStructDeleteDto delete){
        bonusStructService.delete(delete.getBid());
        return Result.ok();
    }

    @GetMapping("list")
    @ApiOperation("奖金结构下拉列表")
    public Result<List<BonusStructVo>> list(){
        List<BonusStructVo> list = bonusStructService.list();
        for (BonusStructVo item : list) {
            item.setName(LangUtil.getCurrentLangVal(item.getI18nName()));
        }
        return Result.ok(list);
    }

    @PostMapping("/item/page")
    @ApiOperation("奖金项列表")
    public Result<PageResult<BonusStructItemVo>> pageItems(@RequestBody  BonusStructItemQueryDto dto) {
        val result = bonusStructService.pageItems(dto);
        return Result.ok(result);
    }

   @PostMapping("/item/create")
   @ApiOperation("新增奖金项")
   public Result<Boolean> addItem(@Valid @RequestBody BonusStructItemDto item) {
       item.setName(item.getName().trim());
       bonusStructService.addItem(item);
       return Result.ok();
   }
   
   @PostMapping("/item/update")
   @ApiOperation("更新奖金项")
   public Result<Boolean> updateItem(@Valid @RequestBody BonusStructItemDto item) {
       item.setName(item.getName().trim());
       bonusStructService.updateItem(item);
       return Result.ok();
   }

    @PostMapping("/item/delete")
    @ApiOperation("删除奖金项")
    public Result<Boolean> deleteItem(@RequestBody BonusItemDeleteDto delete){
        bonusStructService.deleteItem(delete.getStructBid(), delete.getItemBid());
        return Result.ok();
    }

    @GetMapping("/item/one")
    @ApiOperation("奖金项详情")
    public Result<BonusStructItemVo> loadItem(@RequestParam String itemBid){
        val result = bonusStructService.loadItem(itemBid);
        return Result.ok(result);
    }

    @GetMapping("/report/setting")
    @ApiOperation("报表设置查询")
    public Result<BonusReportSettingVo> reportSetting(@RequestParam String structId){
        return Result.ok(bonusStructService.loadReportSetting(structId));
    }

    @GetMapping("/report/setting/all")
    @ApiOperation("报表设置可选项查询")
    public Result<BonusReportSettingAllVo> reportSettingAll(@RequestParam String structId){
        return Result.ok(bonusStructService.loadALlReportSetting(structId));
    }

    @PostMapping("/report/setting")
    @ApiOperation("报表设置保存")
    public Result saveReportSetting(@RequestBody BonusStructReportSettingDto dto){
        bonusStructService.saveReportSetting(dto);
        return Result.ok();
    }
    @PostMapping("/report/setting/order")
    @ApiOperation("报表设置保存")
    public Result saveReportSettingOrder(@RequestBody BonusStructReportSettingDto dto){
        bonusStructService.saveReportSettingOrder(dto);
        return Result.ok();
    }

     //
    // @GetMapping("/function/list")
    // @ApiOperation("奖金计算函数列表")
    // public Result<Map> function(@RequestParam String structId){
    //     return Result.ok(service.functionList(structId));
    // }

    @PutMapping("compose")
    @ApiOperation("修改编排执行方式")
    public Result modifyExecutionType(@RequestParam String structId, @RequestParam ExecutionType executionType) {
        bonusStructService.modifyExecutionType(structId, executionType);
        return Result.ok();
    }


    @GetMapping("/compose/item")
    @ApiOperation("奖金编排列表")
    public Result<List<BonusStructComposeVo>> pageComposeItems(@RequestParam String structId) {
        val result = bonusStructService.listComposeItems(structId);
        return Result.ok(result);
    }

    @PostMapping("/compose/item")
    @ApiOperation("新增奖金编排")
    public Result saveComposeItem(
         @RequestBody   BonusStructComposeDto dto) {
        bonusStructService.saveComposeItem(dto);
        return Result.ok();
    }


    @PostMapping("/compose/item/order")
    @ApiOperation("新增奖金编排")
    public Result saveComposeItemOrder(
            @RequestBody   BonusStructComposeOrderDto dto) {
        bonusStructService.saveComposeItemOrder(dto);
        return Result.ok();
    }

    @PutMapping("/compose/item")
    @ApiOperation("更新奖金编排")
    public Result updateComposeItem(@RequestBody BonusStructComposeDto dto) {
        bonusStructService.updateComposeItem(dto);
        return Result.ok();
    }

    @DeleteMapping("/compose/item")
    @ApiOperation("删除奖金编排")
    public Result deleteComposeItem(@RequestParam String composeItemId) {
        bonusStructService.deleteComposeItem(composeItemId);
        return Result.ok();
    }
}

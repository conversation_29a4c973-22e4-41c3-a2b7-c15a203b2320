package com.caidaocloud.pangu.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.enums.BonusDataType;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

@Data
public class BonusItemDto {
    private String bid;
    private String structBid;
    private String name;
    private BonusDataType bonusDataType;
    private String categoryDictId;
    private String remark;
    private boolean showOnStatistics;
    private String expression;
    private String expressionFrontend;

}

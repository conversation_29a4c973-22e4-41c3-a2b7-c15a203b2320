package com.caidaocloud.pangu.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.pangu.domain.entity.BonusStructReportItem;
import com.caidaocloud.pangu.domain.repository.IBonusStructReportItemRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepositoryImpl;
import com.googlecode.totallylazy.Sequences;
import org.springframework.stereotype.Repository;

import javax.swing.text.TabExpander;
import javax.xml.crypto.Data;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
@Repository
public class BonusStructReportItemRepositoryImpl extends BaseRepositoryImpl<BonusStructReportItem> implements IBonusStructReportItemRepository {
    @Override
    public void saveList(String structId, List<BonusStructReportItem> items) {
        List<BonusStructReportItem> exist = selectList(structId);
        for (BonusStructReportItem item : exist) {
            item.setDeleted(true);
        }
        Map<BonusStructReportItem, BonusStructReportItem> existMap = exist.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
        for (BonusStructReportItem item : items) {
            existMap.merge(item, item, (oldValue, newValue) -> {
                oldValue.setSort(newValue.getSort());
                oldValue.setDeleted(false);
                return oldValue;
            });
        }
        for (Map.Entry<BonusStructReportItem, BonusStructReportItem> entry : existMap.entrySet()) {
            saveOrUpdate(entry.getValue());
        }
    }

    private void saveOrUpdate(BonusStructReportItem item) {
        if (item.getBid() == null) {
            DataInsert.identifier(BonusStructReportItem.BONUS_STRUCT_ITEM_IDENTIFIER).insert(item);
        } else {
            DataUpdate.identifier(BonusStructReportItem.BONUS_STRUCT_ITEM_IDENTIFIER).update(item);
        }
    }

    @Override
    public List<BonusStructReportItem> selectList(String structId) {
        return DataQuery.identifier(BonusStructReportItem.BONUS_STRUCT_ITEM_IDENTIFIER).limit(-1, 1)
                .filter(DataFilter.eq("structId", structId).andNe("deleted",Boolean.TRUE.toString()), BonusStructReportItem.class, "sort,bid", System.currentTimeMillis()).getItems();
    }


}

package com.caidaocloud.pangu.manager.service;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.manager.dto.ArrangementResultDto;
import com.caidaocloud.pangu.manager.model.ArrangementExec;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ResultNoticeService {

    @Autowired
    private MqMessageProducer mqMessageProducer;


    public void success(ArrangementExec exec){
        if(ArrangementType.BONUS.equals(exec.getType())){
            log.info("arrangement success " + FastjsonUtil.toJson(exec));
            RabbitBaseMessage message = new RabbitBaseMessage();
            message.setExchange("exchange.compose.fac.direct.exchange");
            message.setRoutingKey(exec.getType().toString().toLowerCase()+".compose.queue");
            message.setBody(FastjsonUtil.toJson(new ArrangementResultDto(exec.getActualTenantId(), exec.getExecSeqId(),
                    ArrangementExec.Status.SUCCESS)));
            mqMessageProducer.publish(message);
        }
    }

    public void failed(ArrangementExec exec){
        if(ArrangementType.BONUS.equals(exec.getType())){
            log.info("arrangement failed " + FastjsonUtil.toJson(exec));
            RabbitBaseMessage message = new RabbitBaseMessage();
            message.setExchange("exchange.compose.fac.direct.exchange");
            message.setRoutingKey(exec.getType().toString().toLowerCase()+".compose.queue");
            message.setBody(FastjsonUtil.toJson(new ArrangementResultDto(exec.getActualTenantId(), exec.getExecSeqId(),
                    ArrangementExec.Status.ERROR)));
            mqMessageProducer.publish(message);
        }
    }


}

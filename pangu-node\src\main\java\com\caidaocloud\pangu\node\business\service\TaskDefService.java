package com.caidaocloud.pangu.node.business.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.dto.ExpAvailableParamDto;
import com.caidaocloud.pangu.node.business.dto.SourceTaskDefDto;
import com.caidaocloud.pangu.node.business.dto.TaskDefDto;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TaskDefService {

    @Autowired
    private MetadataOperatorService metadataService;

    @Autowired(required = false)
    private ArrangementService arrangementService;

    @PaasTransactional
    public String create(String arrangementId){
        val result = TaskDef.create(arrangementId);
        arrangementService.updateLastUpdate(arrangementId);
        return result;
    }

    @PaasTransactional
    public void update(TaskDefDto taskDefDto){
        val exist = TaskDef.loadDraft(taskDefDto.getTaskDefId());
        if(TaskDef.Status.PUBLISHED.equals(exist.getStatus())){
            val taskDef = taskDefDto.toEntity(exist, true);
            taskDef.createBasic();
            exist.update();
        }else{
            val taskDef = taskDefDto.toEntity(exist, false);
            taskDef.update();
        }
        arrangementService.updateLastUpdate(taskDefDto.getArrangementId());
    }

    @PaasTransactional
    public void updateDetail(TaskDefDto.Detail detailDto){
        val exist = TaskDef.Detail.loadDraft(detailDto.getTaskDefId());
        if(TaskDef.Status.PUBLISHED.equals(exist.getStatus())){
            val detail = detailDto.toEntity(exist, true);
            detail.create();
            exist.update();
        }else{
            val detail = detailDto.toEntity(exist, false);
            detail.update();
        }
        arrangementService.updateLastUpdate(exist.getArrangementId());
    }

    @PaasTransactional
    public String addRule(TaskDefDto.Rule ruleDto){
        val rule = ruleDto.toEntity();
        rule.validate();
        arrangementService.updateLastUpdate(TaskDef.loadDraft(rule.getTaskDefId()).getArrangementId());
        return rule.create();
    }

    @PaasTransactional
    public void updateRule(TaskDefDto.Rule ruleDto){
        val exist = TaskDef.Rule.loadDraft(ruleDto.getRuleId());
        if(TaskDef.Status.PUBLISHED.equals(exist.getStatus())){
            val rule = ruleDto.toEntity(exist, true);
            rule.validate();
            rule.create();
            exist.update();
        }else{
            val rule = ruleDto.toEntity(exist, false);
            rule.validate();
            rule.update();
        }
        arrangementService.updateLastUpdate(TaskDef.loadDraft(exist.getTaskDefId()).getArrangementId());
    }

    public void publishTaskDef(String arrangementId, String arrangementVid){
        val taskDefList = TaskDef.listDraft(arrangementId);
        val detailList = TaskDef.Detail.listDraft(arrangementId);
        Map<String, String> taskDefIdToVid = Maps.map();
        taskDefList.forEach(taskDef->{
            val taskDefVid = SnowUtil.nextId();
            taskDefIdToVid.put(taskDef.getTaskDefId(), taskDefVid);
            if(TaskDef.Status.DRAFT.equals(taskDef.getStatus())){
                taskDef.setStatus(TaskDef.Status.PUBLISHED);
                taskDef.setArrangementVid(arrangementVid);
                taskDef.setTaskDefVid(taskDefVid);
                taskDef.update();
            }else if(TaskDef.Status.PUBLISHED.equals(taskDef.getStatus())){
                taskDef.setStatus(TaskDef.Status.REPLACED);
                taskDef.update();
                taskDef.setBid(null);
                taskDef.setId(null);
                taskDef.setStatus(TaskDef.Status.PUBLISHED);
                taskDef.setTaskDefVid(taskDefVid);
                taskDef.setArrangementVid(arrangementVid);
                taskDef.createBasic();
            }else if(TaskDef.Status.RE_DRAFT.equals(taskDef.getStatus())){
                val taskPublished = TaskDef.loadPublished(taskDef.getTaskDefId());
                taskPublished.setStatus(TaskDef.Status.REPLACED);
                taskPublished.update();

                taskDef.setStatus(TaskDef.Status.PUBLISHED);
                taskDef.setArrangementVid(arrangementVid);
                taskDef.setTaskDefVid(taskDefVid);
                taskDef.update();
            }
        });


        detailList.forEach(detail->{
            val taskDefVid = taskDefIdToVid.get(detail.getTaskDefId());
            if(TaskDef.Status.DRAFT.equals(detail.getStatus())){
                detail.setStatus(TaskDef.Status.PUBLISHED);
                detail.setArrangementVid(arrangementVid);
                detail.setTaskDefVid(taskDefVid);
                detail.update();
            }else if(TaskDef.Status.PUBLISHED.equals(detail.getStatus())){
                detail.setStatus(TaskDef.Status.REPLACED);
                detail.update();
                detail.setBid(null);
                detail.setId(null);
                detail.setStatus(TaskDef.Status.PUBLISHED);
                detail.setTaskDefVid(taskDefVid);
                detail.setArrangementVid(arrangementVid);
                detail.create();
            }else if(TaskDef.Status.RE_DRAFT.equals(detail.getStatus())){
                val taskPublished = TaskDef.Detail.loadPublished(detail.getTaskDefId());
                taskPublished.setStatus(TaskDef.Status.REPLACED);
                taskPublished.update();

                detail.setStatus(TaskDef.Status.PUBLISHED);
                detail.setArrangementVid(arrangementVid);
                detail.setTaskDefVid(taskDefVid);
                detail.update();
            }
        });

        taskDefIdToVid.entrySet().forEach(it->{
            val taskDefId = it.getKey();
            val taskDefVid = it.getValue();
            publishRule(taskDefId, taskDefVid);
        });

    }

    private void publishRule(String taskDefId, String taskDefVid) {
        val ruleList = TaskDef.Rule.listDraft(taskDefId);
        ruleList.forEach(rule->{
            val ruleVid = SnowUtil.nextId();
            if(TaskDef.Status.DRAFT.equals(rule.getStatus())){
                rule.setStatus(TaskDef.Status.PUBLISHED);
                rule.setRuleVid(ruleVid);
                rule.setTaskDefVid(taskDefVid);
                rule.update();
            }else if(TaskDef.Status.PUBLISHED.equals(rule.getStatus())){
                rule.setStatus(TaskDef.Status.REPLACED);
                rule.update();
                rule.setBid(null);
                rule.setId(null);
                rule.setStatus(TaskDef.Status.PUBLISHED);
                rule.setTaskDefVid(taskDefVid);
                rule.create();
            }else if(TaskDef.Status.RE_DRAFT.equals(rule.getStatus())){
                val rulePublished = TaskDef.Rule.loadPublished(rule.getRuleId());
                rulePublished.setStatus(TaskDef.Status.REPLACED);
                rulePublished.update();

                rule.setStatus(TaskDef.Status.PUBLISHED);
                rule.setRuleVid(ruleVid);
                rule.setTaskDefVid(taskDefVid);
                rule.update();
            }
            rule.publishExp();
        });
    }

    public Map<String, String> copyList(String fromArrangementId, String copyArrangementId) {
        val taskDtoList = FastjsonUtil.convertList(TaskDef.listDraft(fromArrangementId), TaskDefDto.class);
        val detailDtoList = FastjsonUtil.convertList(TaskDef.Detail.listDraft(fromArrangementId), TaskDefDto.Detail.class);
        Map<String, String> taskDefIdFromToCopy = Maps.map();
        taskDtoList.forEach(it->{
            val fromTaskDefId = it.getTaskDefId();
            it.setTaskDefId(null);
            it.setArrangementId(copyArrangementId);
            val task = it.toEntity();
            val copyTaskDefId = task.getTaskDefId();
            task.createBasic();
            taskDefIdFromToCopy.put(fromTaskDefId, copyTaskDefId);
        });
        detailDtoList.forEach(it->{
            val fromTaskDefId = it.getTaskDefId();
            String copyTaskDefId = taskDefIdFromToCopy.get(fromTaskDefId);
            it.setTaskDefId(copyTaskDefId);
            val detail = it.toEntity();
            if(StringUtils.isNotEmpty(detail.getDsTaskDefId())){
                detail.setDsTaskDefId(taskDefIdFromToCopy.get(detail.getDsTaskDefId()));
            }
            detail.setArrangementId(copyArrangementId);
            detail.create();
            val ruleDtoList = FastjsonUtil.convertList(TaskDef.Rule.listDraft(fromTaskDefId), TaskDefDto.Rule.class);
            ruleDtoList.forEach(ruleDto->{
                ruleDto.setRuleId(null);
                ruleDto.setTaskDefId(copyTaskDefId);
                TaskDef.Rule rule = ruleDto.toEntity();
                rule.create();
            });
        });
        return taskDefIdFromToCopy;
    }

    public List<SourceTaskDefDto> listDraftSourceTask(String arrangementId) {
        return Arrangement.Detail.loadDraft(arrangementId).getNodes().stream().filter(node->StringUtils.isNotEmpty(node.getTaskDefId()))
                .map(node-> new SourceTaskDefDto(node.getTaskDefId(), node.getTitle())).collect(Collectors.toList());
    }

    public List<SourceTaskDefDto> listPublishedSourceTask(String arrangementId) {
        return Arrangement.Detail.loadDraft(arrangementId).getNodes().stream().filter(node->StringUtils.isNotEmpty(node.getTaskDefId()))
                .map(node-> new SourceTaskDefDto(node.getTaskDefId(), node.getTitle())).collect(Collectors.toList());
    }

    public List<TaskDef> listDraft(String arrangementId) {
        return TaskDef.listDraft(arrangementId);
    }

    public List<TaskDef> listPublished(String arrangementId) {
        return TaskDef.listPublished(arrangementId);
    }

    public TaskDef loadDraft(String taskDefId) {
        return TaskDef.loadDraft(taskDefId);
    }

    public TaskDef loadPublished(String taskDefId) {
        return TaskDef.loadPublished(taskDefId);
    }


    public TaskDef.Detail loadDetailPublished(String taskDefId) {
        return TaskDef.Detail.loadPublished(taskDefId);
    }

    public TaskDef.Detail loadDetailDraft(String taskDefId) {
        return TaskDef.Detail.loadDraft(taskDefId);
    }

    public PageResult<TaskDef.Rule> pageRuleDraft(String taskDefId, int pageSize, int pageNo) {
        return TaskDef.Rule.pageDraft(taskDefId, pageSize, pageNo);
    }

    public PageResult<TaskDef.Rule> pageRulePublished(String taskDefId, int pageSize, int pageNo) {
        return TaskDef.Rule.pagePublished(taskDefId, pageSize, pageNo);
    }

    public List<TaskDef.Detail> listNotSetTaskDefList(String arrangementId){
        return TaskDef.Detail.listDraft(arrangementId).stream().filter(it->
            null == it.getDsType()
        ).collect(Collectors.toList());
    }

    public void deleteByArrangement(String arrangementId) {
        TaskDef.listDraft(arrangementId).forEach(it->it.delete());
        TaskDef.Detail.listDraft(arrangementId).forEach(it-> {
            it.delete();
            TaskDef.Rule.listDraft(it.getTaskDefId()).forEach(rule->{
                rule.delete();
            });
        });

    }

    public void recover(String arrangementId) {
        val drafts = TaskDef.listDraft(arrangementId);
        drafts.forEach(draft->{
            if(TaskDef.Status.DRAFT.equals(draft.getStatus())){
                draft.delete();
            }else if(TaskDef.Status.PUBLISHED.equals(draft.getStatus())){
                //do nothing
            }else if(TaskDef.Status.RE_DRAFT.equals(draft.getStatus())){
                draft.delete();
                val published = TaskDef.loadPublished(arrangementId);
                published.setStatus(TaskDef.Status.PUBLISHED);
                published.update();
            }else{
                throw new ServerException();
            }
            val ruleDrafts = TaskDef.Rule.listDraft(draft.getTaskDefId());
            ruleDrafts.forEach(ruleDraft->{
                if(TaskDef.Status.DRAFT.equals(ruleDraft.getStatus())){
                    ruleDraft.delete();
                }else if(TaskDef.Status.PUBLISHED.equals(ruleDraft.getStatus())){
                    //do nothing
                }else if(TaskDef.Status.RE_DRAFT.equals(ruleDraft.getStatus())){
                    ruleDraft.delete();
                    val published = TaskDef.Rule.loadPublished(arrangementId);
                    published.setStatus(TaskDef.Status.PUBLISHED);
                    published.update();
                }else{
                    throw new ServerException();
                }
            });
        });

        val detailDrafts = TaskDef.Detail.listDraft(arrangementId);
        detailDrafts.forEach(draft->{
            if(TaskDef.Status.DRAFT.equals(draft.getStatus())){
                draft.delete();
            }else if(TaskDef.Status.PUBLISHED.equals(draft.getStatus())){
                //do nothing
            }else if(TaskDef.Status.RE_DRAFT.equals(draft.getStatus())){
                draft.delete();
                val published = TaskDef.Detail.loadPublished(draft.getTaskDefId());
                published.setStatus(TaskDef.Status.PUBLISHED);
                published.update();
            }else{
                throw new ServerException();
            }
        });
    }

    public Map<ExpAvailableParamDto.ParamType, List<ExpAvailableParamDto>> listExpAvailableParam(String taskDefId, TaskDef.Status status) {
        TaskDef.Detail taskDef;
        if(TaskDef.Status.PUBLISHED.equals(status)){
            taskDef = TaskDef.Detail.loadPublished(taskDefId);
        }else{
            taskDef = TaskDef.Detail.loadDraft(taskDefId);
        }
        val arrangementType = Arrangement.loadDraft(taskDef.getArrangementId()).getType();

        Arrangement.Detail arrangementDetail;
        if(TaskDef.Status.PUBLISHED.equals(status)){
            arrangementDetail = Arrangement.Detail.loadPublished(taskDef.getArrangementId());
        }else{
            arrangementDetail = Arrangement.Detail.loadDraft(taskDef.getArrangementId());
        }
        arrangementDetail.getNodes().stream().filter(node->node.getType().equals(Arrangement.NodeType.TASK))
                .map(node->node.getTaskDefId());
        List<TaskDef> taskDefList;
        if(TaskDef.Status.PUBLISHED.equals(status)){
            taskDefList = TaskDef.listPublished(taskDef.getArrangementId());
        }else{
            taskDefList = TaskDef.listDraft(taskDef.getArrangementId());
        }
        List<ExpAvailableParamDto> taskParams = Lists.list();
        taskDefList.forEach(it->{
            if(TaskDef.Status.PUBLISHED.equals(status)){
                taskParams.addAll(TaskDef.Rule.listPublished(it.getTaskDefId()).stream().map(rule->
                        ExpAvailableParamDto.instance(rule.getRuleId(), rule.getName())).collect(Collectors.toList()));
            }else{
                taskParams.addAll(TaskDef.Rule.listDraft(it.getTaskDefId()).stream().map(rule->
                        ExpAvailableParamDto.instance(rule.getRuleId(), rule.getName())).collect(Collectors.toList()));
            }

        });
        return Maps.map(
                ExpAvailableParamDto.ParamType.source,taskDef.getDsType() == TaskDef.SourceType.PAAS?metadataService.load(taskDef.getDsIdentifier())
                        .fetchAllProperties().stream().map(it->ExpAvailableParamDto.instance(it.getProperty(), it.getName()))
                        .collect(Collectors.toList()):Lists.list(),
                ExpAvailableParamDto.ParamType.env, Arrays.stream(EnvironmentContext.values()).filter(it->it.getType().equals(arrangementType))
                        .map(it->ExpAvailableParamDto.instance(""+it.getProperty(), it.getName()))
                        .collect(Collectors.toList()),
                ExpAvailableParamDto.ParamType.task, taskParams,
                ExpAvailableParamDto.ParamType.work, metadataService.load("entity.hr.EmpWorkInfo")
                        .fetchAllProperties().stream().map(it->ExpAvailableParamDto.instance(it.getProperty(), it.getName()))
                        .collect(Collectors.toList()),
                ExpAvailableParamDto.ParamType.privacy, metadataService.load("entity.hr.EmpPrivateInfo")
                        .fetchAllProperties().stream().map(it->ExpAvailableParamDto.instance(it.getProperty(),it.getName()))
                        .collect(Collectors.toList())
        );
    }

    @PaasTransactional
    public void deleteRule(String ruleId) {
        val rule = TaskDef.Rule.loadDraft(ruleId);
        rule.delete();
        arrangementService.updateLastUpdate(TaskDef.loadDraft(rule.getTaskDefId()).getArrangementId());
    }

    private List<KeyValue> contextAvailableInputKey(ArrangementType arrangementType){
        return Arrays.stream(EnvironmentContext.values()).filter(it->it.getType().equals(arrangementType))
                .map(it->new KeyValue("[业务变量]"+it.getName(), "envContext-"+it.getProperty())).collect(Collectors.toList());
    }

    public List<KeyValue> availableInputKeyPublished(String arrangementId, TaskDef.SourceType dsType, String source) {
        val arrangementType = Arrangement.loadPublished(arrangementId).getType();
        if(dsType.equals(TaskDef.SourceType.PAAS)){
            val metadata = metadataService.load(source);
            return Sequences.sequence(metadata.fetchAllProperties())
                    .map(it->new KeyValue(it.getName(), it.getProperty())).join(contextAvailableInputKey(arrangementType)).toList();
        }else{
            return Sequences.sequence(TaskDef.Rule.listPublished(source))
                    .map(it->new KeyValue(it.getName(), it.getRuleId())).join(contextAvailableInputKey(arrangementType)).toList();
        }
    }

    public List<KeyValue> availableInputKeyDraft(String arrangementId, TaskDef.SourceType dsType, String source) {
        val arrangementType = Arrangement.loadDraft(arrangementId).getType();
        if(dsType.equals(TaskDef.SourceType.PAAS)){
            val metadata = metadataService.load(source);
            return Sequences.sequence(metadata.fetchAllProperties())
                    .map(it->new KeyValue(it.getName(), it.getProperty())).join(contextAvailableInputKey(arrangementType)).toList();
        }else{
            return Sequences.sequence(TaskDef.Rule.listDraft(source))
                    .map(it->new KeyValue(it.getName(), it.getRuleId())).join(contextAvailableInputKey(arrangementType)).toList();
        }
    }
}

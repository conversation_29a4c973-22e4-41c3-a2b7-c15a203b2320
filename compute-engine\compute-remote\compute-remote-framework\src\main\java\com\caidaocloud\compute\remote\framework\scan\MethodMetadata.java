package com.caidaocloud.compute.remote.framework.scan;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
public class MethodMetadata {
	private final Method method;
	private Integer addressIndex;

	public MethodMetadata(Method method) {
		this.method = method;
	}

	public static String parseMethodName(Method method) {
		StringBuilder sb = new StringBuilder();
		sb.append(method.getName());
		for (Parameter parameter : method.getParameters()) {
			sb.append('#');
			sb.append(parameter.getName());
		}
		return sb.toString();
	}
}

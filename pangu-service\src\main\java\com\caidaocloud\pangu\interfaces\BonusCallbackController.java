package com.caidaocloud.pangu.interfaces;


import com.caidaocloud.pangu.application.service.BonusApproveService;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/pangu/v1/bonus/approve/callback")
@Slf4j
public class BonusCallbackController {
	@Autowired
	private BonusApproveService bonusApproveService;

	/**
	 * 工作流回调-审批通过
	 * @param dto
	 * @return
	 */
	@PostMapping("approved")
	public Result approved(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow approved callback,{}", dto);
		bonusApproveService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
		return Result.ok(true);
	}


	/**
	 * 工作流回调-审批拒绝
	 * @param dto
	 * @return
	 */
	@PostMapping("refused")
	public Result refused(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow refused callback,{}", dto);
		bonusApproveService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
		return Result.ok(true);
	}
}
package com.caidaocloud.pangu.domain.repository;

import java.util.List;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.googlecode.totallylazy.Triple;

/**
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
public interface IEmpRepository {
	EmpSimple loadEmp(String empId);


	Map<String, Map<String, Object>> loadEmps(List<String> empIds, Map<String, List<Triple<String, String, String>>> identifierPropertyGroup);
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

public class ModelDataTop extends AbstractVariadicFunction {

    //MODEL_DATA_TOP_ORDER( identifier, property, filter, orderBy, order)

    @Override
    public String getName() {
        return "MODEL_DATA_TOP";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        val identifier = FunctionUtils.getStringValue(aviatorObjects[0], env);
        val property = FunctionUtils.getStringValue(aviatorObjects[1], env);
        val filter = (DataFilter) aviatorObjects[2].getValue(env);
        val orderBy = "id";
        val order = "asc";
        val dataList = DataQuery.identifier(identifier).limit(1, 1)
                .filter(filter, DataSimple.class, orderBy + " " + order, System.currentTimeMillis())
                .getItems();
        AviatorObject result;
        MetadataOperatorService metadataService = SpringUtil.getBean(MetadataOperatorService.class);
        MetadataVo metadata = metadataService.load(identifier);
        val isNum = Lists.list(PropertyDataType.Number, PropertyDataType.Integer).contains(metadata.fetchAllProperties().stream()
                .filter(it->it.getProperty().equals(property)).findFirst().get().getDataType());
        if(dataList.isEmpty()){
            if(isNum){
                result = new AviatorDecimal(0);
            }else{
                result = new AviatorString("");
            }
        }else{
            val data = dataList.get(0);
            if("bid".equals(property)){
                result = new AviatorString(data.getBid());
            }else{
                val propertyValue = data.getProperties().get(property);
                if(propertyValue instanceof DictSimple){
                    result = new AviatorString(StringUtils.trimToEmpty(((DictSimple)propertyValue).getCode()));
                }else if(propertyValue instanceof SimplePropertyValue){
                    val value = ((SimplePropertyValue) propertyValue).getValue();
                    if(isNum){
                        if(value == null){
                            result = new AviatorDecimal(0);
                        }else{
                            result = new AviatorDecimal(new BigDecimal(value));
                        }
                    }else{
                        if(value == null){
                            result = new AviatorString("");
                        }else{
                            result = new AviatorString(value);
                        }
                    }
                }else{
                    throw new ServerException("unsupported property data type!");
                }
            }
        }
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(identifier).append(",")
                    .append(property).append(",").append(FastjsonUtil.toJson(filter))
                    .append(")=").append(result.getValue(env)).toString());
        }
        return result;
    }
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class Max extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "MAX";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        BigDecimal result = null;
        val log = new StringBuilder(getName()).append("(");
        for(AviatorObject aviatorObject : aviatorObjects){
            val decimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
            log.append(decimal).append(",");
            if(null == result){
                result = decimal;
            }else{
                if(result.compareTo(decimal) < 0){
                    result = decimal;
                }
            }
        }
        if(FunctionLogTool.logEnabled()){
            log.setLength(log.length()-1);
            log.append(")=").append(result);
            FunctionLogTool.log(log.toString());
        }
        return new AviatorDecimal(result);
    }
}

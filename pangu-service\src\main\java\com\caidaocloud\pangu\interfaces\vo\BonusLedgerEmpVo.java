package com.caidaocloud.pangu.interfaces.vo;

import java.util.List;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.pangu.domain.entity.BonusLedgerEmp;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
@Data
public class BonusLedgerEmpVo {
	@ApiModelProperty("员工id")
	private String empId;

	@Excel(name = "工号", orderNum = "1")
	@ApiModelProperty("工号")
	private String workno;

	@Excel(name = "姓名", orderNum = "2")
	@ApiModelProperty("员工姓名")
	private String name;

	@ApiModelProperty("岗位")
	private String post;

	@ApiModelProperty("岗位名称")
	private String postTxt;

	@ApiModelProperty("所属组织")
	private String organize;

	@Excel(name = "任职组织", orderNum = "3")
	@ApiModelProperty("所属组织名称")
	private String organizeTxt;

	private String workplace;
	@Excel(name = "工作地", orderNum = "4")
	@ApiModelProperty("工作地名称")
	private String workplaceTxt;

	@ApiModelProperty("公司")
	private String company;

	@Excel(name = "合同公司", orderNum = "7")
	@ApiModelProperty("公司名称")
	private String companyTxt;

	@Excel(name = "入职日期", format = "yyyy-MM-dd", orderNum = "8")
	@ApiModelProperty("入职日期")
	private Long hireDate;

	@Excel(name = "员工类型", orderNum = "6")
	@ApiModelProperty("用工类型")
	private DictSimple empType;

	@Excel(name = "离职日期", format = "yyyy-MM-dd", orderNum = "9")
	@ApiModelProperty("离职日期")
	private Long leaveDate;

	@Excel(name = "员工状态", orderNum = "5")
	@ApiModelProperty("员工状态")
	private EnumSimple empStatus;

	@ApiModelProperty("账套名称")
	private Map<String, String> i18nLedgerName;


	@ApiModelProperty("计算年月")
	private Long month;

	@ApiModelProperty("奖金项")
	@JsonProperty("items")
	private List<BonusReport.BonusItem> item;
}

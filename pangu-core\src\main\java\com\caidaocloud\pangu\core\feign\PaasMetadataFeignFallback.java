
package com.caidaocloud.pangu.core.feign;

import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Component
public class PaasMetadataFeignFallback implements PaasMetadataFeign {
	@Override
	public Result<Map<String, String>> getIdentifierFieldMapping(String identifier, String tenantId) {
		return Result.fail();
	}
	
}

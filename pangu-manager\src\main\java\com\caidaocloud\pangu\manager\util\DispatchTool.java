package com.caidaocloud.pangu.manager.util;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.pangu.core.ignite.IgniteCacheContext;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.manager.dto.InvalidNodeDto;
import org.apache.ignite.Ignite;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DispatchTool {
    @Autowired
    private IgniteUtil igniteUtil;

    @Autowired
    private Ignite ignite;

    public static final String DISPATCH_CACHE_NAME = "DISPATCH_CACHE_NAME";

    public IgniteCacheContext<String, TaskDispatchDetail> getDispatchContext(){
        return igniteUtil.keyValueCache(DISPATCH_CACHE_NAME, String.class, TaskDispatchDetail.class);
    }

    public static final String INVALID_NODE_CACHE = "INVALID_NODE_CACHE";

    public IgniteCacheContext<String, InvalidNodeDto> getInvalidNodeContext(){
        return igniteUtil.keyValueCache(INVALID_NODE_CACHE, String.class, InvalidNodeDto.class);
    }
}

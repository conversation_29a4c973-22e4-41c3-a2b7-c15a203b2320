package com.caidaocloud.pangu.node.business.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.node.business.enums.EmpKeyType;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;


@Data
@ApiModel("TaskDefDto")
public class TaskDefDto {

    private String taskDefId;

    private String arrangementId;

    public TaskDef toEntity() {
        TaskDef taskDef = FastjsonUtil.convertObject(this, TaskDef.class);
        taskDef.setTaskDefId(SnowUtil.nextId());
        taskDef.setStatus(TaskDef.Status.DRAFT);
        return taskDef;
    }


    public TaskDef toEntity(TaskDef existed, boolean updateVersion){
        if(updateVersion){
            TaskDef taskDef = FastjsonUtil.convertObject(this, TaskDef.class);
            taskDef.setStatus(TaskDef.Status.RE_DRAFT);
            existed.setStatus(TaskDef.Status.UPDATED);
            return taskDef;
        }else{
            BeanUtils.copyProperties(this, existed);
            return existed;
        }
    }

    @Data
    @ApiModel("TaskDefDto.Detail")
    public static class Detail {

        private String taskDefId;

        private TaskDef.SourceType dsType;

        private String dsIdentifier;

        private String dsTaskDefId;

        private String description;

        private String filter;

        private boolean group = false;

        //private String inputMainKey;

        private boolean output = false;

        private String outputIdentifier;

        @DisplayAsArray
        private List<TaskDef.KeyMapping> keyMappings;

        //private String outputMainKey;

        //private String outputName;

        private TaskDef.OutputType outputType;

        private boolean empKeyOpen;

        private EmpKeyType empKeyType;

        private String empKey;

        public TaskDef.Detail toEntity(TaskDef.Detail existed, boolean updateVersion) {
            if(updateVersion){
                TaskDef.Detail detail = FastjsonUtil.convertObject(this, TaskDef.Detail.class);
                detail.setArrangementId(existed.getArrangementId());
                detail.setStatus(TaskDef.Status.RE_DRAFT);
                existed.setStatus(TaskDef.Status.UPDATED);
                return detail;
            }else{
                BeanUtils.copyProperties(this, existed);
                return existed;
            }
        }

        public TaskDef.Detail toEntity() {
            TaskDef.Detail detail = FastjsonUtil.convertObject(this, TaskDef.Detail.class);
            detail.setStatus(TaskDef.Status.DRAFT);
            return detail;
        }
    }

    @Data
    @ApiModel("TaskDefDto.Rule")
    public static class Rule {

        private String ruleId;

        private String taskDefId;

        private String name;

        private String output;

        private String outputName;

        private String expression;

        private String description;

        private TaskDef.RuleDataType ruleDataType;

        private String convertIntSeqToCharSeq(String seq){
            List<String> chars = Lists.list("a", "b", "c", "d", "e", "f", "g", "h", "i", "j");
            return Lists.list(seq.split("")).stream().map(it->
                chars.get(Integer.valueOf(it))
            ).collect(Collectors.joining());
        }

        public TaskDef.Rule toEntity() {
            TaskDef.Rule rule = FastjsonUtil.convertObject(this, TaskDef.Rule.class);
            rule.setRuleId(convertIntSeqToCharSeq(SnowUtil.nextId()));
            rule.setStatus(TaskDef.Status.DRAFT);
            return rule;
        }

        public TaskDef.Rule toEntity(TaskDef.Rule existed, boolean updateVersion) {
            if(updateVersion){
                TaskDef.Rule rule = FastjsonUtil.convertObject(this, TaskDef.Rule.class);
                rule.setStatus(TaskDef.Status.RE_DRAFT);
                existed.setStatus(TaskDef.Status.UPDATED);
                return rule;
            }else{
                BeanUtils.copyProperties(this, existed);
                return existed;
            }
        }

        @Data
        @ApiModel("TaskDefDto.Rule.Delete")
        public static class Delete{
            private String ruleId;
        }
    }

    @Data
    @ApiModel("TaskDefDto.Create")
    public static class Create{
        private String arrangementId;
    }

}

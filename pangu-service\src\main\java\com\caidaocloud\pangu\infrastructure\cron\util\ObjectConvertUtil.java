package com.caidaocloud.pangu.infrastructure.cron.util;

import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 3/4/2024 2:46 下午
 */
public class ObjectConvertUtil {

    public static <T, F> List<T> convertList(List<F> from, Class<T> toClazz, BiConsumer<F, T> function) {
        return objectConvertCollection(from, toClazz, function);
    }

    public static <T, F> List<T> objectConvertCollection(List<F> modelList, Class<T> toClazz, BiConsumer<F, T> function) {
        return (modelList == null ? Lists.newArrayList() :
                modelList.stream()
                        .map((i) -> convert(i, toClazz, function))
                        .collect(Collectors.toList()));
    }

    public static <T, F> T convert(F from, Class<T> toClazz, BiConsumer<F, T> function) {
        T convert = ObjectConverter.convert(from, toClazz);
        function.accept(from, convert);
        return convert;
    }

}

package com.caidaocloud.pangu.application.dto.cron;

import java.util.Date;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
@Data
public class XxlJobDto {
	private int id;				// 主键ID
	private int jobGroup;		// 执行器主键ID
	private String jobDesc;

	private String author = "system";    // 负责人

	private String scheduleType="CRON";			// 调度类型
	private String scheduleConf;			// 调度配置，值含义取决于调度类型
	private String misfireStrategy="DO_NOTHING";			// 调度过期策略

	private String executorRouteStrategy="FIRST";	// 执行器路由策略
	private String executorHandler;		    // 执行器，任务Handler名称
	private String executorParam;		    // 执行器，任务参数
	private String executorBlockStrategy = "SERIAL_EXECUTION";    // 阻塞处理策略
	private int executorTimeout=0;     		// 任务执行超时时间，单位秒
	private int executorFailRetryCount = 0;        // 失败重试次数

	private String glueType="BEAN";		// GLUE类型	#com.xxl.job.core.glue.GlueTypeEnum
	private int triggerStatus = 1;        // 调度状态：0-停止，1-运行


	public XxlJobDto(int jobGroup, String jobDesc, String scheduleConf, String executorHandler, String executorParam) {
		this.jobGroup = jobGroup;
		this.jobDesc = jobDesc;
		this.scheduleConf = scheduleConf;
		this.executorHandler = executorHandler;
		this.executorParam = executorParam;
	}
}

package com.caidaocloud.pangu.domain;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.pangu.application.dto.BonusSchemaDto;
import com.caidaocloud.pangu.application.dto.BonusStructItemDto;
import com.caidaocloud.pangu.domain.entity.ApproveConfigDo;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/1/22
 */
public class BonusSchemaFactory {


	public static BonusSchema create(BonusSchemaDto dto) {
		BonusSchema schema = ObjectConverter.convert(dto, BonusSchema.class);
		schema.setStructId(dto.getBonusStructBid());
		EnumSimple month = new EnumSimple();
		month.setValue(String.valueOf(dto.getMonth()));
		schema.setMonth(month);
		ApproveConfigDo approveConfigDo = ObjectConverter.convert(dto.getApproveConfig(), ApproveConfigDo.class);
		schema.setApproveConfig(approveConfigDo);
		return schema;
	}

}

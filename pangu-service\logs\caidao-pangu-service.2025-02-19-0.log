2025-02-19 11:29:28.736 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-02-19 11:29:28.800 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-02-19 11:29:29.301 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-02-19 11:29:29.344 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@87467717, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@b1a812c3, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@5741779f, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1b595aff, org.springframework.test.context.support.DirtiesContextTestExecutionListener@58567f48, org.springframework.test.context.transaction.TransactionalTestExecutionListener@24988fd0, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@96642e10, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@86eb42f4, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@634a0580, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@6eeb5e22, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@6e24fc7c, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@c07b5284]
2025-02-19 11:29:30.432 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-02-19 11:29:30.437 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-02-19 11:29:31.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$72d95702] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:32.416 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-02-19 11:29:32.937 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-02-19 11:29:32.964 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-02-19 11:29:36.487 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-02-19 11:29:37.018 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-19 11:29:37.024 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-02-19 11:29:37.114 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68ms. Found 0 repository interfaces.
2025-02-19 11:29:37.253 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-02-19 11:29:37.284 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-02-19 11:29:37.871 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=860c003e-aeb5-3e84-aebe-2873ee3f29f9
2025-02-19 11:29:38.544 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$be856e31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:38.586 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:38.640 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$6bd9124f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:38.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.148 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.163 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.186 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.187 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$4c34c93] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.229 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$1d2b1233] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$8c004db0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$920361ed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:39.713 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.135 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-02-19 11:29:40.140 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-02-19 11:29:40.666 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.729 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.746 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.756 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.763 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.764 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.771 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:40.826 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$72d95702] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:29:41.412 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 11:29:41.412 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 11:29:41.439 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@2fee0786
2025-02-19 11:29:42.126 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-02-19 11:29:42.240 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#37656d43:0/SimpleConnection@f5655033 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 53736]
2025-02-19 11:29:45.332 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-02-19 11:29:45.589 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-02-19 11:29:45.760 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-02-19 11:29:48.393 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-02-19 11:29:49.442 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-02-19 11:29:49.466 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-02-19 11:29:49.595 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-02-19 11:29:49.863 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 11:29:49.864 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 11:29:50.993 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-19 11:29:53.564 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-02-19 11:29:53.622 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-02-19 11:29:53.624 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-02-19 11:29:53.661 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-02-19 11:29:53.779 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-02-19 11:29:54.397 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-02-19 11:29:54.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-02-19 11:29:54.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-02-19 11:29:54.514 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-02-19 11:29:54.546 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-02-19 11:29:54.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-02-19 11:29:54.576 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-02-19 11:29:54.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-02-19 11:29:54.729 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-02-19 11:29:54.973 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-02-19 11:29:55.042 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 25.584 seconds (JVM running for 29.62)
2025-02-19 11:29:55.308 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-02-19 11:29:55.308 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-02-19 11:29:55.308 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-02-19 11:29:55.871 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 11:29:55.982 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-02-19 11:29:55.995 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-02-19 11:29:56.090 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 11:29:56.093 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5b72368c
2025-02-19 11:29:56.459 [main] INFO  org.reflections.Reflections - Reflections took 143 ms to scan 1 urls, producing 9 keys and 30 values 
2025-02-19 11:29:57.016 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 11:30:42.853 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-02-19 11:30:42.901 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory.publisher#da8f60b9:0/SimpleConnection@c3a6028f [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 53906]
2025-02-19 11:30:43.222 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-02-19 11:30:43.223 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-02-19 11:30:43.223 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-02-19 11:30:43.224 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-02-19 11:30:43.227 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-02-19 11:30:43.267 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 11:30:43.340 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 11:30:43.354 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 11:30:44.344 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 11:30:44.387 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-02-19 11:30:44.396 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-02-19 11:30:44.397 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-02-19 11:30:44.397 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-02-19 11:30:44.397 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-02-19 11:30:44.398 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-02-19 11:30:44.398 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-02-19 11:30:44.399 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-02-19 11:30:44.450 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 11:30:44.451 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 11:30:44.516 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-02-19 11:30:45.339 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@e3088518: tags=[[amq.ctag-g3WixtPpue0bqSljIUnvdg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@67ab9d0a Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 11:30:45.339 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@b762231d: tags=[[amq.ctag-0XXe1TmCHViySgxrGm-0sQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@67ab9d0a Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 11:30:45.656 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-02-19 11:30:45.658 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-02-19 11:30:45.659 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-02-19 11:33:19.063 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-02-19 11:33:19.074 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-02-19 11:33:19.430 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-02-19 11:33:19.467 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@d8f7f4b5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4a9d1e24, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@45452275, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3b95689a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@334b379e, org.springframework.test.context.transaction.TransactionalTestExecutionListener@e8051a20, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@ea368436, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@bba91a55, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@bd2d302c, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@7ebc2b3e, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@5ea0dd0f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@6c76c313]
2025-02-19 11:33:21.630 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-02-19 11:33:21.643 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-02-19 11:33:23.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f175f315] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:23.988 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-02-19 11:33:24.576 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-02-19 11:33:24.591 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-02-19 11:33:26.953 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-02-19 11:33:27.621 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-19 11:33:27.628 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-02-19 11:33:27.732 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75ms. Found 0 repository interfaces.
2025-02-19 11:33:27.885 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-02-19 11:33:27.920 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-02-19 11:33:28.543 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=860c003e-aeb5-3e84-aebe-2873ee3f29f9
2025-02-19 11:33:28.993 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$3d220a44] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.019 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.034 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$ea75ae62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.389 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.639 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.662 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$835fe8a6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$9bc7ae46] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:29.868 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$a9ce9c3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:30.231 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$109ffe00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:30.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:31.105 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-02-19 11:33:31.111 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-02-19 11:33:31.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.133 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.164 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.173 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.186 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.196 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.198 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.276 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f175f315] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 11:33:32.939 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 11:33:32.939 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 11:33:32.962 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@f471680f
2025-02-19 11:33:33.887 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-02-19 11:33:34.332 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#c7eb1543:0/SimpleConnection@39400835 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 54149]
2025-02-19 11:33:38.223 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-02-19 11:33:38.563 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-02-19 11:33:38.768 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-02-19 11:33:42.077 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-02-19 11:33:43.434 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-02-19 11:33:43.460 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-02-19 11:33:43.591 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-02-19 11:33:43.931 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 11:33:43.931 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 11:33:45.095 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-19 11:33:47.801 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-02-19 11:33:47.877 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-02-19 11:33:47.878 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-02-19 11:33:47.927 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-02-19 11:33:48.078 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-02-19 11:33:48.693 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-02-19 11:33:48.741 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-02-19 11:33:48.773 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-02-19 11:33:48.777 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-02-19 11:33:48.812 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-02-19 11:33:48.819 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-02-19 11:33:48.837 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-02-19 11:33:48.862 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-02-19 11:33:48.986 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-02-19 11:33:49.202 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-02-19 11:33:49.263 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 29.7 seconds (JVM running for 32.586)
2025-02-19 11:33:49.869 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-02-19 11:33:49.870 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-02-19 11:33:49.870 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-02-19 11:33:51.072 [main] INFO  org.reflections.Reflections - Reflections took 128 ms to scan 1 urls, producing 9 keys and 30 values 
2025-02-19 11:33:51.965 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 11:33:52.159 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-02-19 11:33:52.177 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-02-19 11:33:52.288 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 11:33:52.292 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@65f084d1
2025-02-19 11:34:07.756 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:34:28.317 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-02-19 13:34:28.711 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-02-19 13:34:28.714 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-02-19 13:34:28.726 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-02-19 13:34:28.730 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-02-19 13:34:28.805 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-02-19 13:34:28.862 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-02-19 13:34:28.897 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-02-19 13:34:28.897 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-02-19 13:34:28.898 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-02-19 13:34:29.237 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@9b6fd739: tags=[[amq.ctag-JrXCaxpz5qg0sLW2fDgI2A]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@2fc62738 Shared Rabbit Connection: SimpleConnection@39400835 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 54149], acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:34:29.238 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@12a39acd: tags=[[amq.ctag-T3dhvhShY0ElQADpyL5mGQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,1), conn: Proxy@2fc62738 Shared Rabbit Connection: SimpleConnection@39400835 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 54149], acknowledgeMode=AUTO local queue size=0
2025-02-19 13:34:29.238 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@82300d45: tags=[[amq.ctag-FW1l-7k66V4V76nLpueamg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,2), conn: Proxy@2fc62738 Shared Rabbit Connection: SimpleConnection@39400835 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 54149], acknowledgeMode=AUTO local queue size=0
2025-02-19 13:34:29.253 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6da08718: tags=[[amq.ctag-qjl9aXef4O1Q33jOTz_qFw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@2fc62738 Shared Rabbit Connection: SimpleConnection@39400835 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 54149], acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:34:29.263 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-02-19 13:34:29.325 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#c7eb1543:1/SimpleConnection@c4a25ab7 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 59412]
2025-02-19 13:34:29.359 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 13:34:29.359 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 13:34:29.360 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 13:34:29.360 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 13:34:29.502 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 13:34:29.503 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 13:34:29.506 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@bd7b8fcd
2025-02-19 13:34:29.540 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-02-19 13:34:29.547 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-02-19 13:34:29.549 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-02-19 13:34:29.550 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-02-19 13:34:29.551 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-02-19 13:34:29.553 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-02-19 13:34:29.554 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-02-19 13:34:29.555 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-02-19 13:34:29.722 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 13:34:29.723 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 13:34:29.789 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-02-19 13:34:29.963 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:34:29.970 [main] WARN  o.s.c.a.AnnotationConfigApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ribbonServerList' defined in com.alibaba.cloud.nacos.ribbon.NacosRibbonClientConfiguration: Unsatisfied dependency expressed through method 'ribbonServerList' parameter 1; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'nacosProperties': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-02-19 13:34:30.407 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@19728abf: tags=[[amq.ctag-Tu8Qtro5wFBv55fWMQHYiA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@2fc62738 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:34:30.407 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@1ec2eab1: tags=[[amq.ctag-zM4aR3Z_IfIRanNULmeXog]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,2), conn: Proxy@2fc62738 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:34:30.971 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-02-19 13:34:30.984 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-02-19 13:34:30.994 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-02-19 13:58:53.229 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-02-19 13:58:53.237 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-02-19 13:58:53.521 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-02-19 13:58:53.558 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@77b59c35, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@d3b09042, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b6fa615e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@8475d3c2, org.springframework.test.context.support.DirtiesContextTestExecutionListener@c4389dbc, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7ed51e07, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@d3646182, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@368a5e6b, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@47c43f44, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@183e3aa8, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@211a358d, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3a05af70]
2025-02-19 13:58:54.687 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-02-19 13:58:54.691 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-02-19 13:58:56.845 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$4530fecf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:58:57.718 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-02-19 13:58:58.405 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-02-19 13:58:58.431 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-02-19 13:59:02.629 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-02-19 13:59:03.143 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-19 13:59:03.148 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-02-19 13:59:03.231 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 62ms. Found 0 repository interfaces.
2025-02-19 13:59:03.389 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-02-19 13:59:03.427 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-02-19 13:59:04.014 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=860c003e-aeb5-3e84-aebe-2873ee3f29f9
2025-02-19 13:59:04.315 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$90dd15fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.336 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$3e30ba1c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.567 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.713 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.726 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.726 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$d71af460] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.747 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$ef82ba00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:04.859 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$5e57f57d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.035 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$645b09ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.055 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.324 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-02-19 13:59:05.328 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-02-19 13:59:05.894 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.966 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.971 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.976 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:05.983 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:06.029 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$4530fecf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-19 13:59:06.447 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 13:59:06.447 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 13:59:06.460 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7f78ceba
2025-02-19 13:59:06.991 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-02-19 13:59:07.207 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#5e73eea7:0/SimpleConnection@b36e0361 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 60830]
2025-02-19 13:59:09.677 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-02-19 13:59:09.938 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-02-19 13:59:10.601 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-02-19 13:59:12.233 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-02-19 13:59:12.899 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-02-19 13:59:12.912 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-02-19 13:59:12.971 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-02-19 13:59:13.129 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-19 13:59:13.129 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-19 13:59:13.817 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-02-19 13:59:16.019 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-02-19 13:59:16.050 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-02-19 13:59:16.054 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-02-19 13:59:16.087 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-02-19 13:59:16.154 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-02-19 13:59:16.505 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-02-19 13:59:16.535 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-02-19 13:59:16.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-02-19 13:59:16.564 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-02-19 13:59:16.586 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-02-19 13:59:16.590 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-02-19 13:59:16.604 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-02-19 13:59:16.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-02-19 13:59:16.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-02-19 13:59:17.076 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-02-19 13:59:17.574 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 23.922 seconds (JVM running for 25.566)
2025-02-19 13:59:17.679 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-02-19 13:59:17.679 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-02-19 13:59:17.680 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-02-19 13:59:17.908 [main] INFO  org.reflections.Reflections - Reflections took 68 ms to scan 1 urls, producing 9 keys and 30 values 
2025-02-19 13:59:18.470 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:59:18.534 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-02-19 13:59:18.540 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-02-19 13:59:18.706 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:59:18.708 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5eb29aa1
2025-02-19 13:59:19.545 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:59:20.118 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:59:20.124 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-02-19 13:59:20.125 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-02-19 13:59:20.171 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-02-19 13:59:20.172 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[***************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@76aacd5a
2025-02-19 13:59:20.690 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-02-19 13:59:20.690 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-02-19 13:59:20.690 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-02-19 13:59:20.691 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-02-19 13:59:20.692 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-02-19 13:59:20.867 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 13:59:20.965 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 13:59:20.998 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-02-19 13:59:21.162 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-02-19 13:59:21.178 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-02-19 13:59:21.237 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-02-19 13:59:21.237 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-02-19 13:59:21.237 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-02-19 13:59:21.238 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-02-19 13:59:21.238 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-02-19 13:59:21.238 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-02-19 13:59:21.238 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-02-19 13:59:21.294 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 13:59:21.294 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-02-19 13:59:21.320 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-02-19 13:59:21.383 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@87df605f: tags=[[amq.ctag-i-FU2RDFZtPU9Tuf5FPRkg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@ed9c89ea Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:59:21.600 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@7b3b5029: tags=[[amq.ctag-lAGEZRyqPggSiUlu-AOC5w]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@ed9c89ea Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-02-19 13:59:22.442 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-02-19 13:59:22.442 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-02-19 13:59:22.443 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!

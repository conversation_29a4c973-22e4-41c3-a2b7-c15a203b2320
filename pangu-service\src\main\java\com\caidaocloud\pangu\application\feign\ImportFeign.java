package com.caidaocloud.pangu.application.feign;

import com.caidaocloud.pangu.application.dto.ImportFunctionDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


@FeignClient(value = "${feign.rename.caidaocloud-import-export-service:caidaocloud-import-export-service}", fallback = ImportFeignFallback.class, configuration = FeignConfiguration.class, contextId = "importFeign")
public interface ImportFeign {

    @PostMapping("/api/import/v1/config/function")
    Result<Boolean> addImportFunction(ImportFunctionDto function);

}

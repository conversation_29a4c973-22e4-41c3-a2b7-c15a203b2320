package com.caidaocloud.pangu.application.feign;

import com.caidaocloud.pangu.application.dto.workflow.WfTaskApproveDTO;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskBackDTO;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskRevokeDTO;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

/**
 * 工作流流程操作
 */
@Component
public class WfOperateFeignFallBack implements WfOperateFeignClient {
    /**
     * 任务审批
     *
     * @param wfApproveTaskDTO
     * @return
     */
    @Override
    public Result<?> approveTask(WfTaskApproveDTO wfApproveTaskDTO) {
        return Result.fail();
    }

    /**
     * 任务驳回
     *
     * @param wfTaskBackDTO
     * @return
     */
    @Override
    public Result backTask(WfTaskBackDTO wfTaskBackDTO) {
        return Result.fail();
    }

    /**
     * 任务流程撤回
     *
     * @param wfTaskRevokeDTO
     * @return
     */
    @Override
    public Result<String> revokeProcessOfTask(WfTaskRevokeDTO wfTaskRevokeDTO) {
        return Result.fail();
    }

//    /**
//     * 流程任务第三方业务状态更新
//     *
//     * @param wfTaskThirdStatusDTO
//     * @return
//     */
//    @Override
//    public Result<String> updateThirdStatus(WfTaskThirdStatusDTO wfTaskThirdStatusDTO) {
//        return Result.fail();
//    }
//
//    /**
//     * 获取任务节点配置
//     *
//     * @param taskId 任务节点id
//     * @return
//     */
//    @Override
//    public Result<WfDefNodeUserTaskDTO> getConfigOfCurrentNodeByTaskId(String taskId) {
//        return Result.fail();
//    }
//
//    /**
//     * 获取流程发起人和申请人
//     *
//     * @param businessKey 流程业务key
//     * @return
//     */
//    @Override
//    public Result<WfInitiatorAndApplicantVo> getInitiatorAndApplicant(String businessKey) {
//        return Result.fail();
//    }
//
//    @Override
//    public Result<Boolean> checkDefEnabled(String funCode) {
//        return Result.fail();
//    }
}

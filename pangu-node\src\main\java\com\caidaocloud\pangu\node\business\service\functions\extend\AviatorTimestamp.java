package com.caidaocloud.pangu.node.business.service.functions.extend;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorType;
import com.googlecode.totallylazy.Maps;
import lombok.val;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

public class AviatorTimestamp extends AviatorObject {

    private LocalDateTime dateTime;

    public AviatorTimestamp(long timestamp){
        dateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp/1000), ZoneId.systemDefault());
    }

    public AviatorTimestamp(LocalDateTime dateTime){
        this.dateTime = dateTime;
    }

    @Override
    public int innerCompare(AviatorObject other, Map<String, Object> map) {
        if(((LocalDateTime)this.getValue(map)).isBefore(((LocalDateTime)other.getValue(map)))){
            return -1;
        }else if(((LocalDateTime)this.getValue(map)).isAfter(((LocalDateTime)other.getValue(map)))){
            return 1;
        }else{
            return 0;
        }
    }

    @Override
    public AviatorType getAviatorType() {
        return AviatorType.JavaType;
    }

    @Override
    public Object getValue(Map<String, Object> map) {
        return dateTime;
    }

    public static LocalDateTime transformAviatorValue(AviatorObject aviatorObject, Map<String, Object> env){
        val value = aviatorObject.getValue(env);
        if(null == value || value instanceof LocalDateTime){
            return (LocalDateTime) value;
        }else{
            return (LocalDateTime) new AviatorTimestamp(Long.valueOf(String.valueOf(value))).getValue(env);
        }
    }
}

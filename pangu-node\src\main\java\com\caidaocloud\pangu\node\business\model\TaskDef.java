package com.caidaocloud.pangu.node.business.model;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.node.business.enums.EmpKeyType;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.val;

import java.util.List;

@Data
@ApiModel("TaskDef")
public class TaskDef extends DataSimple {

    public static final String identifier = "entity.pangu.TaskDef";

    private String taskDefId;

    private String taskDefVid;

    private String arrangementId;

    private String arrangementVid;

    private Status status;

    public static String create(String arrangementId) {
        val taskDef = new TaskDef();
        taskDef.taskDefId = SnowUtil.nextId();
        taskDef.arrangementId = arrangementId;
        taskDef.status = Status.DRAFT;
        DataInsert.identifier(identifier).insert(taskDef);

        Detail detail = new Detail();
        detail.taskDefId = taskDef.taskDefId;
        detail.arrangementId = arrangementId;
        detail.status = Status.DRAFT;
        detail.create();

        return taskDef.taskDefId;
    }



    public void createBasic() {
        DataInsert.identifier(identifier).insert(this);
    }

    public void update() {
        DataUpdate.identifier(identifier).update(this);
    }

    public static TaskDef loadDraft(String taskDefId) {
        return DataQuery.identifier(identifier)
                .filter(DataFilter.eq("taskDefId", taskDefId)
                        .andIn("status", Lists.list(
                                Status.DRAFT.name(),
                                Status.PUBLISHED.name(),
                                Status.RE_DRAFT.name())), TaskDef.class)
                .getItems().stream().findAny().orElseThrow(()->new ServerException("task def not exist"));
    }

    public static TaskDef loadPublished(String taskDefId) {
        return DataQuery.identifier(identifier)
                .filter(DataFilter.eq("taskDefId", taskDefId)
                        .andIn("status", Lists.list(
                                Status.PUBLISHED.name(),
                                Status.UPDATED.name())), TaskDef.class)
                .getItems().stream().findAny().orElseThrow(()->new ServerException("task def not exist"));
    }

    public static List<TaskDef> listDraft(String arrangementId) {
        return DataQuery.identifier(identifier).limit(-1, 1)
                .filter(DataFilter.eq("arrangementId", arrangementId)
                        .andIn("status", Lists.list(
                                Status.DRAFT.name(),
                                Status.PUBLISHED.name(),
                                Status.RE_DRAFT.name())), TaskDef.class)
                .getItems();
    }

    public static List<TaskDef> listPublished(String arrangementId) {
        return DataQuery.identifier(identifier).limit(-1, 1)
                .filter(DataFilter.eq("arrangementId", arrangementId)
                        .andIn("status", Lists.list(
                                Status.PUBLISHED.name(),
                                Status.UPDATED.name())), TaskDef.class)
                .getItems();
    }

    public void delete() {
        DataDelete.identifier(identifier).delete(getBid());
    }

    public enum Status{
        DRAFT, PUBLISHED, RE_DRAFT, UPDATED, REPLACED
    }

    public enum SourceType {
        PAAS, TASK_OUTPUT
    }



    public enum OutputType{
        INCREMENTAL, FULL
    }

    @Data
    @ApiModel("TaskDef.Detail")
    public static class Detail extends DataSimple{

        public static final String identifier = "entity.pangu.TaskDefDetail";

        private String arrangementId;

        private String arrangementVid;

        private String taskDefId;

        private String taskDefVid;

        private SourceType dsType;

        private String dsIdentifier;

        private String dsTaskDefId;

        private String description;

        private String filter;

        private boolean group = false;

        //private String inputMainKey;

        private boolean output = false;

        private String outputIdentifier;

        //private String outputMainKey;

        //private String outputName;

        @DisplayAsArray
        private List<KeyMapping> keyMappings;

        private OutputType outputType;

        private Status status;

        private boolean empKeyOpen;

        private EmpKeyType empKeyType;

        private String empKey;

        public static Detail loadDraft(String taskDefId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Detail.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("task def not exist"));
        }

        public static List<Detail> listDraft(String arrangementId) {
            return DataQuery.identifier(identifier).limit(-1, 1)
                    .filter(DataFilter.eq("arrangementId", arrangementId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Detail.class)
                    .getItems();
        }

        public static Detail loadPublished(String taskDefId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Detail.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("task def not exist"));
        }

        public static Detail loadVersion(String taskDefId, String arrangementVid) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andEq("arrangementVid", arrangementVid), Detail.class)
                    .getItems().stream().filter(it->
                            Lists.list(Status.UPDATED, Status.PUBLISHED, Status.REPLACED).contains(it.getStatus()))
                    .findAny().orElseThrow(()->new ServerException("task def not exist"));
        }

        public void create() {
            DataInsert.identifier(identifier).insert(this);
        }

        public void update() {
            DataUpdate.identifier(identifier).update(this);
        }

        public void delete() {
            DataDelete.identifier(identifier).delete(getBid());
        }


    }

    @Data
    public static class KeyMapping{

        private String input;

        private String output;

    }

    @Data
    @ApiModel("TaskDef.Rule")
    public static class Rule extends DataSimple{

        public static final String identifier = "entity.pangu.TaskDefRule";

        private String ruleId;

        private String ruleVid;

        private String taskDefId;

        private String taskDefVid;

        private String name;

        private String output;

        private String outputName;

        private String expression;

        private String description;

        private Status status;

        private RuleDataType ruleDataType;

        public static Rule loadDraft(String ruleId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("ruleId", ruleId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Rule.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("rule not exist"));
        }

        public static Rule loadPublished(String ruleId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("ruleId", ruleId)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Rule.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("rule not exist"));
        }

        public static List<Rule> listDraft(String taskDefId) {
            return DataQuery.identifier(identifier).limit(-1, 1)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Rule.class)
                    .getItems();
        }

        public static PageResult<Rule> pageDraft(String taskDefId, int pageSize, int pageNo) {
            return DataQuery.identifier(identifier).limit(pageSize, pageNo)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Rule.class, "createTime asc", System.currentTimeMillis());
        }

        public static PageResult<Rule> pagePublished(String taskDefId, int pageSize, int pageNo) {
            return DataQuery.identifier(identifier).limit(pageSize, pageNo)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Rule.class, "createTime asc", System.currentTimeMillis());
        }

        public static List<Rule> listByTaskVersion(String taskDefVid) {
            return DataQuery.identifier(identifier).limit(-1, 1)
                    .filter(DataFilter.eq("taskDefVid", taskDefVid)
                            , Rule.class).getItems();
        }

        public static List<Rule> listPublished(String taskDefId) {
            return DataQuery.identifier(identifier).limit(-1, 1)
                    .filter(DataFilter.eq("taskDefId", taskDefId)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Rule.class).getItems();
        }

        public void update() {
            this.setUpdateTime(System.currentTimeMillis());
            DataUpdate.identifier(identifier).update(this);
        }

        public String create() {
            this.setUpdateTime(System.currentTimeMillis());
            this.setCreateTime(this.getUpdateTime());
            return DataInsert.identifier(identifier).insert(this);
        }

        public void delete() {
            DataDelete.identifier(identifier).delete(getBid());
        }

        public void validate() {
            ExpressionTool.validateExp(expression);
        }

        public void publishExp() {
            ExpressionTool.publishExp(ruleVid, expression);
        }
    }

    public enum RuleDataType {
        Number, Int, String, Timestamp
    }
}

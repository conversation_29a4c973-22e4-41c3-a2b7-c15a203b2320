// package com.caidaocloud.pangu.infrastructure.config;
//
// import javax.sql.DataSource;
//
// import com.caidaocloud.pangu.infrastructure.ignite.store.DbContext;
// import com.caidaocloud.pangu.infrastructure.ignite.store.IgniteBlobStoreConfigurationFactory;
// import com.caidaocloud.pangu.infrastructure.ignite.store.IgniteDbStoreConfigurationFactory;
// import com.caidaocloud.pangu.infrastructure.ignite.store.PgsqlDbContext;
//
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// /**
//  *
//  * <AUTHOR> Zhou
//  * @date 2023/7/28
//  */
// // @Configuration
// public class IgniteConfig {
//
// 	@Value("${spring.datasource.url}")
// 	private String connUrl;
// 	@Value("${spring.datasource.username}")
// 	private String user;
// 	@Value("${spring.datasource.password}")
// 	private String pwd;
//
// 	@Bean
// 	public DbContext dbContext() {
// 		return new PgsqlDbContext(connUrl, user, pwd);
// 	}
//
// 	@Bean
// 	public IgniteDbStoreConfigurationFactory igniteDbStoreConfigurationFactory(DbContext dbContext) {
// 		IgniteBlobStoreConfigurationFactory factory = new IgniteBlobStoreConfigurationFactory(dbContext);
// 		return factory;
// 	}
// }

package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

public class Trim extends AbstractFunction {
    @Override
    public String getName() {
        return "TRIM";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1) {
        val value = FunctionUtils.getStringValue(aviatorObject1, env);
        val result = StringUtils.trimToEmpty(value);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(value).append(")=").append(result).toString());
        }
        return new AviatorString(result);
    }
}

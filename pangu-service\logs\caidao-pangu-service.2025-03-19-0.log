2025-03-19 11:04:46.553 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:04:46.610 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:04:46.894 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:04:46.922 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@9545d77e, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@9fff3e6, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@55fd3e9d, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@a6f7d934, org.springframework.test.context.support.DirtiesContextTestExecutionListener@e08c7899, org.springframework.test.context.transaction.TransactionalTestExecutionListener@3455953f, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@6bcbb725, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@10551c45, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@29e5b6ec, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@b80d0131, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@5e57dac5, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@22f57b9c]
2025-03-19 11:04:47.918 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:04:47.925 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:04:48.877 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d95284c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:49.480 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:04:49.856 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:04:49.889 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:04:52.857 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:04:53.260 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:04:53.264 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:04:53.330 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51ms. Found 0 repository interfaces.
2025-03-19 11:04:53.403 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:04:53.420 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:04:53.824 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:04:54.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$59413f7b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.072 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.080 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$694e399] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.432 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.452 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$9f7f1ddd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.480 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$b7e6e37d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$26bc1efa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.871 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$2cbf3337] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:54.899 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:55.417 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:04:55.426 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:04:56.012 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.067 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.083 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.092 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.093 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.098 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.099 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.138 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d95284c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:04:56.561 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:04:56.561 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:04:56.580 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@987adab8
2025-03-19 11:04:57.117 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:04:57.212 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#737a2c58:0/SimpleConnection@d74c7389 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 64204]
2025-03-19 11:04:59.333 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:04:59.516 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:04:59.729 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:05:01.335 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:05:01.945 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:05:01.959 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:05:02.028 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:05:02.225 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:05:02.225 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:05:02.998 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:05:04.624 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:05:04.652 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:05:04.654 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:05:04.679 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:05:04.739 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:05:05.059 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:05:05.086 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:05:05.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:05:05.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:05:05.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:05:05.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:05:05.156 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:05:05.161 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:05:05.178 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:05:05.249 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:05:05.401 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:05:05.471 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 18.477 seconds (JVM running for 20.275)
2025-03-19 11:05:05.600 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:05:05.600 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:05:05.601 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:05:05.775 [main] INFO  org.reflections.Reflections - Reflections took 62 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:05:05.941 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:05:06.005 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:05:06.012 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:05:06.086 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:05:06.088 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@51d2cedb
2025-03-19 11:05:07.020 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:05:07.359 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:05:07.359 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:05:07.359 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:05:07.360 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:05:07.361 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:05:07.375 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:05:08.353 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:05:08.370 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:05:08.419 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:05:08.438 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:05:08.448 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:05:08.448 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:05:08.449 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:05:08.449 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:05:08.449 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:05:08.449 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:05:08.450 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:05:08.481 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:05:08.482 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:05:08.496 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:05:08.499 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@93337f45: tags=[[amq.ctag-meMRhIGBqZXMwppnriRhqg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@3d1320d9 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:05:09.479 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@ae75be2c: tags=[[amq.ctag-QoGFAZ42NGNj5m4j_KL18Q]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@3d1320d9 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:05:09.622 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:05:09.622 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:05:09.622 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:05:44.981 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:05:44.992 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:05:45.403 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:05:45.429 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@bb1d52af, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@86f4f5db, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@da905063, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1532b058, org.springframework.test.context.support.DirtiesContextTestExecutionListener@abc37a6f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7af0f651, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4e7ef425, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@dd420dde, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@c3d21b0e, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@3f564ab2, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@ebbe7d4b, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@8a4b7feb]
2025-03-19 11:05:46.182 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:05:46.187 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:05:48.147 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6195ecf0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:48.742 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:05:49.170 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:05:49.183 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:05:51.132 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:05:51.641 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:05:51.645 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:05:51.714 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53ms. Found 0 repository interfaces.
2025-03-19 11:05:51.806 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:05:51.834 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:05:52.233 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:05:52.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$ad42041f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:52.648 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:52.658 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$5a95a83d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:52.884 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.030 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.052 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$f37fe281] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.078 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$be7a821] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.221 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$7abce39e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$80bff7db] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:53.848 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:05:53.852 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:05:54.351 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.410 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.435 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.443 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.448 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.449 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:54.500 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6195ecf0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:05:55.030 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:05:55.030 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:05:55.048 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@dc38c3cb
2025-03-19 11:05:55.693 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:05:55.796 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#b3d8de50:0/SimpleConnection@bd509d05 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 64419]
2025-03-19 11:05:58.586 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:05:58.786 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:05:58.991 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:06:01.113 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:06:02.213 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:06:02.229 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:06:02.301 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:06:02.837 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:06:02.837 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:06:03.862 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:06:05.842 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:06:05.881 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:06:05.883 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:06:05.909 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:06:05.993 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:06:06.501 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:06:06.544 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:06:06.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:06:06.581 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:06:06.605 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:06:06.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:06:06.626 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:06:06.638 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:06:06.661 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:06:06.756 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:06:06.919 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:06:07.047 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 21.547 seconds (JVM running for 26.111)
2025-03-19 11:06:07.165 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:06:07.166 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:06:07.166 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:06:07.357 [main] INFO  org.reflections.Reflections - Reflections took 68 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:06:07.554 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:06:07.634 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:06:07.648 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:06:07.754 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:06:07.756 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@57ef9448
2025-03-19 11:06:08.667 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:06:42.626 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:06:42.626 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:06:42.626 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:06:42.626 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:06:42.628 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:06:42.646 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:06:43.622 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:06:43.629 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:06:44.633 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:06:44.657 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:06:44.664 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:06:44.664 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:06:44.664 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:06:44.664 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:06:44.665 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:06:44.665 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:06:44.666 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:06:44.704 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:06:44.704 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:06:44.720 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:06:45.629 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5cbd1a17: tags=[[amq.ctag-ZAl9WTkIUm5cFTA-hfsO8w]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@6437f544 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:06:45.629 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@64d036ec: tags=[[amq.ctag-86jSeQ2b-BTpkUsFLFC35g]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@6437f544 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:06:45.854 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:06:45.854 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:06:45.855 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:07:35.212 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:07:35.219 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:07:35.540 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:07:35.568 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@de37daf8, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@1e497081, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@420abdeb, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4c7763f6, org.springframework.test.context.support.DirtiesContextTestExecutionListener@56997d33, org.springframework.test.context.transaction.TransactionalTestExecutionListener@663e1f01, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@e204b0e0, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@8ce890cd, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@3f8ce1de, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@25b962e, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@daf732b4, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@263484f9]
2025-03-19 11:07:37.013 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:07:37.016 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:07:38.131 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8b1cfc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:38.970 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:07:39.358 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:07:39.370 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:07:41.233 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:07:41.757 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:07:41.760 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:07:41.842 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 61ms. Found 0 repository interfaces.
2025-03-19 11:07:41.965 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:07:41.998 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:07:42.398 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:07:42.787 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$5837342b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:42.810 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:42.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$58ad849] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.026 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.174 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.180 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.197 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.197 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$9e75128d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.227 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$b6dcd82d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$25b213aa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.628 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$2bb527e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:43.672 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.013 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:07:44.017 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:07:44.438 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.464 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.490 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.509 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.525 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.529 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.530 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:44.577 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$c8b1cfc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:07:45.153 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:07:45.153 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:07:45.188 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@a78e18a4
2025-03-19 11:07:45.743 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:07:45.840 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#9f299178:0/SimpleConnection@1a8194ff [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 64676]
2025-03-19 11:07:49.519 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:07:49.775 [redisson-netty-5-12] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:07:49.947 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:07:53.297 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:07:54.282 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:07:54.296 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:07:54.393 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:07:54.630 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:07:54.630 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:07:55.836 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:07:58.212 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:07:58.260 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:07:58.263 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:07:58.325 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:07:58.446 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:07:58.983 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:07:59.029 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:07:59.067 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:07:59.075 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:07:59.104 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:07:59.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:07:59.128 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:07:59.137 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:07:59.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:07:59.258 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:07:59.451 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:07:59.519 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 23.851 seconds (JVM running for 28.717)
2025-03-19 11:07:59.667 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:07:59.668 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:07:59.669 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:07:59.958 [main] INFO  org.reflections.Reflections - Reflections took 111 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:08:00.246 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:08:00.348 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:08:00.366 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:08:00.504 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:08:00.507 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@98da9a41
2025-03-19 11:08:01.380 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:11:57.229 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:11:57.228 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:11:57.232 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:11:57.234 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:11:57.238 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:11:57.477 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:11:58.146 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:11:58.152 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:11:59.134 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:11:59.335 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:11:59.365 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:11:59.366 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:11:59.366 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:11:59.367 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:11:59.369 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:11:59.370 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:11:59.371 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:11:59.553 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:11:59.555 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:11:59.620 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:12:00.119 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@30931a0e: tags=[[amq.ctag-LwMnYR2lNnIzzdwtNH8mig]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@6387b8b9 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:12:00.134 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@e2e195ae: tags=[[amq.ctag-NpDNTjf0r4VWpnDoOCIgJQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@6387b8b9 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:12:00.794 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:12:00.797 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:12:00.798 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:12:10.311 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:12:10.445 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:12:11.034 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:12:11.061 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@c7f3bd1b, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@d502f05c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@746d714, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@26f608c8, org.springframework.test.context.support.DirtiesContextTestExecutionListener@e0023a65, org.springframework.test.context.transaction.TransactionalTestExecutionListener@978b7a8c, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@8d61b907, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@6493f7c2, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@2716f8e2, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@3c5de0bb, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@c195af80, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@532b0995]
2025-03-19 11:12:11.902 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:12:11.905 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:12:12.750 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$78e31d69] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:13.448 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:12:13.931 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:12:13.945 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:12:15.926 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:12:16.396 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:12:16.400 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:12:16.477 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 56ms. Found 0 repository interfaces.
2025-03-19 11:12:16.577 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:12:16.602 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:12:17.021 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:12:17.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$c48f3498] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$71e2d8b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.640 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.779 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.803 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.804 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$acd12fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.825 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$2334d89a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:17.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$920a1417] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:18.193 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$980d2854] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:18.217 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:18.662 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:12:18.666 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:12:19.180 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.241 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.263 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.275 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$78e31d69] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:12:19.836 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:12:19.836 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:12:19.852 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@b44ce003
2025-03-19 11:12:20.455 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:12:20.622 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#681cd960:0/SimpleConnection@4c584c1b [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 65045]
2025-03-19 11:12:23.859 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:12:24.169 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:12:24.431 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:12:26.824 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:12:27.670 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:12:27.684 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:12:27.762 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:12:27.961 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:12:27.961 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:12:28.808 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:12:30.822 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:12:30.863 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:12:30.868 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:12:30.897 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:12:30.988 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:12:31.447 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:12:31.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:12:31.527 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:12:31.530 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:12:31.556 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:12:31.561 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:12:31.577 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:12:31.583 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:12:31.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:12:31.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:12:31.903 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:12:32.433 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 21.301 seconds (JVM running for 24.484)
2025-03-19 11:12:32.560 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:12:32.560 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:12:32.560 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:12:32.779 [main] INFO  org.reflections.Reflections - Reflections took 80 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:12:32.981 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:12:33.061 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:12:33.072 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:12:33.218 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:12:33.221 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@343b662
2025-03-19 11:12:34.083 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:13:34.922 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:13:34.923 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:13:34.925 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:13:34.925 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:13:34.926 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:13:34.947 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:13:35.908 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:13:35.915 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:13:36.907 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:13:37.638 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:13:37.664 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:13:37.664 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:13:37.665 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:13:37.665 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:13:37.666 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:13:37.666 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:13:37.685 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:13:37.884 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:13:37.885 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:13:37.960 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:13:38.914 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@33399229: tags=[[amq.ctag-apzkIZrrtDz7EGyR1oxsRA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@7a2e83d Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:13:38.914 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5529671a: tags=[[amq.ctag-x11-D2iq10T6AgluIRkeiA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@7a2e83d Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:13:39.121 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:13:39.129 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:13:39.137 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:22:06.123 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:22:06.137 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:22:06.574 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:22:06.628 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@44f8e26a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@fa1328aa, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@6b4eb9cc, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@c376606a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@ae2f0c5, org.springframework.test.context.transaction.TransactionalTestExecutionListener@f4e04154, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@bb0e31c0, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@86800670, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@158f9f2d, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@78755f57, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1b4679ff, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@bd01a14d]
2025-03-19 11:22:07.764 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:22:07.769 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:22:08.796 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f50a2a67] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:09.674 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:22:10.220 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:22:10.234 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:22:12.604 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:22:13.180 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:22:13.186 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:22:13.277 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 64ms. Found 0 repository interfaces.
2025-03-19 11:22:13.406 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:22:13.433 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:22:13.941 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:22:14.398 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$40b64196] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:14.432 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:14.444 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$ee09e5b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:14.803 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:14.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.000 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.023 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.024 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$86f41ff8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.051 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$9f5be598] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.232 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$e312115] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.558 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$14343552] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:15.592 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.034 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:22:16.041 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:22:16.701 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.742 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.781 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.805 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.814 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.825 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.833 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.835 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:16.897 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f50a2a67] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:22:17.454 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:22:17.454 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:22:17.475 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@39671cfd
2025-03-19 11:22:18.136 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:22:18.255 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#c9e810f8:0/SimpleConnection@cc998e10 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 49382]
2025-03-19 11:22:20.838 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:22:21.031 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:22:21.205 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:22:23.141 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:22:23.897 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:22:23.909 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:22:23.988 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:22:24.188 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:22:24.189 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:22:25.069 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:22:26.990 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:22:27.031 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:22:27.038 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:22:27.067 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:22:27.159 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:22:27.714 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:22:27.765 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:22:27.806 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:22:27.810 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:22:27.845 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:22:27.851 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:22:27.879 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:22:27.890 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:22:27.921 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:22:28.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:22:28.326 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:22:28.387 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 21.633 seconds (JVM running for 24.263)
2025-03-19 11:22:28.609 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:22:28.610 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:22:28.610 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:22:28.887 [main] INFO  org.reflections.Reflections - Reflections took 100 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:22:29.184 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:22:29.300 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:22:29.311 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:22:29.458 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:22:29.462 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@2ff2015b
2025-03-19 11:22:30.324 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:33:10.486 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-03-19 11:33:10.639 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:33:10.645 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:33:10.648 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:33:10.652 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:33:10.668 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:33:10.800 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:33:10.803 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:33:10.807 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:33:10.807 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:33:11.188 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:33:11.189 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:33:11.190 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:33:11.191 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:33:11.279 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@155a49f3: tags=[[amq.ctag-wXJGx5xOniuns97ra-x29A]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@c8932a7a Shared Rabbit Connection: SimpleConnection@cc998e10 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 49382], acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:33:11.282 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@50f16359: tags=[[amq.ctag-y8BTZDt02CnQuoJYvLmHjw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@c8932a7a Shared Rabbit Connection: SimpleConnection@cc998e10 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 49382], acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:33:11.292 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:33:11.344 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#c9e810f8:1/SimpleConnection@15d59a7e [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 50498]
2025-03-19 11:33:11.467 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:33:11.482 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:33:11.483 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:33:11.485 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:33:11.488 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:33:11.490 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:33:11.490 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:33:11.491 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:33:11.668 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:33:11.669 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:33:11.765 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:33:12.467 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@41907855: tags=[[amq.ctag-IZQriFne18Lsqt06__1O_A]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,2), conn: Proxy@c8932a7a Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:33:12.467 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5d747581: tags=[[amq.ctag-X3KBsdP1OZsZF0_2bFwXuQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@c8932a7a Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:33:13.058 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:33:13.071 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:33:13.076 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:33:35.462 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:33:35.476 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:33:35.791 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:33:35.818 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@aac3d91, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@dd8369e8, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@3e702234, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@8b6c33aa, org.springframework.test.context.support.DirtiesContextTestExecutionListener@f28b2e09, org.springframework.test.context.transaction.TransactionalTestExecutionListener@9afd6322, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@975552d, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@916f31ea, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@918bebf, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@a4054e8f, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@7c56c91c, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@a555fdf3]
2025-03-19 11:33:36.645 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:33:36.649 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:33:37.479 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80f50257] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:39.312 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:33:39.756 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:33:39.797 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:33:41.523 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:33:41.928 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:33:41.933 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:33:42.000 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51ms. Found 0 repository interfaces.
2025-03-19 11:33:42.129 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:33:42.155 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:33:42.574 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:33:42.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$cca11986] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:42.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:42.952 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$79f4bda4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.165 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.309 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.315 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.330 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.330 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$12def7e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.356 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$2b46bd88] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.495 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$9a1bf905] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.706 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$a01f0d42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:43.729 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.041 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:33:44.047 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:33:44.455 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.482 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.511 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.531 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.536 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.541 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.542 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.546 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.547 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:44.596 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$80f50257] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:33:45.068 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:33:45.068 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:33:45.084 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@530f5eae
2025-03-19 11:33:45.580 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:33:45.665 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#25ee9285:0/SimpleConnection@e3f9f0d0 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 50552]
2025-03-19 11:33:48.563 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:33:48.742 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:33:48.899 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:33:50.822 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:33:51.589 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:33:51.601 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:33:51.680 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:33:51.885 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:33:51.885 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:33:52.900 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:33:55.116 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:33:55.163 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:33:55.171 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:33:55.207 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:33:55.346 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:33:56.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:33:56.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:33:56.175 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:33:56.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:33:56.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:33:56.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:33:56.265 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:33:56.284 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:33:56.334 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:33:56.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:33:56.855 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:33:56.931 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 21.03 seconds (JVM running for 23.125)
2025-03-19 11:33:57.132 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:33:57.132 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:33:57.132 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:33:58.191 [main] INFO  org.reflections.Reflections - Reflections took 598 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:33:58.565 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:33:58.783 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:33:58.803 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:33:58.962 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:33:58.966 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7f39da1
2025-03-19 11:33:59.821 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:38:12.758 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-03-19 11:44:53.702 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:44:53.726 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:44:53.728 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:44:53.822 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:44:53.944 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:44:54.463 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:44:54.485 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:44:54.487 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:44:54.493 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-19 11:44:54.531 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@22430306: tags=[[amq.ctag-gLMNlC7cuDdO2avfc-lZgg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@2ef31f35 Shared Rabbit Connection: SimpleConnection@e3f9f0d0 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 50552], acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:44:54.571 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:44:54.608 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#25ee9285:1/SimpleConnection@9b19efdd [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 51637]
2025-03-19 11:44:54.965 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:44:54.966 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:44:54.969 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:44:54.969 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:44:55.260 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@8493b023: tags=[[amq.ctag-6y1je6Wx6e40xKrWnqJPnQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@2ef31f35 Shared Rabbit Connection: SimpleConnection@9b19efdd [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 51637], acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:44:55.342 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:44:55.349 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:44:55.354 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:44:55.354 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:44:55.355 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:44:55.358 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:44:55.360 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:44:55.362 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:44:55.562 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:44:55.562 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:44:55.740 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:44:56.064 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@ca65d7fc: tags=[[amq.ctag-1Ueoc8PTaoSvolRiPx1hCQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,2), conn: Proxy@2ef31f35 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:44:56.296 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@8683532f: tags=[[amq.ctag-PThwv3RFc_lCSCMah3-Q7Q]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,1), conn: Proxy@2ef31f35 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:44:56.959 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:44:56.964 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:44:56.967 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 11:45:19.478 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 11:45:19.490 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 11:45:19.855 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 11:45:19.889 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@fe44d411, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@701293c8, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b0799d91, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@eac61edc, org.springframework.test.context.support.DirtiesContextTestExecutionListener@1150d236, org.springframework.test.context.transaction.TransactionalTestExecutionListener@e391a346, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@43d5fc20, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@6ff73a3b, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@e5c4af07, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@bdc99622, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@4b99bf09, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@d6117baf]
2025-03-19 11:45:21.050 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 11:45:21.055 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 11:45:21.972 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$38dddc56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:22.653 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 11:45:23.118 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 11:45:23.130 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 11:45:25.214 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 11:45:25.739 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 11:45:25.743 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 11:45:25.824 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58ms. Found 0 repository interfaces.
2025-03-19 11:45:25.943 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 11:45:25.972 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 11:45:26.596 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 11:45:27.139 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$8489f385] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.164 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$31dd97a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.587 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.593 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.609 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.610 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$cac7d1e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.634 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$e32f9787] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.766 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$5204d304] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:27.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$5807e741] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:28.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:29.169 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 11:45:29.191 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 11:45:29.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.023 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.070 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.078 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.084 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.085 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.089 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.090 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.136 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$38dddc56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 11:45:30.571 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:45:30.571 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:45:30.591 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c20d365f
2025-03-19 11:45:31.139 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 11:45:31.235 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#b9905b5a:0/SimpleConnection@f9313c1c [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 51685]
2025-03-19 11:45:35.082 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 11:45:35.269 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 11:45:35.458 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 11:45:37.474 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 11:45:38.247 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 11:45:38.265 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 11:45:38.345 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 11:45:38.539 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 11:45:38.539 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 11:45:39.454 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 11:45:41.845 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 11:45:41.881 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 11:45:41.882 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 11:45:41.912 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 11:45:42.015 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 11:45:42.511 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 11:45:42.551 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 11:45:42.585 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 11:45:42.589 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 11:45:42.624 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 11:45:42.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 11:45:42.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 11:45:42.661 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 11:45:42.682 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 11:45:42.792 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 11:45:42.928 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 11:45:42.985 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 22.914 seconds (JVM running for 27.391)
2025-03-19 11:45:43.117 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 11:45:43.117 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 11:45:43.118 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 11:45:43.331 [main] INFO  org.reflections.Reflections - Reflections took 81 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 11:45:43.520 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:45:43.603 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 11:45:43.611 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 11:45:43.720 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:45:43.722 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@6c978e86
2025-03-19 11:45:44.615 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 11:46:06.772 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 11:46:06.773 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 11:46:06.773 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 11:46:06.774 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 11:46:06.775 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 11:46:06.794 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:46:07.710 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:46:07.717 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 11:46:08.716 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 11:46:08.755 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 11:46:08.766 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 11:46:08.766 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 11:46:08.766 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 11:46:08.767 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 11:46:08.767 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 11:46:08.767 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 11:46:08.768 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 11:46:08.814 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:46:08.815 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 11:46:08.844 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 11:46:09.716 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6b382f2f: tags=[[amq.ctag-bXTaRbKCOeVVlKxdpNrunA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@438c3ce7 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:46:09.716 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@9fe99b2f: tags=[[amq.ctag-CoTjHGtwvxhY4EJWuj11Qg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@438c3ce7 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 11:46:09.967 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 11:46:09.968 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 11:46:09.968 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 13:30:38.127 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 13:30:38.137 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 13:30:38.482 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 13:30:38.515 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@159d6b42, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7ac0f06c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@f857676d, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@38e16c26, org.springframework.test.context.support.DirtiesContextTestExecutionListener@9cea2858, org.springframework.test.context.transaction.TransactionalTestExecutionListener@a1978015, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@64e12b4d, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@7a27792e, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@47a36872, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@5fab1d65, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@671ccbfb, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1d5a235b]
2025-03-19 13:30:39.516 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 13:30:39.520 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 13:30:40.399 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1d7547cf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:41.270 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 13:30:41.875 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 13:30:41.897 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 13:30:45.933 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 13:30:46.384 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 13:30:46.389 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 13:30:46.478 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 67ms. Found 0 repository interfaces.
2025-03-19 13:30:46.597 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 13:30:46.636 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 13:30:47.412 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 13:30:48.123 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$69215efe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$1675031c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.480 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.659 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.667 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.689 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.689 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$af5f3d60] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.716 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$c7c70300] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:48.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$369c3e7d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:49.133 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$3c9f52ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:49.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:49.587 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 13:30:49.594 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 13:30:50.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.084 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.120 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.139 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.144 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.150 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.155 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.156 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.207 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$1d7547cf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:30:50.766 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 13:30:50.767 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 13:30:50.784 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@e31666c8
2025-03-19 13:30:51.371 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 13:30:51.467 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#802f6315:0/SimpleConnection@f2313fd8 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 55418]
2025-03-19 13:30:54.434 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 13:30:54.659 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 13:30:54.848 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 13:30:57.208 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 13:30:58.194 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 13:30:58.211 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 13:30:58.367 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 13:30:58.724 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 13:30:58.724 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 13:30:59.996 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 13:31:02.214 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 13:31:02.253 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 13:31:02.256 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 13:31:02.290 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 13:31:02.381 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 13:31:02.973 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 13:31:03.042 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 13:31:03.084 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 13:31:03.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 13:31:03.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 13:31:03.131 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 13:31:03.150 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 13:31:03.157 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 13:31:03.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 13:31:03.302 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 13:31:03.677 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 13:31:03.827 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 25.213 seconds (JVM running for 51.084)
2025-03-19 13:31:03.994 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 13:31:03.994 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 13:31:03.995 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 1 ms
2025-03-19 13:31:04.297 [main] INFO  org.reflections.Reflections - Reflections took 103 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 13:31:04.526 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:31:04.647 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 13:31:04.661 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 13:31:04.868 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:31:04.871 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@154b5239
2025-03-19 13:31:05.673 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:33:42.936 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 13:33:42.935 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 13:33:42.933 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 13:33:42.976 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 13:33:42.982 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 13:33:43.234 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 13:33:43.829 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 13:33:43.835 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 13:33:43.866 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 13:33:43.993 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 13:33:44.001 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 13:33:44.002 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 13:33:44.003 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 13:33:44.005 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 13:33:44.006 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 13:33:44.007 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 13:33:44.008 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 13:33:44.079 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 13:33:44.079 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 13:33:44.121 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 13:33:44.823 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@b588af52: tags=[[amq.ctag-STCtn8BlctP2RCA7ph_kcw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@128ccd00 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 13:33:44.823 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@1a141417: tags=[[amq.ctag-oRiL_QiXhplGU1egYFJvKA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@128ccd00 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 13:33:45.273 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 13:33:45.278 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 13:33:45.279 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-19 13:33:54.010 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-19 13:33:54.019 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-19 13:33:54.304 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-19 13:33:54.332 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@b3091925, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@ff524794, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@f33fee3b, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@d5413f99, org.springframework.test.context.support.DirtiesContextTestExecutionListener@9ef654c, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1558759e, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@433e3a88, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@5b4bfe09, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@182bf83e, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@ac30d597, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@b8a74018, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@12182c3b]
2025-03-19 13:33:55.055 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-19 13:33:55.059 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-19 13:33:55.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8c6cf819] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:33:56.409 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-19 13:33:56.834 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-19 13:33:56.843 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-19 13:33:58.654 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-19 13:33:59.056 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-19 13:33:59.060 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-19 13:33:59.136 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 60ms. Found 0 repository interfaces.
2025-03-19 13:33:59.227 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-19 13:33:59.279 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-19 13:33:59.639 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-19 13:33:59.883 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$d8190f48] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:33:59.899 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:33:59.907 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$856cb366] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.091 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.214 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.220 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.239 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.239 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$1e56edaa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.260 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$36beb34a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.372 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$a593eec7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.540 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$ab970304] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.560 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:00.795 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-19 13:34:00.798 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-19 13:34:01.195 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.215 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.238 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.250 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.253 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.266 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.300 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8c6cf819] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-19 13:34:01.679 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 13:34:01.679 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 13:34:01.694 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@d794d619
2025-03-19 13:34:02.200 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-19 13:34:02.284 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#9cab9327:0/SimpleConnection@1ec4df71 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 55752]
2025-03-19 13:34:05.348 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-19 13:34:05.514 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-19 13:34:05.675 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-19 13:34:07.159 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-19 13:34:07.732 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-19 13:34:07.745 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-19 13:34:07.800 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-19 13:34:07.943 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-19 13:34:07.943 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-19 13:34:08.579 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-19 13:34:10.311 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-19 13:34:10.337 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-19 13:34:10.340 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-19 13:34:10.364 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-19 13:34:10.422 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-19 13:34:10.726 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-19 13:34:10.755 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-19 13:34:10.779 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-19 13:34:10.781 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-19 13:34:10.800 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-19 13:34:10.803 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-19 13:34:10.817 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-19 13:34:10.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-19 13:34:10.840 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-19 13:34:10.907 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-19 13:34:11.008 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-19 13:34:11.097 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 16.686 seconds (JVM running for 17.978)
2025-03-19 13:34:11.195 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-19 13:34:11.195 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-19 13:34:11.195 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-19 13:34:11.343 [main] INFO  org.reflections.Reflections - Reflections took 55 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-19 13:34:11.497 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:34:11.556 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-19 13:34:11.562 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-19 13:34:11.644 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:34:11.645 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@57998df1
2025-03-19 13:34:12.576 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2024-03-01
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2024-06-01
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2024-05-01
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2024-08-21
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2025-07-31
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2014-12-20
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2024-05-06
2025-03-19 13:34:12.858 [main] WARN  c.c.p.a.service.BonusReportService - Failed to parse date value: 2025-02-21
2025-03-19 13:34:13.782 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-19 13:34:13.782 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-19 13:34:13.783 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-19 13:34:13.784 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-19 13:34:13.785 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-19 13:34:13.794 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 13:34:13.993 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 13:34:13.998 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-19 13:34:14.019 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-19 13:34:14.037 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-19 13:34:14.044 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-19 13:34:14.045 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-19 13:34:14.045 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-19 13:34:14.045 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-19 13:34:14.045 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-19 13:34:14.045 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-19 13:34:14.045 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-19 13:34:14.070 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 13:34:14.070 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-19 13:34:14.086 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-19 13:34:14.127 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@d5e5cc4f: tags=[[amq.ctag-GkYewHktCD4s2WRQMso_Ew]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@cbd2c190 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 13:34:15.088 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6a4e6641: tags=[[amq.ctag-K1B09jXqiUZkEC6-5p9jug]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@cbd2c190 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-19 13:34:15.233 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-19 13:34:15.233 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-19 13:34:15.234 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!

package com.caidaocloud.pangu.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
public enum ExecutionType {
	ALL("整体执行", "0"),
	SINGLE("单个执行","1") ;

	private String text;

	private final String code;

	ExecutionType(String text,String code) {
		this.text = text;this.code=code;
	}

	public  EnumSimple toValue(){
		EnumSimple enumSimple = new EnumSimple();
		enumSimple.setValue(getCode());
		return enumSimple;
	};

	public String getCode() {
		return code;
	}
}

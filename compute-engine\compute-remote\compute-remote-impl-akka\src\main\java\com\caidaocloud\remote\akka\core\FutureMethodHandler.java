package com.caidaocloud.remote.akka.core;

import java.time.Duration;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;

import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import akka.pattern.Patterns;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import lombok.extern.slf4j.Slf4j;

import static com.caidaocloud.remote.akka.constant.AkkaConstant.REMOTE_REQUEST_TIMEOUT;

/**
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Slf4j
public class FutureMethodHandler extends AkkaMethodHandler {


	public FutureMethodHandler(ActorSystem actorSystem, MethodMetadata methodMetadata) {
		super(actorSystem, methodMetadata);
	}

	@Override
	Object doExecute(ActorSelection actorSelection, ActorMessageWrapper request) {
		CompletionStage<Object> askRet = Patterns.ask(actorSelection, request, Duration.ofMillis(REMOTE_REQUEST_TIMEOUT));
		try {
			return askRet.toCompletableFuture().get(REMOTE_REQUEST_TIMEOUT, TimeUnit.MILLISECONDS);
		}
		catch (Exception e) {
			log.error("akka async remote request occurs error,{}", e.getMessage());
			throw new ServerException("akka async remote request occurs error", e);
		}
	}
}

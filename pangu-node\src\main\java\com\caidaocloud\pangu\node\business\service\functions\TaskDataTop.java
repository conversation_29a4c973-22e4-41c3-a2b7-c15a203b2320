package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.core.ignite.KeyValueDto;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.common.feign.PanguManagerFeign;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TaskDataTop extends AbstractVariadicFunction {

    //MODEL_DATA_TOP_ORDER( identifier, property, filter, orderBy, order)

    @Override
    public String getName() {
        return "TASK_DATA_TOP";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {

        val rule = FunctionUtils.getStringValue(aviatorObjects[0], env).replace("ruleelur_", "");
        val scTaskDefId = StringUtils.substringBefore(rule, "#");
        val property = StringUtils.substringAfter(rule, "#");
        val filter = (DataFilter) aviatorObjects[1].getValue(env);

//        val scTaskDefId = FunctionUtils.getStringValue(aviatorObjects[0], env);
//        val property = FunctionUtils.getStringValue(aviatorObjects[1], env);
//        val filter = (DataFilter) aviatorObjects[2].getValue(env);
        val orderBy = "id";
        val order = "ASC";

        val execSeqId = (String)env.get("$.env.execSeqId");
        val arrangementVid = (String)env.get("$.env.arrangementVid");
        val arrangementDetail = Arrangement.Detail.loadVersion(arrangementVid);
        AviatorObject result;
        val scNode = arrangementDetail.getNodes().stream().filter(it->scTaskDefId.equals(it.getTaskDefId())).findFirst().orElseThrow(()->new ServerException("load task not exist"));
        String scCacheId = SpringUtil.getBean(PanguManagerFeign.class).getResultCacheId(execSeqId, scNode.getId()).getData();

        List<List<KeyValueDto>> dataList = SpringUtil.getBean(IgniteUtil.class).getContext(scCacheId).loadProperties(filter, Lists.list(property), Lists.list(orderBy + " " + order),
                0, 1);
        TaskDef.RuleDataType ruleDataType = (TaskDef.RuleDataType) env.get("$.env.ruleDataType");
        val isNum = TaskDef.RuleDataType.Int.equals(ruleDataType) || TaskDef.RuleDataType.Number.equals(ruleDataType);
        if(dataList.isEmpty()){
            if(isNum){
                result = new AviatorDecimal(0);
            }else{
                result = new AviatorString("");
            }
        }else{
            val propertyValue = dataList.get(0).get(1);
            val value = propertyValue.getValue();
            if(isNum){
                if(value == null){
                    result = new AviatorDecimal(0);
                }else{
                    result = new AviatorDecimal(new BigDecimal(String.valueOf(value)));
                }
            }else{
                if(value == null){
                    result = new AviatorString("");
                }else{
                    result = new AviatorString(String.valueOf(value));
                }
            }
        }
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(scTaskDefId).append(",")
                    .append(property).append(",").append(FastjsonUtil.toJson(filter))
                    .append(")=").append(result.getValue(env)).toString());
        }
        return result;
    }
}

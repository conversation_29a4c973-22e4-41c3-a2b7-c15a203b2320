package com.caidaocloud.pangu.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerFinalStep;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Sequences;

import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FinalStepExecSubscriber {

    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = BonusConstant.BONUS_FINAL_STEP_EXEC_QUEUE, durable = "true"),
                    exchange = @Exchange(value = BonusConstant.BONUS_DIRECT_EXCHANGE),
                    key = {BonusConstant.BONUS_FINAL_STEP_EXEC_ROUTINGKEY}
            )
    )
    @RabbitHandler
    public void receive(String msg) {
        FinalStepExecMessageDTO event = FastjsonUtil.toObject(msg, FinalStepExecMessageDTO.class);
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            
            // Use Spring bean to ensure transaction works properly
            SpringUtil.getBean(FinalStepExecSubscriber.class).processWithTransaction(event);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
    
    @PaasTransactional
    public void processWithTransaction(FinalStepExecMessageDTO event) {
        log.info("Processing final step execution for ledgerId: {}, stepId: {}", 
                event.getLedgerId(), event.getStepId());
                
        // Load the final step and execute it
        BonusLedgerFinalStep finalStep = (BonusLedgerFinalStep) Sequences.sequence(BonusLedger.load(event.getLedgerId()).loadStep())
                .find(s -> event.getStepId().equals(s.getBid())).get();
        finalStep.doExec();
        
        log.info("Final step execution completed for stepId: {}", event.getStepId());
    }
} 
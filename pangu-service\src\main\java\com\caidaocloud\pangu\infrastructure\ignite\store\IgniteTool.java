// package com.caidaocloud.pangu.infrastructure.ignite.store;
//
// import com.caidaocloud.util.SpringUtil;
// import org.apache.ignite.Ignite;
// import org.apache.ignite.IgniteCache;
//
// public class IgniteTool {
//
//     public static IgniteCache getOrCreateCache(String cacheName){
//         return SpringUtil.getBean(Ignite.class).getOrCreateCache(
//                 SpringUtil.getBean(IgniteDbStoreConfigurationFactory.class)
//                         .createCacheConfiguration(cacheName)
//         );
//     }
//
// }

package com.caidaocloud.pangu.domain;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.pangu.application.dto.BonusStructItemDto;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2025/1/22
 */
public class BonusStructFactory {


	public static BonusStruct create(Map<String, String> i18nMap) {
		BonusStruct bonusStruct = new BonusStruct(i18nMap);
		bonusStruct.setBasic(defaultWorkInfo());
		return bonusStruct;
	}

	public static BonusStructItem createItem(BonusStructItemDto dto, Long itemNo) {
		BonusStructItem item = ObjectConverter.convert(dto, BonusStructItem.class);
		DictSimple dictSimple = new DictSimple();
		dictSimple.setValue(dto.getType());
		item.setCode(formatItemCode(dto.getStructId(), itemNo));
		item.setType(dictSimple);
		return item;
	}

	private static String formatItemCode(String structId, Long itemNo) {
		LocalDate currentDate = LocalDate.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
		String formattedDate = currentDate.format(formatter);
		return "J" + StringUtils.right(structId, 5) + formattedDate + String.format("%05d", itemNo);
	}

	private static List<String> defaultWorkInfo(){
		return Lists.list(
				"entity.hr.EmpWorkInfo@workno",
				"entity.hr.EmpWorkInfo@name",
				"entity.hr.EmpWorkInfo@companyTxt",
				"entity.hr.EmpWorkInfo@organizeTxt",
				"entity.hr.EmpWorkInfo@postTxt",
				"entity.hr.EmpWorkInfo@empType",
				"entity.hr.EmpWorkInfo@hireDate",
				"entity.hr.EmpWorkInfo@confirmationDate",
				"entity.hr.EmpWorkInfo@leaveDate"
		);
	}
}

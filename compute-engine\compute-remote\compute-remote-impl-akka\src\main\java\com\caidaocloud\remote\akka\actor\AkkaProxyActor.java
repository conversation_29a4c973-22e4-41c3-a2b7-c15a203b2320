package com.caidaocloud.remote.akka.actor;

import java.lang.reflect.Method;
import java.util.Optional;

import akka.actor.AbstractActor;
import akka.actor.Props;
import akka.actor.Status;
import akka.japi.pf.ReceiveBuilder;
import com.caidaocloud.compute.remote.framework.actor.ActorInfo;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import com.caidaocloud.compute.remote.framework.actor.HandlerInfo;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AkkaProxyActor extends AbstractActor {

    private final ActorInfo actorInfo;

    public static Props props(ActorInfo actorInfo) {
        return Props.create(AkkaProxyActor.class, () -> new AkkaProxyActor(actorInfo));
    }

    public AkkaProxyActor(ActorInfo actorInfo) {
        this.actorInfo = actorInfo;
    }

    @Override
    public Receive createReceive() {
        ReceiveBuilder receiveBuilder = receiveBuilder();
        actorInfo.getHandlerInfos().forEach(handlerInfo -> {
            final Method handlerMethod = handlerInfo.getMethod();
            receiveBuilder.match(ActorMessageWrapper.class, msg -> handlerMethod.getName()
                    .equals(msg.getMethodName()), req -> onReceiveProcessorReportTaskStatusReq(req, handlerInfo));
        });
        return receiveBuilder.build();
    }

    private <T> void onReceiveProcessorReportTaskStatusReq(T req, HandlerInfo handlerInfo) {

        try {
            ActorMessageWrapper message = (ActorMessageWrapper) req;
            final Object ret = handlerInfo.getMethod().invoke(actorInfo.getActor(), message.getArgs());
            if (ret == null) {
                return;
            }
            if (ret instanceof Optional) {
                if (!((Optional<?>) ret).isPresent()) {
                    return;
                }
            }
            getSender().tell(ret, getSelf());
        } catch (Exception e) {
            log.error("[remote-AKKA] process failed!", e);
            getSender().tell(new Status.Failure(e), getSelf());
        }
    }

}
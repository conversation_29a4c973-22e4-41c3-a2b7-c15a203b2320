package com.caidaocloud.compute.remote.framework.actor;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.springframework.beans.factory.config.BeanDefinition;

/**
 *
 * <AUTHOR>
 * @date 2024/10/23
 */
@Data
@NoArgsConstructor
public class ActorInfo {

	private Object actor;
	private RemoteHandler definition;

	private String path;
	private List<HandlerInfo> handlerInfos;

	public ActorInfo(Object actor, RemoteHandler definition) {
		this.actor = actor;
		this.definition = definition;
		this.path = definition.value();
	}
}

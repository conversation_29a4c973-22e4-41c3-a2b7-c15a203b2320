package com.caidaocloud.pangu.manager.feign;

import com.caidaocloud.pangu.manager.annotations.FeignThrow;
import com.caidaocloud.pangu.manager.dto.ArrangementDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "${feign.rename.caidaocloud-user-service:caidaocloud-user-service}", fallback = UserFeignFallback.class, configuration = FeignConfiguration.class, contextId = "userFeign")
public interface UserFeign {

    @GetMapping("/api/user/v1/getUserAndAccountInfo")
    @FeignThrow
    Result<Map> getUserAndAccountInfo(@RequestParam Long userId);
}

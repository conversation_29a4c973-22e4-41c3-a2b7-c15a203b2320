package com.caidaocloud.pangu.application.event;

import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EmployeeStatsMessageDTO {
    private String ledgerId;
    private String tenantId;

    public EmployeeStatsMessageDTO(String ledgerId) {
        this.ledgerId = ledgerId;
        this.tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
    }
}
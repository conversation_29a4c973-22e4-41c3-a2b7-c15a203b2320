package com.caidaocloud.pangu.interfaces.vo;


import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("奖金报表--元数据定义")
public class BonusMetadataVo {
    @ApiModelProperty("业务模型全限定名")
    private String identifier;
    @ApiModelProperty("业务模型名称")
    private String name;
    private Map<String, String> i18nName;
    @ApiModelProperty("标准属性")
    private List<MetadataPropertyVo> standardProperties;
}

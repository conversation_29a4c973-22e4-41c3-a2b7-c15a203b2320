package com.caidaocloud.pangu.node.business.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class TaskProcessorConfiguration {

    @Value("${processors:10}")
    private int processorsCount;

    public static AtomicInteger availableProcessCount;

    @Bean("taskProcessors")
    public ExecutorService taskProcessors(){
        availableProcessCount = new AtomicInteger(processorsCount);
        return Executors.newFixedThreadPool(processorsCount);
    }
}

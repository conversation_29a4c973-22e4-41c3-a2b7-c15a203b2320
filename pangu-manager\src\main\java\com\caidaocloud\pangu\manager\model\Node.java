package com.caidaocloud.pangu.manager.model;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Slf4j
public class Node {

    private String id;

    private String host;

    private String port;

    private int processors;

    private int idle;

    private long lastHeartbeatTime;

    private NodeStatus nodeStatus;

    public static List<Node> fetchNodeList() {
        String nodeStr = SpringUtil.getBean(CacheService.class).getValue("pangu_node_list");
        if(StringUtils.isEmpty(nodeStr)){
            return Lists.list();
        }
        return FastjsonUtil.toList(nodeStr, Node.class);
    }

    public static void cacheNodeList(List<Node> nodeList) {
        SpringUtil.getBean(CacheService.class).cacheValue("pangu_node_list",
                FastjsonUtil.toJson(nodeList));
        log.info("fresh nodelist to cache success " + FastjsonUtil.toJson(nodeList));
    }

    public enum NodeStatus{
        ALIVE, SUSPEND
    }
}

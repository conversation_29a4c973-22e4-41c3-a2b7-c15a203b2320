package dynamic;

import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.util.FastjsonUtil;
import lombok.SneakyThrows;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.binary.BinaryObject;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.internal.binary.BinaryObjectImpl;
import org.junit.Test;

public class IgniteClientTest {

    @SneakyThrows
    @Test
    public void it() {
            IgniteConfiguration configuration = new IgniteConfiguration();
            configuration.setPeerClassLoadingEnabled(true);
        configuration.setClientMode(true);
            configuration.setDeploymentMode(DeploymentMode.CONTINUOUS);
            configuration.setWorkDirectory("C:\\ignite_node");
            try (Ignite ignite = Ignition.start(configuration);) {
                IgniteCache tmp = ignite.getOrCreateCache("tmp_blob");
                IgniteCache cache = tmp.withKeepBinary();
                BinaryObjectImpl object = (BinaryObjectImpl) cache.get("1");
                System.out.println(FastjsonUtil.toJson(object));
            }

    }}



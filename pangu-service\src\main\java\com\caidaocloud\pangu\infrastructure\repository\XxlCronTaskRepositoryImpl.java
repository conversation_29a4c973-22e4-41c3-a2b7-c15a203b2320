package com.caidaocloud.pangu.infrastructure.repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.pangu.application.dto.cron.CronTaskDto;
import com.caidaocloud.pangu.application.dto.cron.XxlJobDto;
import com.caidaocloud.pangu.domain.repository.ICronTaskRepository;
import com.caidaocloud.pangu.infrastructure.util.RestTemplateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.jarvis.cache.annotation.Cache;
import com.xxl.job.core.biz.model.ReturnT;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/18
 */
@Repository
public class XxlCronTaskRepositoryImpl implements ICronTaskRepository {
	@Autowired
	private CacheService cacheService;

	@Value("${xxl.job.admin.addresses}")
	private String addresses;

	@Value("${xxl.job.admin.login.username:admin}")
	private String username;

	@Value("${xxl.job.admin.login.pass:123456}")
	private String pass;

	@Value("${spring.application.name}")
	private String appName;

	public static final String XXL_COOKIE_KEY = "pangu-xxl-cookie";
	public static final String XXL_EXEC_ID_KEY = "pangu-xxl-exec-id";
	public static final String XXL_HANDLER = "IndicatorCronTaskHandler";
	public static final String TYPE = "XXL-JOB";

	public String getXxlCookie() {
		String cookie = cacheService.getValue(XXL_COOKIE_KEY);
		if (StringUtils.isEmpty(cookie)) {
			Map<String, Object> param = new HashMap<>();
			param.put("userName", username);
			param.put("password", pass);
			param.put("ifRemember", "on");
			ResponseEntity response = RestTemplateUtil.postFormDataResp(addresses, "/login", null, param, ReturnT.class);
			List<String> list = response.getHeaders().get("Set-Cookie");
			for (String c : list.get(0).split(";")) {
				c = c.trim();
				if (c.startsWith("XXL_JOB_LOGIN_IDENTITY")) {
					cookie = c;
					break;
				}
			}
			if (StringUtils.isEmpty(cookie)) {
				throw new ServerException("Failed to get cookie from xxl-job");
			}
			cacheService.cacheValue(XXL_COOKIE_KEY, cookie, 600);
		}
		return cookie;
	}

	public Integer getExecutorId() {
		String execId = cacheService.getValue(XXL_EXEC_ID_KEY);
		if (StringUtils.isEmpty(execId)) {
			Map<String, Object> param = new HashMap<>();
			param.put("appname", appName);
			Map res = RestTemplateUtil.postFormData(addresses, "/jobgroup/pageList", getXxlCookie(), param, Map.class);
			List data = ((ArrayList) res.get("data"));
			if (CollectionUtils.isEmpty(data)) {
				throw new ServerException("Failed to get exec id from xxl-job");
			}
			execId = String.valueOf(((LinkedHashMap) data.get(0)).get("id"));
			cacheService.cacheValue(XXL_EXEC_ID_KEY, execId);
		}
		return Integer.valueOf(execId);
	}

	@Override
	public String registerTask(CronTaskDto cronTask, String desc,String cron) {
		String param = FastjsonUtil.toJson(cronTask);
		XxlJobDto xxlJobDto = new XxlJobDto(getExecutorId(), desc, cron, XXL_HANDLER, param);
		ReturnT<String> result = RestTemplateUtil.postFormData(addresses, "/jobinfo/add", getXxlCookie(), JsonEnhanceUtil.toObject(xxlJobDto, Map.class), ReturnT.class);
		return result.getContent();
	}

	@Override
	public void cancelTask(String taskId) {
		Map<String, Object> param = new HashMap<>();
		param.put("id", taskId);
		RestTemplateUtil.postFormData(addresses, "/jobinfo/remove", getXxlCookie(), param, ReturnT.class);
	}

	@Override
	public void editTask(String cronTaskId, CronTaskDto dto, String name, String cron) {
		String param = FastjsonUtil.toJson(dto);
		XxlJobDto xxlJobDto = new XxlJobDto(getExecutorId(), name, cron, XXL_HANDLER, param);
		xxlJobDto.setId(Integer.parseInt(cronTaskId));
		RestTemplateUtil.postFormData(addresses, "/jobinfo/update", getXxlCookie(), JsonEnhanceUtil.toObject(xxlJobDto, Map.class), ReturnT.class);
	}
}


package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.pangu.domain.repository.IBonusStructItemRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 * 奖金结构--奖金项配置
 */
@Data
public class BonusStructItemVo {
    private String bid;
    private String structId;
    private String name;
    private String code;
    private DictSimple type;
    private String valueIdentifier;
    private String valueProperty;
    private String worknoProperty;
    private String monthProperty;
    private String remark;


}

package com.caidaocloud.pangu.infrastructure.repository;

import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.domain.repository.IBonusReportRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepositoryImpl;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;

import org.springframework.stereotype.Repository;

/**
 * BonusReport Repository Implementation
 *
 * <AUTHOR>
 * @date 2024/1/23
 */
@Repository
public class BonusReportRepositoryImpl extends BaseRepositoryImpl<BonusReport> implements IBonusReportRepository {

    private static List<String> ledgerFields = Lists.list("ledgerId");

    @Override
    public PageResult<BonusReport> selectPageByLedgerId(String ledgerId, BonusReportDto queryDto) {
        DataJoin.ModelInfo workInfo = workInfo(queryDto);
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("ledgerId", ledgerId);
        return ledgerEmpModel(queryDto, workInfo, filter);
    }

    @NotNull
    private PageResult<BonusReport> ledgerEmpModel(BonusReportDto queryDto, DataJoin.ModelInfo workInfo, DataFilter filter) {
        filter = (DataFilter) queryDto.doDataFilter(Sequences.sequence(queryDto.getFilters())
                .filter(fe -> ledgerFields.contains(fe.getProp())).toList(), filter);
        DataJoin.ModelInfo ledgerEmp = DataJoin.ModelInfo.model(BonusReport.BONUS_REPORT_IDENTIFIER, Lists.list("bid", "ledger_id", "emp_id", "month","item"), filter);

        DataJoin join = DataJoin.joinModels(ledgerEmp,workInfo,
                DataJoin.JoinInfo.joinInfo(Lists.list(DataJoin.JoinPropertyInfo.joinProperty(ledgerEmp.getIdentifier(), workInfo.getIdentifier(), "empId", "empId"))));
        join.limit(queryDto.getPageSize(), queryDto.getPageNo());
        PageResult<Triple<BonusReport, BonusReport, BonusReport>> result = join.join(BonusReport.class, System.currentTimeMillis());
        return new PageResult<>(Sequences.sequence(result.getItems()).map(Triple::getLeft)
                .toList(), result.getPageNo(), result.getPageSize(), result.getTotal());
    }

    @Override
    public PageResult<BonusReport> selectPageByStructId(String structId, String startMonth, String endMonth, BonusReportDto dto) {
        DataJoin.ModelInfo workInfo = workInfo(dto);
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("structId", structId)
                .andGe("month", startMonth)
                .andLe("month", endMonth);
        return ledgerEmpModel(dto, workInfo, filter);
    }

    @Override
    public void saveReport(List<BonusReport> bonusReports) {
        for (BonusReport bonusReport : bonusReports) {
            // bonusReport.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            DataInsert.identifier(BonusReport.BONUS_REPORT_IDENTIFIER).insert(bonusReport);
        }
        // DataInsert.identifier(BonusReport.BONUS_REPORT_IDENTIFIER).batchInsert(bonusReports);
    }

    @Override
    public void deleteByLedgerId(String ledgerId) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("ledgerId", ledgerId);
        DataDelete.identifier(BonusReport.BONUS_REPORT_IDENTIFIER).batchDelete(filter);
    }

    @Override
    public void removeEmpBatch(String bid, List<String> empId) {
        DataDelete.identifier(BonusReport.BONUS_REPORT_IDENTIFIER)
                .batchDelete(DataFilter.eq("ledgerId", bid).andIn("empId", empId));
    }

    @Override
    public int countByLedgerId(String bid) {
        DataFilter filter = DataFilter.eq("ledgerId", bid)
                .andNe("deleted", Boolean.TRUE.toString());
        long count = DataQuery.identifier(BonusReport.BONUS_REPORT_IDENTIFIER)
                .count(filter, System.currentTimeMillis());
        return (int) count;
    }

    private DataJoin.ModelInfo workInfo(BonusReportDto queryDto) {
        DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());
        filter = (DataFilter) queryDto.doDataFilter(Sequences.sequence(queryDto.getFilters())
                .filter(fe -> !ledgerFields.contains(fe.getProp())).toList(), filter);
                // .andEqIf("organize", queryDto.getOrganize(), () -> StringUtils.isNotEmpty(queryDto.getOrganize()))
                // .andEqIf("company", queryDto.getCompany(), () -> StringUtils.isNotEmpty(queryDto.getCompany()))
                // .andEqIf("empType$dict$value", queryDto.getEmpType(), () -> StringUtils.isNotEmpty(queryDto.getEmpType()))
                // .andEqIf("workPlace", queryDto.getWorkPlace(), () -> StringUtils.isNotEmpty(queryDto.getWorkPlace()));
        if (StringUtils.isNotEmpty(queryDto.getKeyword())){
            filter=filter.and(DataFilter.regex("workno", queryDto.getKeyword()).orRegex("name", queryDto.getKeyword()));
        }
        return DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", Lists.list(), filter);
    }
} 
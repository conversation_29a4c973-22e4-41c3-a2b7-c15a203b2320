package com.caidaocloud.pangu.manager;

import com.caidaocloud.compute.remote.framework.scan.EnableRemote;

import com.caidaocloud.pangu.core.ignite.config.StaticIpConfig;
import com.caidaocloud.pangu.core.ignite.config.StaticIpConfigurer;
import com.caidaocloud.pangu.manager.service.ArrangementExecService;
import com.caidaocloud.pangu.manager.service.ArrangementScheduleService;
import com.caidaocloud.pangu.manager.service.NodeManagerService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.ignite.springframework.boot.autoconfigure.IgniteConfigurer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/6
 */
@SpringBootApplication
@EnableCircuitBreaker
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.caidaocloud.pangu.manager.feign","com.caidaocloud.pangu.core.feign"})
@EnableRemote
@ComponentScan(basePackages = {"com.caidaocloud.pangu",
		"com.caidaocloud.config", "com.caidaocloud.metadata"},
		excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
				classes = {}))
@Slf4j
public class PanguManagerApplication extends SpringBootServletInitializer {

	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(PanguManagerApplication.class, appendArgs(args));
		try{
			// IgniteCacheContext<String, TaskDispatchDetail> context1 = SpringUtil.getBean(DispatchTool.class)
			// 		.getDispatchContext();
			// context1.put("1", new TaskDispatchDetail("1"));
			// context1.count(DataFilter.eq("execNodeSeqId", "12")
			// 		.andEq("from", "0").andEq("to", "0"), "execNodeSeqId");
			log.info("init node list");
			context.getBean(NodeManagerService.class).initNodeList(context);
			log.info("init node list complete");
			context.getBean(ArrangementScheduleService.class).scheduleArrangementNode();
		}catch (Exception e){
			log.error("init error", e);
		}


	}

	private static String[] appendArgs(String[] args){
		List<String> springParams =
				Lists.newArrayList(
						"spring.cloud.nacos.discovery.server-addr",
						"spring.cloud.nacos.discovery.username",
						"spring.cloud.nacos.discovery.password",
						"spring.cloud.nacos.discovery.namespace",

						"nacos.config.server-addr",
						"nacos.config.username",
						"nacos.config.password",
						"nacos.config.namespace",
						"logging.config");
		val allArgs = springParams.stream().map(it->{
			String value = System.getProperty(it);
			if(value == null){
				return null;
			}else{
				return "--" + it + "=" + value;
			}
		}).filter(it->it != null).collect(Collectors.toList());
		if(null != args && args.length > 0){
			allArgs.addAll(Lists.newArrayList(args));
		}
		return allArgs.toArray(new String[allArgs.size()]);
	}

	@Bean
	public IgniteConfigurer igniteConfigurer(StaticIpConfig config){
		return new StaticIpConfigurer(config);
	}
}

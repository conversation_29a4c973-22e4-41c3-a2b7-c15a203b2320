package com.caidaocloud.pangu.infrastructure.repository;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.BonusStructCompose;
import com.caidaocloud.pangu.domain.repository.IBonusStructComposeRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepositoryImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/23
 */
@Repository
public class BonusStructComposeRepositoryImpl extends BaseRepositoryImpl<BonusStructCompose> implements IBonusStructComposeRepository  {
    @Override
    public List<BonusStructCompose> selectList(String structId) {
        return DataQuery.identifier(BonusStructCompose.BONUS_STRUCT_COMPOSE_IDENTIFIER)
                .limit(-1, 1)
                .filter(DataFilter.ne("deleted", Boolean.TRUE.toString()).andEq("structId", structId), BonusStructCompose.class,"sort,bid asc", System.currentTimeMillis()).getItems();
    }
}

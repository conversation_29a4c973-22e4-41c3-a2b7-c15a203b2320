package com.caidaocloud.pangu.application.dto.cron;

import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
@Data
@AllArgsConstructor
public class CronTaskDto {
	private String indicatorBid;
	private String tenantId;

	public CronTaskDto(String indicatorBid) {
		this.indicatorBid = indicatorBid;
		tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
	}
}

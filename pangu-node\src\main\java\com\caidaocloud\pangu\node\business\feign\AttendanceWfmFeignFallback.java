package com.caidaocloud.pangu.node.business.feign;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.node.business.dto.ArrangeMonthAnalyze;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class AttendanceWfmFeignFallback implements AttendanceWfmFeign {
    @Override
    public Result<PageResult<ArrangeMonthAnalyze>> getEmpArrangeMonthAnalyzeList(long startDate, long endDate, int pageNo, int pageSize, String accountId) {
        return Result.fail();
    }

    @Override
    public Result<PageResult<ArrangeMonthAnalyze>> getEmpNonProcessArrangeMonthAnalyzeList(long startDate, long endDate, int pageNo, int pageSize, String accountId) {
        return Result.fail();
    }

    @Override
    public Result<PageResult<ArrangeMonthAnalyze>> getArrangeMonthAnalyzeList(long startDate, long endDate, int pageNo, int pageSize, String accountId) {
        return Result.fail();
    }
}

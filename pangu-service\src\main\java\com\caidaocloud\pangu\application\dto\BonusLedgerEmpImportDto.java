package com.caidaocloud.pangu.application.dto;

import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/2/17
 */
@Data
public class BonusLedgerEmpImportDto {
		@Excel(name="工号", width = 15, orderNum = "1")
		private String workno;

		private String empId;

		@Excel(name="姓名", width = 20, orderNum = "2")
		private String name;

		private boolean checkFailFlag = false;

		@Excel(name = "错误原因", width = 30, orderNum = "3")
		private String checkFailTips;

	public void addEmpPropTip(String tip) {
		checkFailFlag = true;
		if(null==checkFailTips){
			checkFailTips = tip;
		}else {
			checkFailTips += "，" + tip;
		}
	}
}

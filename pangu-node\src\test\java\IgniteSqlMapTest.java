import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.pangu.node.PanguNodeApplication;
import com.googlecode.totallylazy.Maps;
import dynamic.ByteBuddyFieldGetterSetterExample;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.implementation.FieldAccessor;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheMode;
import org.apache.ignite.cache.CachePeekMode;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.QueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.internal.binary.BinaryObjectImpl;
import org.apache.ignite.lang.IgniteBiPredicate;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/8
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguNodeApplication.class)
public class IgniteSqlMapTest {
	@Autowired
	private Ignite ignite;
	static CacheConfiguration cfg ;
	static IgniteConfiguration igniteCfg;
	static String cache_name = "PersonCache";

	static {
		// 创建动态类
		DynamicType.Unloaded<?> dynamicType = new ByteBuddy()
				.subclass(Object.class) // 继承自 Object 类
				.name("com.example.generated.Person") // 设置类名
				// 添加字段：name
				.defineField("id", int.class) // 添加字段：age
				// 添加 getter 和 setter 方法
				.defineField("name", String.class)
				.defineMethod("getName", String.class, Modifier.PUBLIC) // getter 方法
				.intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
				.defineMethod("setName", void.class,Modifier.PUBLIC) // setter 方法
				.withParameters(String.class) // 接受 String 类型参数
				.intercept(FieldAccessor.ofBeanProperty()) // 不做操作，只生成方法
				.defineMethod("getId", int.class,Modifier.PUBLIC) // getter 方法
				.intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
				.defineMethod("setId", void.class,Modifier.PUBLIC) // setter 方法
				.withParameters(int.class) // 接受 int 类型参数
				.intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
				.make(); // 生成类
		Class<?> dynamicClass = dynamicType.load(ByteBuddyFieldGetterSetterExample.class.getClassLoader()).getLoaded();

		igniteCfg = new IgniteConfiguration();
		 cfg = new CacheConfiguration();

		cfg.setName(cache_name);
		cfg.setCacheMode(CacheMode.PARTITIONED);
		cfg.setAtomicityMode(CacheAtomicityMode.ATOMIC);

		cfg.setReadThrough(true);
		cfg.setWriteThrough(true);

		CacheJdbcPojoStoreFactory<Integer, Map> factory = new CacheJdbcPojoStoreFactory<>();
		factory.setDialect(new BasicJdbcDialect());
		factory.setDataSourceFactory(IgniteThickClientTest.getDataSourceFactory());

		JdbcType personType = new JdbcType();
		personType.setCacheName(cache_name);
		personType.setDatabaseTable("PERSON");
		personType.setKeyType(Integer.class);
		personType.setValueType(dynamicClass);
// Specify the schema if applicable
// personType.setDatabaseSchema("MY_DB_SCHEMA");

		personType.setKeyFields(new JdbcTypeField(java.sql.Types.INTEGER, "id", Integer.class, "id"));
		personType.setValueFields(
				new JdbcTypeField(java.sql.Types.INTEGER, "id", Integer.class, "id"),
				new JdbcTypeField(java.sql.Types.VARCHAR, "name", String.class, "name"));
		factory.setTypes(personType);
		// CachePaasStoreFactory factory = new CachePaasStoreFactory();
		// factory.setIdentifier("entity.hr.EmpWorkInfo");
		// factory.setTenantId("11");
		cfg.setCacheStoreFactory(factory);


		QueryEntity qryEntity = new QueryEntity();

		qryEntity.setKeyType(Integer.class.getName());
		qryEntity.setValueType(dynamicClass.getName());
		qryEntity.setKeyFieldName("id");

		Set<String> keyFields = new HashSet<>();
		keyFields.add("id");
		qryEntity.setKeyFields(keyFields);

		LinkedHashMap<String, String> fields = new LinkedHashMap<>();
		fields.put("id", Integer.class.getName());
		fields.put("name", String.class.getName());

		qryEntity.setFields(fields);

		cfg.setQueryEntities(Collections.singletonList(qryEntity));
		// igniteCfg.se
		igniteCfg.setCacheConfiguration(cfg);
		// BinaryConfiguration binaryCfg = new BinaryConfiguration();
		// binaryCfg.setSerializer(new MapBinarySerializer()); // 注册自定义序列化器
		// binaryCfg.setTypeConfigurations(Collections.singletonList(
		// 		new BinaryTypeConfiguration(Map.class.getName())
		// ));
		//
		// igniteCfg.setBinaryConfiguration(binaryCfg);
	}

	@Test
	public void read_test(){

			try (IgniteCache cache = ignite.getOrCreateCache(cfg)) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				cache.loadCache(null);
				Assert.assertEquals(5,cache.size(CachePeekMode.ALL));
				// cache.get("1995743976544256");
				//
				// Assert.assertNotNull("Alice", cache.get("1995743976544256"));
				// Assert.assertNotNull("Alice", cache.get("1995743976544256"));
			}finally {
				ignite.destroyCache(cache_name);
			}

	}

	@Test
	public void read_map_test(){
		try (Ignite ignite=Ignition.start(igniteCfg)) {

			try (IgniteCache<Integer, Map> cache = ignite.getOrCreateCache("mapCache");) {
				// cache.loadCache(null);
				cache.put(1, Maps.map("name","Alice"));
				Assert.assertEquals("Alice", cache.get(1).get("name"));
			}finally {
				ignite.destroyCache("mapCache");
			}

		}
	}

	@Test
	public void read_predicate_test(){
		try (Ignite ignite=Ignition.start(igniteCfg)) {

			try (IgniteCache<Integer, Map> cache = ignite.cache(cache_name);) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				// ignite.destroyCache(cache_name);
				cache.loadCache(new IgniteBiPredicate() {
					@SneakyThrows
					@Override
					public boolean apply(Object key, Object person) {
						BinaryObjectImpl binaryObject = (BinaryObjectImpl) person;
						Map value = binaryObject.context().marshaller().unmarshal(binaryObject.array(), null);
						return ((Integer) value.get("id"))>3;
					}
				});
				Assert.assertEquals(2,cache.size(CachePeekMode.ALL));
			}finally {
				ignite.destroyCache(cache_name);
			}

		}
	}

	@Test
	public void read_sql_test(){

			try (IgniteCache<String, DataSimple> cache = ignite.getOrCreateCache(cfg);) {
				cache.loadCache(null);
				// cache.get("1995743976544256");
				// Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				SqlFieldsQuery sql = new SqlFieldsQuery(
						"select concat(id, ' ', name) from Person").setLocal(true);
//				SELECT      s.SCHEMA_NAME,     t.TABLE_NAME FROM      INFORMATION_SCHEMA.SCHEMATA s JOIN      INFORMATION_SCHEMA.TABLES t     ON s.SCHEMA_NAME = t.TABLE_SCHEMA where s.SCHEMA_NAME != 'INFORMATION_SCHEMA' and s.SCHEMA_NAME != 'SYS';
// Iterate over the result set.
				try (QueryCursor<List<?>> cursor = cache.query(sql)) {
					for (List<?> row : cursor)
						System.out.println("personName=" + row.get(0));
				}
				// cache.loadCache(null);
				// Assert.assertEquals(5,cache.size(CachePeekMode.ALL));

// 				 sql = new SqlFieldsQuery(
// 						"select concat(bid, ' ', name) from Map");
//
// // Iterate over the result set.
// 				try (QueryCursor<List<?>> cursor = cache.query(sql)) {
// 					for (List<?> row : cursor)
// 						System.out.println("personName=" + row.get(0));
// 				}
// 				 sql = new SqlFieldsQuery(
// 						"select * from Map");
// 				List<List<?>> all = cache.query(sql).getAll();
// 				// Assert.assertEquals(5, all.size());
// 				for (List<?> row : all)
// 					System.out.println("personName=" + row.get(1));
			}finally {
				ignite.destroyCache(cache_name);
			}

	}
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class NumToStr extends AbstractFunction {
    @Override
    public String getName() {
        return "NUM_TO_STR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1) {
        val value = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject1, env).toString());
        val result = value.stripTrailingZeros().toPlainString();
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(value).append(")=").append(result).toString());
        }
        return new AviatorString(result);
    }
}
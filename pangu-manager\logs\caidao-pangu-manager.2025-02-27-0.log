2025-02-27 10:48:58.416 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [pangu.PgsqlBlobDbTest], using SpringBootContextLoader
2025-02-27 10:48:58.430 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [pangu.PgsqlBlobDbTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-02-27 10:48:58.757 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-02-27 10:48:58.789 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@714b0b95, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@545c20e9, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@7a866a96, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@50a0688e, org.springframework.test.context.support.DirtiesContextTestExecutionListener@f9addb57, org.springframework.test.context.transaction.TransactionalTestExecutionListener@8052b580, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@466559d3, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@dd47ef59, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@d365d0b2, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@4e51d1e7, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@f4203e76, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@a06d0334]
2025-02-27 10:48:59.433 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-02-27 10:48:59.437 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-02-27 10:48:59.815 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9bc5cf1c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:00.618 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='192.168.120.202:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-manager-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-02-27 10:49:00.999 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-manager-config, group is : CORE_HR_GROUP
2025-02-27 10:49:01.030 [main] INFO  pangu.PgsqlBlobDbTest - The following profiles are active: dev
2025-02-27 10:49:02.935 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-02-27 10:49:03.428 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-02-27 10:49:03.434 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-02-27 10:49:03.487 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2025-02-27 10:49:03.598 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-02-27 10:49:03.615 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-02-27 10:49:04.217 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.cache.CacheAutoConfiguration; factoryMethodName=redisTemplate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/cache/CacheAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.217 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.cache.CacheAutoConfiguration; factoryMethodName=cacheService; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/cache/CacheAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.217 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.distributelock.DistributeAutoConfiguration; factoryMethodName=distributeLockImpl; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/distributelock/DistributeAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.218 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.mq.rabbitmq.MqAutoConfiguration; factoryMethodName=mqMessageSender; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/mq/rabbitmq/MqAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.218 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.mq.rabbitmq.MqAutoConfiguration; factoryMethodName=consumerGenerate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/mq/rabbitmq/MqAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.218 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.security.config.SecurityAutoConfiguration; factoryMethodName=sessionServiceRepository; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/security/config/SecurityAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.225 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=ac85dde6-56a9-3b1f-8090-c6368590c114
2025-02-27 10:49:04.372 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.cache.CacheAutoConfiguration; factoryMethodName=redisTemplate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/cache/CacheAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.372 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.cache.CacheAutoConfiguration; factoryMethodName=cacheService; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/cache/CacheAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.373 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.distributelock.DistributeAutoConfiguration; factoryMethodName=distributeLockImpl; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/distributelock/DistributeAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.373 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.mq.rabbitmq.MqAutoConfiguration; factoryMethodName=mqMessageSender; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/mq/rabbitmq/MqAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.373 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.mq.rabbitmq.MqAutoConfiguration; factoryMethodName=consumerGenerate; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/mq/rabbitmq/MqAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.373 [main] ERROR com.alibaba.spring.util.BeanUtils - Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=com.caidaocloud.security.config.SecurityAutoConfiguration; factoryMethodName=sessionServiceRepository; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/caidaocloud/security/config/SecurityAutoConfiguration.class] can't be resolved bean type!
2025-02-27 10:49:04.447 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.pangu.core.feign.PaasMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.449 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.pangu.manager.feign.PanguNodeFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.462 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.pangu.manager.feign.ArrangementClient' of type [com.caidaocloud.compute.remote.framework.core.RemoteRequestFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.465 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.467 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.470 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IMdMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.471 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.feign.IMdDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.474 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.476 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.IMdTransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.477 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.478 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.paas.common.feign.IDisplayPropertyFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.481 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.paas.common.feign.IPageFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.482 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.484 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.656 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$e771e64b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.679 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:04.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$94c58a69] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.171 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.406 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.418 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.442 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$2dafc4ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.478 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$46178a4d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7fabcc1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:05.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$b4ecc5ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:06.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$baefda07] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:06.098 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:06.604 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-02-27 10:49:06.610 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-02-27 10:49:07.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.382 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.461 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.504 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.518 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.529 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.532 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.545 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.548 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.656 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.remote.vertx.config.VertxConfiguration' of type [com.caidaocloud.remote.vertx.config.VertxConfiguration$$EnhancerBySpringCGLIB$$84312c1d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.util.UtilAutoConfiguration' of type [org.springframework.cloud.commons.util.UtilAutoConfiguration$$EnhancerBySpringCGLIB$$475995a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'inetUtilsProperties' of type [org.springframework.cloud.commons.util.InetUtilsProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:07.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'inetUtils' of type [org.springframework.cloud.commons.util.InetUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:08.298 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'initializerConfig' of type [com.caidaocloud.compute.remote.framework.core.InitializerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:08.365 [main] INFO  c.c.c.r.f.c.DynamicPortInitializer - vertx端口：19000
2025-02-27 10:49:08.849 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'remoteInitializer' of type [com.caidaocloud.remote.vertx.core.VertxInitializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:08.911 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9bc5cf1c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-02-27 10:49:10.580 [main] WARN   - Failed to resolve default logging config file: config/java.util.logging.properties
2025-02-27 10:49:10.800 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - 

>>>    __________  ________________  
>>>   /  _/ ___/ |/ /  _/_  __/ __/  
>>>  _/ // (7 7    // /  / / / _/    
>>> /___/\___/_/|_/___/ /_/ /___/   
>>> 
>>> ver. 2.15.0#20230425-sha1:f98f7f35
>>> 2023 Copyright(C) Apache Software Foundation
>>> 
>>> Ignite documentation: https://ignite.apache.org

2025-02-27 10:49:10.813 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Config URL: n/a
2025-02-27 10:49:10.845 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - IgniteConfiguration [igniteInstanceName=bonus-calc, pubPoolSize=8, svcPoolSize=8, callbackPoolSize=8, stripedPoolSize=8, sysPoolSize=8, mgmtPoolSize=4, dataStreamerPoolSize=8, utilityCachePoolSize=8, utilityCacheKeepAliveTime=60000, p2pPoolSize=2, qryPoolSize=8, buildIdxPoolSize=2, igniteHome=null, igniteWorkDir=C:\ignite, mbeanSrv=com.sun.jmx.mbeanserver.JmxMBeanServer@1888d4d4, nodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44, marsh=BinaryMarshaller [], marshLocJobs=false, p2pEnabled=true, netTimeout=5000, netCompressionLevel=1, sndRetryDelay=1000, sndRetryCnt=3, metricsHistSize=10000, metricsUpdateFreq=2000, metricsExpTime=9223372036854775807, discoSpi=TcpDiscoverySpi [addrRslvr=null, addressFilter=null, sockTimeout=0, ackTimeout=0, marsh=null, reconCnt=10, reconDelay=2000, maxAckTimeout=600000, soLinger=0, forceSrvMode=false, clientReconnectDisabled=false, internalLsnr=null, skipAddrsRandomization=false], segPlc=USE_FAILURE_HANDLER, segResolveAttempts=2, waitForSegOnStart=true, allResolversPassReq=true, segChkFreq=10000, commSpi=TcpCommunicationSpi [connectGate=org.apache.ignite.spi.communication.tcp.internal.ConnectGateway@c7cd0daf, ctxInitLatch=java.util.concurrent.CountDownLatch@f10aaca9[Count = 1], stopping=false, clientPool=null, nioSrvWrapper=null, stateProvider=null], evtSpi=org.apache.ignite.spi.eventstorage.NoopEventStorageSpi@7d4f93ef, colSpi=NoopCollisionSpi [], deploySpi=LocalDeploymentSpi [], indexingSpi=org.apache.ignite.spi.indexing.noop.NoopIndexingSpi@64f67628, addrRslvr=null, encryptionSpi=org.apache.ignite.spi.encryption.noop.NoopEncryptionSpi@e81e6495, tracingSpi=org.apache.ignite.spi.tracing.NoopTracingSpi@b80aab04, clientMode=false, rebalanceThreadPoolSize=2, rebalanceTimeout=10000, rebalanceBatchesPrefetchCnt=3, rebalanceThrottle=0, rebalanceBatchSize=524288, txCfg=TransactionConfiguration [txSerEnabled=false, dfltIsolation=REPEATABLE_READ, dfltConcurrency=PESSIMISTIC, dfltTxTimeout=0, txTimeoutOnPartitionMapExchange=0, deadlockTimeout=10000, pessimisticTxLogSize=0, pessimisticTxLogLinger=10000, tmLookupClsName=null, txManagerFactory=null, useJtaSync=false], cacheSanityCheckEnabled=true, discoStartupDelay=60000, deployMode=CONTINUOUS, p2pMissedCacheSize=100, locHost=null, timeSrvPortBase=31100, timeSrvPortRange=100, failureDetectionTimeout=10000, sysWorkerBlockedTimeout=null, clientFailureDetectionTimeout=30000, metricsLogFreq=60000, connectorCfg=ConnectorConfiguration [jettyPath=null, host=null, port=11211, noDelay=true, directBuf=false, sndBufSize=32768, rcvBufSize=32768, idleQryCurTimeout=600000, idleQryCurCheckFreq=60000, sndQueueLimit=0, selectorCnt=4, idleTimeout=7000, sslEnabled=false, sslClientAuth=false, sslCtxFactory=null, sslFactory=null, portRange=100, threadPoolSize=8, msgInterceptor=null], odbcCfg=null, warmupClos=null, atomicCfg=AtomicConfiguration [seqReserveSize=1000, cacheMode=PARTITIONED, backups=1, aff=null, grpName=null], classLdr=null, sslCtxFactory=null, platformCfg=null, binaryCfg=null, memCfg=null, pstCfg=null, dsCfg=DataStorageConfiguration [pageSize=0, concLvl=0, sysDataRegConf=org.apache.ignite.configuration.SystemDataRegionConfiguration@ee72daed, dfltDataRegConf=DataRegionConfiguration [name=default, maxSize=1643026022, initSize=268435456, swapPath=null, pageEvictionMode=DISABLED, pageReplacementMode=CLOCK, evictionThreshold=0.9, emptyPagesPoolSize=100, metricsEnabled=false, metricsSubIntervalCount=5, metricsRateTimeInterval=60000, persistenceEnabled=false, checkpointPageBufSize=0, lazyMemoryAllocation=true, warmUpCfg=null, memoryAllocator=null, cdcEnabled=false], dataRegions=null, storagePath=null, checkpointFreq=180000, lockWaitTime=10000, checkpointThreads=4, checkpointWriteOrder=SEQUENTIAL, walHistSize=20, maxWalArchiveSize=1073741824, walSegments=10, walSegmentSize=67108864, walPath=db/wal, walArchivePath=db/wal/archive, cdcWalPath=db/wal/cdc, cdcWalDirMaxSize=0, metricsEnabled=false, walMode=LOG_ONLY, walTlbSize=131072, walBuffSize=0, walFlushFreq=2000, walFsyncDelay=1000, walRecordIterBuffSize=67108864, alwaysWriteFullPages=false, fileIOFactory=org.apache.ignite.internal.processors.cache.persistence.file.AsyncFileIOFactory@68944e68, metricsSubIntervalCnt=5, metricsRateTimeInterval=60000, walAutoArchiveAfterInactivity=-1, walForceArchiveTimeout=-1, writeThrottlingEnabled=false, walCompactionEnabled=false, walCompactionLevel=1, checkpointReadLockTimeout=null, walPageCompression=DISABLED, walPageCompressionLevel=null, dfltWarmUpCfg=null, encCfg=org.apache.ignite.configuration.EncryptionConfiguration@44ec7e3f, defragmentationThreadPoolSize=4, minWalArchiveSize=-1, memoryAllocator=null], snapshotPath=snapshots, snapshotThreadPoolSize=4, activeOnStart=true, activeOnStartPropSetFlag=false, autoActivation=true, autoActivationPropSetFlag=false, clusterStateOnStart=null, sqlConnCfg=null, cliConnCfg=ClientConnectorConfiguration [host=null, port=10800, portRange=100, sockSndBufSize=0, sockRcvBufSize=0, tcpNoDelay=true, maxOpenCursorsPerConn=128, threadPoolSize=8, selectorCnt=4, idleTimeout=0, handshakeTimeout=10000, jdbcEnabled=true, odbcEnabled=true, thinCliEnabled=true, sslEnabled=false, useIgniteSslCtxFactory=true, sslClientAuth=false, sslCtxFactory=null, thinCliCfg=ThinClientConfiguration [maxActiveTxPerConn=100, maxActiveComputeTasksPerConn=0, sendServerExcStackTraceToClient=false]], mvccVacuumThreadCnt=2, mvccVacuumFreq=5000, authEnabled=false, failureHnd=null, commFailureRslvr=null, sqlCfg=SqlConfiguration [longQryWarnTimeout=3000, dfltQryTimeout=0, sqlQryHistSize=1000, validationEnabled=false], asyncContinuationExecutor=null]
2025-02-27 10:49:10.846 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - OS: Windows 10 10.0 amd64
2025-02-27 10:49:10.846 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - OS user: admin
2025-02-27 10:49:10.847 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - PID: 21732
2025-02-27 10:49:10.848 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Language runtime: Java Platform API Specification ver. 1.8
2025-02-27 10:49:10.848 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - VM information: IBM Semeru Runtime Open Edition 1.8.0_312-b07 Eclipse OpenJ9 Eclipse OpenJ9 VM openj9-0.29.0
2025-02-27 10:49:10.848 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - VM total memory: 1.9GB
2025-02-27 10:49:10.848 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Remote Management [restart: off, REST: on, JMX (remote: off)]
2025-02-27 10:49:10.848 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Logger: JavaLogger [quiet=true, config=null]
2025-02-27 10:49:10.849 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - IGNITE_HOME=null
2025-02-27 10:49:10.850 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - VM arguments: [-Xoptionsfile=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default\options.default, -Xlockword:mode=default,noLockword=java/lang/String,noLockword=java/util/MapEntry,noLockword=java/util/HashMap$Entry,noLockword=org/apache/harmony/luni/util/ModifiedMap$Entry,noLockword=java/util/Hashtable$Entry,noLockword=java/lang/invoke/MethodType,noLockword=java/lang/invoke/MethodHandle,noLockword=java/lang/invoke/CollectHandle,noLockword=java/lang/invoke/ConstructorHandle,noLockword=java/lang/invoke/ConvertHandle,noLockword=java/lang/invoke/ArgumentConversionHandle,noLockword=java/lang/invoke/AsTypeHandle,noLockword=java/lang/invoke/ExplicitCastHandle,noLockword=java/lang/invoke/FilterReturnHandle,noLockword=java/lang/invoke/DirectHandle,noLockword=java/lang/invoke/ReceiverBoundHandle,noLockword=java/lang/invoke/DynamicInvokerHandle,noLockword=java/lang/invoke/FieldHandle,noLockword=java/lang/invoke/FieldGetterHandle,noLockword=java/lang/invoke/FieldSetterHandle,noLockword=java/lang/invoke/StaticFieldGetterHandle,noLockword=java/lang/invoke/StaticFieldSetterHandle,noLockword=java/lang/invoke/IndirectHandle,noLockword=java/lang/invoke/InterfaceHandle,noLockword=java/lang/invoke/VirtualHandle,noLockword=java/lang/invoke/PrimitiveHandle,noLockword=java/lang/invoke/InvokeExactHandle,noLockword=java/lang/invoke/InvokeGenericHandle,noLockword=java/lang/invoke/VarargsCollectorHandle,noLockword=java/lang/invoke/ThunkTuple, -Xjcl:jclse29, -Dcom.ibm.oti.vm.bootstrap.library.path=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin, -Dsun.boot.library.path=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin, -Djava.library.path=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;., -Djava.home=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre, -Djava.ext.dirs=C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext, -Duser.dir=C:\caidao\caidao-pangu-engine\pangu-manager, -Djava.class.path=., -Dvisualvm.id=3106810834300, -ea, -Didea.test.cyclic.buffer.size=1048576, -javaagent:C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\lib\idea_rt.jar=52986:C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin, -Dfile.encoding=UTF-8, -Djava.class.path=C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\lib\idea_rt.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\junit\lib\junit5-rt.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\junit\lib\junit-rt.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\charsets.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\access-bridge-64.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\cldrdata.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\dnsns.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\dtfj.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\dtfjview.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\jaccess.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\localedata.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\nashorn.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\sunec.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\sunjce_provider.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\sunmscapi.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\sunpkcs11.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\traceformat.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\ext\zipfs.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\jce.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\jsse.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\management-agent.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\resources.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\lib\rt.jar;C:\Users\<USER>\.jdks\semeru-1.8.0_312\lib\tools.jar;C:\caidao\caidao-pangu-engine\pangu-manager\target\test-classes;C:\caidao\caidao-pangu-engine\pangu-manager\target\classes;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.1.2.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\1.4.3\nacos-client-1.4.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-common\1.4.3\nacos-common-1.4.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.7\commons-io-2.7.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.10\httpcore-nio-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-api\1.4.3\nacos-api-1.4.3.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.6\spring-context-support-1.0.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\2.1.0.RELEASE\spring-cloud-commons-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.1.1.RELEASE\spring-security-crypto-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\2.1.0.RELEASE\spring-cloud-context-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.1.0.RELEASE\spring-cloud-starter-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.5.2\junit-jupiter-5.5.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.3.1\junit-jupiter-api-5.3.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.0.0\apiguardian-api-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.1.1\opentest4j-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.3.1\junit-platform-commons-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.3.1\junit-jupiter-params-5.3.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.3.1\junit-jupiter-engine-5.3.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.3.1\junit-platform-engine-1.3.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\2.23.0\mockito-junit-jupiter-2.23.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.1.0.RELEASE\spring-boot-starter-web-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.1.0.RELEASE\spring-boot-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.1.0.RELEASE\spring-boot-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.1.0.RELEASE\spring-boot-starter-logging-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.25\jul-to-slf4j-1.7.25.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.1.0.RELEASE\spring-boot-starter-json-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.3\jackson-datatype-jdk8-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.3\jackson-datatype-jsr310-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.3\jackson-module-parameter-names-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.1.0.RELEASE\spring-boot-starter-tomcat-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.12\tomcat-embed-el-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.12\tomcat-embed-websocket-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.0.13.Final\hibernate-validator-6.0.13.Final.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.3.2.Final\jboss-logging-3.3.2.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.1.2.RELEASE\spring-web-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.1.2.RELEASE\spring-beans-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.1.2.RELEASE\spring-webmvc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.1.2.RELEASE\spring-aop-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.1.2.RELEASE\spring-expression-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-hystrix\2.1.0.RELEASE\spring-cloud-starter-netflix-hystrix-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\2.1.0.RELEASE\spring-cloud-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.7.RELEASE\spring-security-rsa-1.0.7.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.60\bcpkix-jdk15on-1.60.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.60\bcprov-jdk15on-1.60.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-hystrix\2.1.0.RELEASE\spring-cloud-netflix-hystrix-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.1.0.RELEASE\spring-boot-starter-aop-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.1.0.RELEASE\spring-cloud-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.1.0.RELEASE\spring-cloud-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.1.0.RELEASE\spring-cloud-starter-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Users\<USER>\.m2\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-serialization\1.5.18\hystrix-serialization-1.5.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-afterburner\2.13.3\jackson-module-afterburner-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-metrics-event-stream\1.5.18\hystrix-metrics-event-stream-1.5.18.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-javanica\1.5.18\hystrix-javanica-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.2\aspectjweaver-1.9.2.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava-reactive-streams\1.2.1\rxjava-reactive-streams-1.2.1.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.2\reactive-streams-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-mq\1.0.0-SNAPSHOT\galaxy-service-mq-1.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.2\lombok-1.18.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.25\slf4j-api-1.7.25.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-amqp\2.1.0.RELEASE\spring-boot-starter-amqp-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.1.2.RELEASE\spring-messaging-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\amqp\spring-rabbit\2.1.0.RELEASE\spring-rabbit-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\amqp\spring-amqp\2.1.0.RELEASE\spring-amqp-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\retry\spring-retry\1.2.2.RELEASE\spring-retry-1.2.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\rabbitmq\amqp-client\5.4.3\amqp-client-5.4.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.1.2.RELEASE\spring-tx-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\caidaocloud-commons\1.0.0-SNAPSHOT\caidaocloud-commons-1.0.0-20241118.033041-1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.8.1\commons-lang3-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.1.2.RELEASE\spring-context-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.12\tomcat-embed-core-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\tomcat-annotations-api\9.0.12\tomcat-annotations-api-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.1.0.RELEASE\spring-boot-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.7.0\springfox-swagger-ui-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\2.1.0.RELEASE\spring-boot-configuration-processor-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.10.0\okhttp-4.10.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.0.0\okio-jvm-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.5.21\kotlin-stdlib-1.5.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.5.21\kotlin-stdlib-common-1.5.21.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\2.1.0.RELEASE\spring-boot-starter-mail-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.1.2.RELEASE\spring-context-support-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;C:\Users\<USER>\.m2\repository\com\googlecode\aviator\aviator\5.4.2\aviator-5.4.2.jar;C:\Users\<USER>\.m2\repository\com\googlecode\totallylazy\totallylazy\2.286\totallylazy-2.286.jar;C:\caidao\caidao-pangu-engine\compute-engine\compute-remote\compute-remote-impl-akka\target\classes;C:\caidao\caidao-pangu-engine\compute-engine\compute-remote\compute-remote-framework\target\classes;C:\Users\<USER>\.m2\repository\com\esotericsoftware\kryo\kryo5\5.3.0\kryo5-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-actor-typed_2.13\2.8.7\akka-actor-typed_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\org\scala-lang\scala-library\2.13.11\scala-library-2.13.11.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-actor_2.13\2.8.7\akka-actor_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\config\1.4.2\config-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\scala-lang\modules\scala-java8-compat_2.13\1.0.0\scala-java8-compat_2.13-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-slf4j_2.13\2.8.7\akka-slf4j_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-cluster-typed_2.13\2.8.7\akka-cluster-typed_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-cluster_2.13\2.8.7\akka-cluster_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-remote_2.13\2.8.7\akka-remote_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-stream_2.13\2.8.7\akka-stream_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-protobuf-v3_2.13\2.8.7\akka-protobuf-v3_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\ssl-config-core_2.13\0.6.1\ssl-config-core_2.13-0.6.1.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-pki_2.13\2.8.7\akka-pki_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\hierynomus\asn-one\0.6.0\asn-one-0.6.0.jar;C:\Users\<USER>\.m2\repository\org\agrona\agrona\1.17.1\agrona-1.17.1.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-coordination_2.13\2.8.7\akka-coordination_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-cluster-tools_2.13\2.8.7\akka-cluster-tools_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\com\typesafe\akka\akka-distributed-data_2.13\2.8.7\akka-distributed-data_2.13-2.8.7.jar;C:\Users\<USER>\.m2\repository\org\lmdbjava\lmdbjava\0.8.3\lmdbjava-0.8.3.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jnr-constants\0.10.4\jnr-constants-0.10.4.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jnr-ffi\2.2.13\jnr-ffi-2.2.13.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jffi\1.3.10\jffi-1.3.10.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jffi\1.3.10\jffi-1.3.10-native.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.2\asm-commons-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-analysis\9.2\asm-analysis-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.2\asm-tree-9.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-util\9.2\asm-util-9.2.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jnr-a64asm\1.0.0\jnr-a64asm-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\jnr\jnr-x86asm\1.0.2\jnr-x86asm-1.0.2.jar;C:\caidao\caidao-pangu-engine\compute-engine\compute-remote\compute-remote-impl-vertx\target\classes;C:\Users\<USER>\.m2\repository\io\vertx\vertx-core\4.5.11\vertx-core-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.115.Final\netty-handler-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.115.Final\netty-handler-proxy-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.115.Final\netty-codec-socks-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.115.Final\netty-codec-http-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.115.Final\netty-codec-http2-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.115.Final\netty-resolver-dns-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.115.Final\netty-codec-dns-4.1.115.Final.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-web\4.5.11\vertx-web-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-web-common\4.5.11\vertx-web-common-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-auth-common\4.5.11\vertx-auth-common-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-bridge-common\4.5.11\vertx-bridge-common-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-web-client\4.5.11\vertx-web-client-4.5.11.jar;C:\Users\<USER>\.m2\repository\io\vertx\vertx-uri-template\4.5.11\vertx-uri-template-4.5.11.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\metadata-sdk\2.0.2-SNAPSHOT\metadata-sdk-2.0.2-20250224.105320-36.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-httpclient\10.1.0\feign-httpclient-10.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.6\httpclient-4.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.10\httpcore-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-msg\1.0.0-SNAPSHOT\galaxy-service-msg-1.0.0-20241029.023610-2.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\caidao-resource\1.0.0-SNAPSHOT\caidao-resource-1.0.0-20250220.090908-46.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-security\1.0.0-SNAPSHOT\galaxy-service-security-1.0.0-20240919.065632-1.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.5.21\kotlin-stdlib-jdk8-1.5.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.5.21\kotlin-stdlib-jdk7-1.5.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-reflect\1.5.21\kotlin-reflect-1.5.21.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.13.3\jackson-module-kotlin-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache-spring-boot-starter\7.0.8\autoload-cache-spring-boot-starter-7.0.8.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache\7.0.8\autoload-cache-7.0.8.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\5.1.2.RELEASE\lettuce-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.2.2.RELEASE\reactor-core-3.2.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.6.0\commons-pool2-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\paas-sdk\2.0.2-SNAPSHOT\paas-sdk-2.0.2-20250224.105425-8.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\masterdata\masterdata-core\1.0.0-SNAPSHOT\masterdata-core-1.0.0-20240417.091854-21.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-distributedlock\2.0.0-SNAPSHOT\galaxy-service-distributedlock-2.0.0-20241204.093503-1.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-cache\1.0.0-SNAPSHOT\galaxy-service-cache-1.0.0-20241204.092735-1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.1.0.RELEASE\spring-boot-starter-data-redis-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.1.2.RELEASE\spring-data-redis-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.1.2.RELEASE\spring-data-keyvalue-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.1.2.RELEASE\spring-data-commons-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.1.2.RELEASE\spring-oxm-5.1.2.RELEASE.jar;C:\caidao\caidao-pangu-engine\pangu-core\target\classes;C:\Users\<USER>\.m2\repository\org\apache\ignite\ignite-core\2.15.0\ignite-core-2.15.0.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.0\cache-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\ignite\ignite-spring\2.15.0\ignite-spring-2.15.0.jar;C:\Users\<USER>\.m2\repository\org\apache\ignite\ignite-spring-boot-autoconfigure-ext\1.0.0\ignite-spring-boot-autoconfigure-ext-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\apache\ignite\ignite-indexing\2.15.0\ignite-indexing-2.15.0.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-core\3.0.3\lucene-core-3.0.3.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-analyzers-common\8.11.2\lucene-analyzers-common-8.11.2.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queryparser\8.11.2\lucene-queryparser-8.11.2.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queries\8.11.2\lucene-queries-8.11.2.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-sandbox\8.11.2\lucene-sandbox-8.11.2.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\1.4.197\h2-1.4.197.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.2.0\HikariCP-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.4.0\postgresql-42.4.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.9.3\byte-buddy-1.9.3.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.4.3.2\mybatis-plus-boot-starter-3.4.3.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.4.3.2\mybatis-plus-3.4.3.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.4.3.2\mybatis-plus-extension-3.4.3.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.4.3.2\mybatis-plus-core-3.4.3.2.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.4.3.2\mybatis-plus-annotation-3.4.3.2.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.1\jsqlparser-4.1.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.1.0.RELEASE\spring-boot-starter-jdbc-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.1.2.RELEASE\spring-jdbc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.1.0.RELEASE\spring-cloud-starter-openfeign-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.1.0.RELEASE\spring-cloud-openfeign-core-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\10.1.0\feign-core-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\10.1.0\feign-slf4j-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-hystrix\10.1.0\feign-hystrix-10.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.1.0.RELEASE\spring-boot-starter-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.1.0.RELEASE\spring-boot-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.1.0.RELEASE\spring-boot-test-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.11.1\assertj-core-3.11.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\2.23.0\mockito-core-2.23.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.9.3\byte-buddy-agent-1.9.3.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-library\1.3\hamcrest-library-1.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.1.2.RELEASE\spring-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.1.2.RELEASE\spring-jcl-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.1.2.RELEASE\spring-test-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.6.2\xmlunit-core-2.6.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.1.0.RELEASE\spring-boot-starter-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.1.0.RELEASE\spring-boot-actuator-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.1.0.RELEASE\spring-boot-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.1.0\micrometer-core-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.7.0\springfox-swagger2-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.14\swagger-annotations-1.5.14.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.13\swagger-models-1.5.13.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.7.0\springfox-spi-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.7.0\springfox-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.7.0\springfox-schema-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.7.0\springfox-swagger-common-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.7.0\springfox-spring-web-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.4.0\classmate-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.1.0.Final\mapstruct-1.1.0.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-starter\0.2.10\nacos-config-spring-boot-starter-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-spring-context\1.1.1\nacos-spring-context-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-autoconfigure\0.2.10\nacos-config-spring-boot-autoconfigure-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-spring-boot-base\0.2.10\nacos-spring-boot-base-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.3\jackson-annotations-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.3\jackson-core-2.13.3.jar, -Dsun.java.command=com.intellij.rt.junit.JUnitStarter -ideVersion5 -junit4 pangu.PgsqlBlobDbTest,readThroughTest, -Dsun.java.launcher=SUN_STANDARD]
2025-02-27 10:49:10.850 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - System cache's DataRegion size is configured to 40 MB. Use DataStorageConfiguration.systemRegionInitialSize property to change the setting.
2025-02-27 10:49:10.850 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Configured caches [in 'sysMemPlc' dataRegion: ['ignite-sys-cache']]
2025-02-27 10:49:10.851 [main] WARN  o.a.i.i.IgniteKernal%bonus-calc - Peer class loading is enabled (disable it in production for performance and deployment consistency reasons)
2025-02-27 10:49:10.851 [main] WARN  o.a.i.i.IgniteKernal%bonus-calc - Please set system property '-Djava.net.preferIPv4Stack=true' to avoid possible problems in mixed environments.
2025-02-27 10:49:11.018 [main] INFO  o.a.i.i.p.p.IgnitePluginProcessor - Configured plugins:
2025-02-27 10:49:11.018 [main] INFO  o.a.i.i.p.p.IgnitePluginProcessor -   ^-- None
2025-02-27 10:49:11.018 [main] INFO  o.a.i.i.p.p.IgnitePluginProcessor - 
2025-02-27 10:49:11.021 [main] INFO  o.a.i.i.p.failure.FailureProcessor - Configured failure handler: [hnd=StopNodeOrHaltFailureHandler [tryStop=false, timeout=0, super=AbstractFailureHandler [ignoredFailureTypes=UnmodifiableSet [SYSTEM_WORKER_BLOCKED, SYSTEM_CRITICAL_OPERATION_TIMEOUT]]]]
2025-02-27 10:49:11.093 [pub-#21%bonus-calc%] WARN  o.a.ignite.internal.GridDiagnostic - Initial heap size is 8MB (should be no less than 512MB, use -Xms512m -Xmx512m).
2025-02-27 10:49:11.628 [main] INFO  o.a.i.s.c.tcp.TcpCommunicationSpi - Successfully bound communication NIO server to TCP port [port=47101, locHost=0.0.0.0/0.0.0.0, selectorsCnt=4, selectorSpins=0, pairedConn=false]
2025-02-27 10:49:11.628 [main] WARN  o.a.i.s.c.tcp.TcpCommunicationSpi - Message queue limit is set to 0 which may lead to potential OOMEs when running cache operations in FULL_ASYNC or PRIMARY_SYNC modes due to message queues growth on sender and receiver sides.
2025-02-27 10:49:11.940 [main] INFO  o.a.i.i.m.c.GridCollisionManager - Collision resolution is disabled (all jobs will be activated upon arrival).
2025-02-27 10:49:12.520 [main] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Successfully bound to TCP port [port=47501, localHost=0.0.0.0/0.0.0.0, locNodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44]
2025-02-27 10:49:12.526 [main] INFO  o.a.i.i.p.c.GridLocalConfigManager - Resolved page store work directory: C:\ignite\db\0_0_0_0_0_0_0_1_127_0_0_1_172_21_160_1_192_168_130_41_47501
2025-02-27 10:49:12.610 [main] INFO  o.a.i.i.p.c.p.IgniteCacheDatabaseSharedManager - Configured data regions initialized successfully [total=4]
2025-02-27 10:49:12.835 [main] WARN  o.a.i.i.p.query.h2.IgniteH2Indexing - Serialization of Java objects in H2 was enabled.
2025-02-27 10:49:13.265 [main] INFO  o.a.i.i.p.o.ClientListenerProcessor - Client connector processor has started on TCP port 10801
2025-02-27 10:49:13.420 [main] INFO  o.a.i.i.p.r.p.t.GridTcpRestProtocol - Command protocol successfully started [name=TCP binary, host=0.0.0.0/0.0.0.0, port=11212]
2025-02-27 10:49:13.967 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Non-loopback local IPs: ************, **************, fe80:0:0:0:7443:b29c:552:ed0e%eth15, fe80:0:0:0:7482:9c06:5392:55e6%net5, fe80:0:0:0:d4ae:5f2d:781d:46ed%eth1, fe80:0:0:0:f96a:2909:20a6:32f%wlan4, fe80:0:0:0:ff8e:65e3:b338:ab47%wlan2
2025-02-27 10:49:13.968 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Enabled local MACs: 00155DEDEA76, 00FF938F4F5F, D41B812F5ECB, D41B812F5ECC, D61B812F5ECB, F61B812F5ECB
2025-02-27 10:49:13.974 [main] INFO  o.a.i.i.p.cluster.ClusterProcessor - Cluster ID and tag has been read from metastorage: null
2025-02-27 10:49:13.979 [main] INFO  o.a.i.i.cluster.IgniteClusterImpl - Shutdown policy was updated [oldVal=null, newVal=null]
2025-02-27 10:49:13.981 [main] INFO  o.a.i.i.p.q.s.IgniteStatisticsManagerImpl - Statistics usage state was changed from null to null
2025-02-27 10:49:14.000 [main] WARN  o.a.i.s.d.t.i.m.TcpDiscoveryMulticastIpFinder - TcpDiscoveryMulticastIpFinder has no pre-configured addresses (it is recommended in production to specify at least one address in TcpDiscoveryMulticastIpFinder.getAddresses() configuration property)
2025-02-27 10:49:16.696 [tcp-disco-srvr-[:47501]-#3%bonus-calc%-#47%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - TCP discovery accepted incoming connection [rmtAddr=/************, rmtPort=53090]
2025-02-27 10:49:16.701 [tcp-disco-srvr-[:47501]-#3%bonus-calc%-#47%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - TCP discovery spawning a new thread for connection [rmtAddr=/************, rmtPort=53090]
2025-02-27 10:49:16.702 [tcp-disco-sock-reader-[]-#8%bonus-calc%-#52%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Started serving remote node connection [rmtAddr=/************:53090, rmtPort=53090]
2025-02-27 10:49:16.703 [tcp-disco-sock-reader-[]-#8%bonus-calc%-#52%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Received ping request from the remote node [rmtNodeId=a375c3dd-35e1-43ae-94bf-ed979156e807, rmtAddr=/************:53090, rmtPort=53090]
2025-02-27 10:49:16.704 [tcp-disco-sock-reader-[]-#8%bonus-calc%-#52%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Finished writing ping response [rmtNodeId=a375c3dd-35e1-43ae-94bf-ed979156e807, rmtAddr=/************:53090, rmtPort=53090]
2025-02-27 10:49:16.704 [tcp-disco-sock-reader-[]-#8%bonus-calc%-#52%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Finished serving remote node connection [rmtAddr=/************:53090, rmtPort=53090, rmtNodeId=null]
2025-02-27 10:49:16.769 [tcp-disco-srvr-[:47501]-#3%bonus-calc%-#47%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - TCP discovery accepted incoming connection [rmtAddr=/0:0:0:0:0:0:0:1, rmtPort=53091]
2025-02-27 10:49:16.769 [tcp-disco-srvr-[:47501]-#3%bonus-calc%-#47%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - TCP discovery spawning a new thread for connection [rmtAddr=/0:0:0:0:0:0:0:1, rmtPort=53091]
2025-02-27 10:49:16.770 [tcp-disco-sock-reader-[]-#9%bonus-calc%-#53%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Started serving remote node connection [rmtAddr=/0:0:0:0:0:0:0:1:53091, rmtPort=53091]
2025-02-27 10:49:16.770 [tcp-disco-sock-reader-[a375c3dd 0:0:0:0:0:0:0:1:53091]-#9%bonus-calc%-#53%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - Initialized connection with remote server node [nodeId=a375c3dd-35e1-43ae-94bf-ed979156e807, rmtAddr=/0:0:0:0:0:0:0:1:53091]
2025-02-27 10:49:16.800 [tcp-disco-msg-worker-[]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.s.d.tcp.TcpDiscoverySpi - New next node [newNext=TcpDiscoveryNode [id=a375c3dd-35e1-43ae-94bf-ed979156e807, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47500, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=null, discPort=47500, order=1, intOrder=1, lastExchangeTime=1740624556771, loc=false, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false]]
2025-02-27 10:49:16.862 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.c.DistributedBaselineConfiguration - Baseline parameter 'baselineAutoAdjustEnabled' was changed from 'null' to 'true'
2025-02-27 10:49:16.862 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.cluster.ClusterProcessor - Cluster tag will be set to new value: wonderful_elbakyan, previous value was: null
2025-02-27 10:49:16.863 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.c.DistributedBaselineConfiguration - Baseline parameter 'baselineAutoAdjustTimeout' was changed from 'null' to '0'
2025-02-27 10:49:16.863 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'longOperationsDumpTimeout' was changed from 'null' to '60000'
2025-02-27 10:49:16.864 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'longTransactionTimeDumpThreshold' was changed from 'null' to '0'
2025-02-27 10:49:16.865 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'transactionTimeDumpSamplesCoefficient' was changed from 'null' to '0.0'
2025-02-27 10:49:16.865 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'longTransactionTimeDumpSamplesPerSecondLimit' was changed from 'null' to '5'
2025-02-27 10:49:16.865 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'collisionsDumpInterval' was changed from 'null' to '1000'
2025-02-27 10:49:16.866 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.c.t.IgniteTxManager - Transactions parameter 'txOwnerDumpRequestsAllowed' was changed from 'null' to 'true'
2025-02-27 10:49:16.866 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.query.h2.IgniteH2Indexing - SQL parameter 'sql.disabledFunctions' was changed from 'null' to '[FILE_WRITE, CANCEL_SESSION, MEMORY_USED, CSVREAD, LINK_SCHEMA, MEMORY_FREE, FILE_READ, CSVWRITE, SESSION_ID, LOCK_MODE]'
2025-02-27 10:49:16.867 [tcp-disco-msg-worker-[a375c3dd 0:0:0:0:0:0:0:1:47500]-#2%bonus-calc%-#46%bonus-calc%] INFO  o.a.i.i.p.query.h2.IgniteH2Indexing - SQL parameter 'sql.defaultQueryTimeout' was changed from 'null' to '0'
2025-02-27 10:49:16.879 [disco-notifier-worker-#45%bonus-calc%] INFO  o.a.i.i.p.c.GridClusterStateProcessor - Received activate cluster request with BaselineTopology[id=0] initiator node ID: c3977ab8-a39c-406d-83c2-f823ae1f7a44
2025-02-27 10:49:16.881 [disco-notifier-worker-#45%bonus-calc%] INFO  o.a.i.i.p.c.GridClusterStateProcessor - Started state transition: activate cluster
2025-02-27 10:49:16.888 [disco-notifier-worker-#45%bonus-calc%] INFO  o.a.i.i.p.c.GridClusterStateProcessor - Received state change finish message: ACTIVE
2025-02-27 10:49:16.888 [disco-notifier-worker-#45%bonus-calc%] INFO  o.a.i.i.p.c.GridClusterStateProcessor - Cluster state was changed from ACTIVE to ACTIVE
2025-02-27 10:49:16.889 [main] WARN  o.a.i.i.IgniteKernal%bonus-calc - Nodes started on local machine require more than 80% of physical RAM what can lead to significant slowdown due to swapping (please decrease JVM heap size, data region size or checkpoint buffer size) [required=6273MB, available=7834MB]
2025-02-27 10:49:16.891 [disco-notifier-worker-#45%bonus-calc%] INFO  o.a.i.i.p.c.mvcc.MvccProcessorImpl - Assigned mvcc coordinator [crd=MvccCoordinator [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], nodeId=a375c3dd-35e1-43ae-94bf-ed979156e807, ver=1740624499711, local=false, initialized=false]]
2025-02-27 10:49:16.907 [sys-#56%bonus-calc%] INFO  o.a.i.i.p.cluster.ClusterProcessor - Writing cluster ID and tag to metastorage on ready for write ClusterIdAndTag [id=b485aa75-590d-4ff3-9e50-d0a33c3ac9f9, tag=wonderful_elbakyan]
2025-02-27 10:49:17.071 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Started exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], crd=false, evt=NODE_JOINED, evtNode=c3977ab8-a39c-406d-83c2-f823ae1f7a44, customEvt=null, allowMerge=true, exchangeFreeSwitch=false]
2025-02-27 10:49:17.109 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.p.IgniteCacheDatabaseSharedManager - Data Regions Started: 4
2025-02-27 10:49:17.110 [exchange-worker-#57%bonus-calc%] INFO  org.apache.ignite.cache.msg - Components activation performed in 38 ms.
2025-02-27 10:49:17.275 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.cache.GridCacheProcessor - Started cache [name=ignite-sys-cache, id=-2100569601, dataRegionName=sysMemPlc, mode=REPLICATED, atomicity=TRANSACTIONAL, backups=2147483647, mvcc=false]
2025-02-27 10:49:17.298 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.cache.GridCacheProcessor - Starting caches on local join performed in 186 ms.
2025-02-27 10:49:17.309 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Skipped waiting for partitions release future (local node is joining) [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0]]
2025-02-27 10:49:17.374 [grid-nio-worker-tcp-comm-0-#23%TcpCommunicationSpi%] INFO  o.a.i.s.c.tcp.TcpCommunicationSpi - Established outgoing communication connection [locAddr=/127.0.0.1:53093, rmtAddr=/127.0.0.1:47100]
2025-02-27 10:49:17.383 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Finished exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], crd=false]
2025-02-27 10:49:17.489 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Received full message, will finish exchange [node=a375c3dd-35e1-43ae-94bf-ed979156e807, resVer=AffinityTopologyVersion [topVer=2, minorTopVer=0]]
2025-02-27 10:49:17.586 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finish exchange future [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], err=null, rebalanced=false, wasRebalanced=false]
2025-02-27 10:49:17.602 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.cache.GridCacheProcessor - Finish proxy initialization, cacheName=ignite-sys-cache, localNodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44
2025-02-27 10:49:17.603 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Completed partition exchange [localNode=c3977ab8-a39c-406d-83c2-f823ae1f7a44, exchange=GridDhtPartitionsExchangeFuture [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], evt=NODE_JOINED, evtNode=TcpDiscoveryNode [id=c3977ab8-a39c-406d-83c2-f823ae1f7a44, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47501, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47501, /0:0:0:0:0:0:0:1:47501, /127.0.0.1:47501, LAPTOP-TT07MCS9/**************:47501], discPort=47501, order=2, intOrder=2, lastExchangeTime=1740624557445, loc=true, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], rebalanced=false, done=true, newCrdFut=null], topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0]]
2025-02-27 10:49:17.604 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange timings [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], stage="Waiting in exchange queue" (168 ms), stage="Exchange parameters initialization" (5 ms), stage="Components activation" (38 ms), stage="Determine exchange type" (196 ms), stage="Preloading notification" (1 ms), stage="After states restored callback" (0 ms), stage="WAL history reservation" (0 ms), stage="Waiting for Full message" (179 ms), stage="Affinity recalculation" (95 ms), stage="Full map updating" (1 ms), stage="Exchange done" (17 ms), stage="Total time" (700 ms)]
2025-02-27 10:49:17.605 [sys-#61%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange longest local stages [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], stage="Affinity fetch" (192 ms) (parent=Determine exchange type), stage="Affinity initialization (local join) [grp=ignite-sys-cache]" (95 ms) (parent=Affinity recalculation)]
2025-02-27 10:49:17.624 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.GridCachePartitionExchangeManager - Rebalancing scheduled [order=[ignite-sys-cache], top=AffinityTopologyVersion [topVer=2, minorTopVer=0], rebalanceId=1, evt=NODE_JOINED, node=c3977ab8-a39c-406d-83c2-f823ae1f7a44]
2025-02-27 10:49:17.624 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionDemander - Prepared rebalancing [grp=ignite-sys-cache, mode=SYNC, supplier=a375c3dd-35e1-43ae-94bf-ed979156e807, partitionsCount=100, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], rebalanceId=1]
2025-02-27 10:49:17.630 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.t.PartitionsEvictManager - Eviction in progress [groups=1, remainingPartsToEvict=0]
2025-02-27 10:49:17.631 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.t.PartitionsEvictManager - Group eviction in progress [grpName=ignite-sys-cache, grpId=-2100569601, remainingPartsToEvict=1, partsEvictInProgress=0, totalParts=100]
2025-02-27 10:49:17.632 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.t.PartitionsEvictManager - Partitions have been scheduled for eviction: [grpId=-2100569601, grpName=ignite-sys-cache, clearing=[0]]
2025-02-27 10:49:17.642 [sys-#59%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionDemander - Starting rebalance routine [ignite-sys-cache, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], supplier=a375c3dd-35e1-43ae-94bf-ed979156e807, fullPartitions=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99], histPartitions=[], rebalanceId=1]
2025-02-27 10:49:17.682 [rebalance-#68%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionDemander - Completed (final) rebalancing [rebalanceId=1, grp=ignite-sys-cache, supplier=a375c3dd-35e1-43ae-94bf-ed979156e807, partitions=100, entries=0, duration=57ms, bytesRcvd=0.0 B, bandwidth=0.0 B/sec, histPartitions=0, histEntries=0, histBytesRcvd=0.0 B, fullPartitions=100, fullEntries=0, fullBytesRcvd=0.0 B, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], progress=1/1]
2025-02-27 10:49:17.683 [rebalance-#68%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionDemander - Completed rebalance future: RebalanceFuture [state=STARTED, grp=CacheGroupContext [grp=ignite-sys-cache], topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], rebalanceId=1, routines=1, receivedBytes=1200, receivedKeys=0, partitionsLeft=0, partitionsTotal=100, startTime=1740624557624, endTime=1740624557682, lastCancelledTime=-1, result=true]
2025-02-27 10:49:17.687 [rebalance-#68%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionDemander - Completed rebalance chain: [rebalanceId=1, partitions=100, entries=0, duration=63ms, bytesRcvd=0.0 B]
2025-02-27 10:49:17.706 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Started exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], crd=false, evt=DISCOVERY_CUSTOM_EVT, evtNode=a375c3dd-35e1-43ae-94bf-ed979156e807, customEvt=CacheAffinityChangeMessage [id=8e80e454591-60dd39db-976c-448f-acaa-26739fc07354, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=0], exchId=null, partsMsg=null, exchangeNeeded=true, stopProc=false], allowMerge=false, exchangeFreeSwitch=false]
2025-02-27 10:49:17.714 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partition release future [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], waitTime=0ms, futInfo=NA, mode=DISTRIBUTED]
2025-02-27 10:49:17.723 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partitions release latch: ClientLatch [coordinator=TcpDiscoveryNode [id=a375c3dd-35e1-43ae-94bf-ed979156e807, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47500, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47500, LAPTOP-TT07MCS9/**************:47500, /0:0:0:0:0:0:0:1:47500, /127.0.0.1:47500], discPort=47500, order=1, intOrder=1, lastExchangeTime=1740624556771, loc=false, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], ackSent=true, super=CompletableLatch [id=CompletableLatchUid [id=exchange, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1]]]]
2025-02-27 10:49:17.723 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partition release future [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], waitTime=0ms, futInfo=NA, mode=LOCAL]
2025-02-27 10:49:17.727 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Finished exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], crd=false]
2025-02-27 10:49:17.730 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Security status [authentication=off, sandbox=off, tls/ssl=off]
2025-02-27 10:49:17.730 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Performance suggestions for grid 'bonus-calc' (fix if possible)
2025-02-27 10:49:17.730 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - To disable, set -DIGNITE_PERFORMANCE_SUGGESTIONS_DISABLED=true
2025-02-27 10:49:17.730 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Enable server mode for JVM (add '-server' to JVM options)
2025-02-27 10:49:17.730 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Switch to the most recent 11 JVM version
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Enable G1 Garbage Collector (add '-XX:+UseG1GC' to JVM options)
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Specify JVM heap max size (add '-Xmx<size>[g|G|m|M|k|K]' to JVM options)
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Set max direct memory size if getting 'OOME: Direct buffer memory' (add '-XX:MaxDirectMemorySize=<size>[g|G|m|M|k|K]' to JVM options)
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc -   ^-- Disable assertions (remove '-ea' from JVM options)
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - Refer to this page for more performance suggestions: https://ignite.apache.org/docs/latest/perf-and-troubleshooting/memory-tuning
2025-02-27 10:49:17.731 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - 
2025-02-27 10:49:17.733 [main] INFO  o.a.i.i.IgniteKernal%bonus-calc - 

>>> +-----------------------------------------------------------------------+
>>> Ignite ver. 2.15.0#20230425-sha1:f98f7f35de6dc76a9b69299154afaa2139a5ec6d
>>> +-----------------------------------------------------------------------+
>>> OS name: Windows 10 10.0 amd64
>>> CPU(s): 8
>>> Heap: 1.9GB
>>> VM name: 21732@LAPTOP-TT07MCS9
>>> Ignite instance name: bonus-calc
>>> Local node [ID=C3977AB8-A39C-406D-83C2-F823AE1F7A44, order=2, clientMode=false]
>>> Local node addresses: [LAPTOP-TT07MCS9.mshome.net/0:0:0:0:0:0:0:1, LAPTOP-TT07MCS9/127.0.0.1, /************, /**************]
>>> Local ports: TCP:10801 TCP:11212 TCP:47101 UDP:47400 TCP:47501 
>>> +-----------------------------------------------------------------------+

2025-02-27 10:49:17.734 [sys-#63%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Received full message, will finish exchange [node=a375c3dd-35e1-43ae-94bf-ed979156e807, resVer=AffinityTopologyVersion [topVer=2, minorTopVer=1]]
2025-02-27 10:49:17.736 [sys-#63%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finish exchange future [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], err=null, rebalanced=true, wasRebalanced=false]
2025-02-27 10:49:17.736 [sys-#63%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Completed partition exchange [localNode=c3977ab8-a39c-406d-83c2-f823ae1f7a44, exchange=GridDhtPartitionsExchangeFuture [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], evt=DISCOVERY_CUSTOM_EVT, evtNode=TcpDiscoveryNode [id=a375c3dd-35e1-43ae-94bf-ed979156e807, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47500, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47500, LAPTOP-TT07MCS9/**************:47500, /0:0:0:0:0:0:0:1:47500, /127.0.0.1:47500], discPort=47500, order=1, intOrder=1, lastExchangeTime=1740624556771, loc=false, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], rebalanced=true, done=true, newCrdFut=null], topVer=AffinityTopologyVersion [topVer=2, minorTopVer=1]]
2025-02-27 10:49:17.736 [sys-#63%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange timings [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], stage="Waiting in exchange queue" (0 ms), stage="Exchange parameters initialization" (0 ms), stage="Determine exchange type" (1 ms), stage="Preloading notification" (0 ms), stage="Wait partitions release [latch=exchange]" (11 ms), stage="Wait partitions release latch [latch=exchange]" (3 ms), stage="Wait partitions release [latch=exchange]" (0 ms), stage="After states restored callback" (1 ms), stage="WAL history reservation" (0 ms), stage="Waiting for Full message" (9 ms), stage="Affinity recalculation" (0 ms), stage="Full map updating" (1 ms), stage="Exchange done" (0 ms), stage="Total time" (26 ms)]
2025-02-27 10:49:17.737 [sys-#63%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange longest local stages [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=1], stage="Affinity change by custom message [grp=ignite-sys-cache]" (1 ms) (parent=Determine exchange type)]
2025-02-27 10:49:17.738 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.GridCachePartitionExchangeManager - Skipping rebalancing (nothing scheduled) [top=AffinityTopologyVersion [topVer=2, minorTopVer=1], force=false, evt=DISCOVERY_CUSTOM_EVT, node=a375c3dd-35e1-43ae-94bf-ed979156e807]
2025-02-27 10:49:17.740 [main] INFO  o.a.i.i.m.d.GridDiscoveryManager - Topology snapshot [ver=2, locNode=c3977ab8, servers=2, clients=0, state=ACTIVE, CPUs=8, offheap=3.1GB, heap=2.9GB, aliveNodes=[TcpDiscoveryNode [id=a375c3dd-35e1-43ae-94bf-ed979156e807, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47500, isClient=false, ver=2.15.0#20230425-sha1:f98f7f35], TcpDiscoveryNode [id=c3977ab8-a39c-406d-83c2-f823ae1f7a44, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47501, isClient=false, ver=2.15.0#20230425-sha1:f98f7f35]]]
2025-02-27 10:49:17.740 [main] INFO  o.a.i.i.m.d.GridDiscoveryManager -   ^-- Baseline [id=0, size=2, online=2, offline=0]
2025-02-27 10:49:17.740 [main] INFO  o.a.ignite.internal.util.typedef.G - Node started : [stage="Configure system pool" (42 ms),stage="Start managers" (1252 ms),stage="Configure binary metadata" (126 ms),stage="Start processors" (1382 ms),stage="Init metastore" (435 ms),stage="Finish recovery" (0 ms),stage="Join topology" (2907 ms),stage="Await transition" (36 ms),stage="Await exchange" (814 ms),stage="Total time" (6994 ms)]
2025-02-27 10:49:18.237 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-27 10:49:18.237 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-27 10:49:18.266 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@62a6b83a
2025-02-27 10:49:20.708 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-02-27 10:49:21.512 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-02-27 10:49:21.720 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-02-27 10:49:21.720 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-02-27 10:49:22.261 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-02-27 10:49:22.600 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-02-27 10:49:25.448 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-02-27 10:49:25.477 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-02-27 10:49:25.501 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-02-27 10:49:25.655 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: reportUsingPOST_1
2025-02-27 10:49:25.661 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: reportUsingPOST_2
2025-02-27 10:49:25.667 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: reportUsingPOST_3
2025-02-27 10:49:25.673 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: reportUsingPOST_4
2025-02-27 10:49:25.678 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: reportUsingPOST_5
2025-02-27 10:49:25.894 [main] INFO  pangu.PgsqlBlobDbTest - Started PgsqlBlobDbTest in 27.031 seconds (JVM running for 28.137)
2025-02-27 10:49:26.270 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Started exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], crd=false, evt=DISCOVERY_CUSTOM_EVT, evtNode=c3977ab8-a39c-406d-83c2-f823ae1f7a44, customEvt=DynamicCacheChangeBatch [id=ec1ee454591-c486ad5f-e607-43fc-ad29-4d93e355ab23, reqs=ArrayList [DynamicCacheChangeRequest [cacheName=empCacheWithPgsqlBlobv1, hasCfg=true, nodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44, clientStartOnly=false, stop=false, destroy=false, disabledAfterStart=false]], exchangeActions=ExchangeActions [startCaches=[empCacheWithPgsqlBlobv1], stopCaches=null, startGrps=[empCacheWithPgsqlBlobv1], stopGrps=[], resetParts=null, finalizePartitionCounters=false, stateChangeRequest=null], startCaches=false], allowMerge=false, exchangeFreeSwitch=false]
2025-02-27 10:49:26.288 [exchange-worker-#57%bonus-calc%] ERROR o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Failed to initialize cache(s) (will try to rollback) [exchId=GridDhtPartitionExchangeId [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], discoEvt=DiscoveryCustomEvent [customMsg=DynamicCacheChangeBatch [id=ec1ee454591-c486ad5f-e607-43fc-ad29-4d93e355ab23, reqs=ArrayList [DynamicCacheChangeRequest [cacheName=empCacheWithPgsqlBlobv1, hasCfg=true, nodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44, clientStartOnly=false, stop=false, destroy=false, disabledAfterStart=false]], exchangeActions=ExchangeActions [startCaches=[empCacheWithPgsqlBlobv1], stopCaches=null, startGrps=[empCacheWithPgsqlBlobv1], stopGrps=[], resetParts=null, finalizePartitionCounters=false, stateChangeRequest=null], startCaches=false], affTopVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], super=DiscoveryEvent [evtNode=TcpDiscoveryNode [id=c3977ab8-a39c-406d-83c2-f823ae1f7a44, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47501, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47501, /0:0:0:0:0:0:0:1:47501, /127.0.0.1:47501, LAPTOP-TT07MCS9/**************:47501], discPort=47501, order=2, intOrder=2, lastExchangeTime=1740624566263, loc=true, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], topVer=2, msgTemplate=null, span=o.a.i.i.processors.tracing.NoopSpan@be8d8965, nodeId8=c3977ab8, msg=null, type=DISCOVERY_CUSTOM_EVT, tstamp=1740624566263]], nodeId=c3977ab8, evt=DISCOVERY_CUSTOM_EVT], caches=[o.a.i.i.processors.cache.ExchangeActions$CacheGroupActionData@d80c0172]]
org.apache.ignite.IgniteException: Spring application context resource is not injected.
	at org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory.create(CacheJdbcBlobStoreFactory.java:122)
	at org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory.create(CacheJdbcBlobStoreFactory.java:62)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.createCacheContext(GridCacheProcessor.java:1228)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.prepareCacheContext(GridCacheProcessor.java:1994)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.prepareCacheStart(GridCacheProcessor.java:1926)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.lambda$prepareStartCaches$55a0e703$1(GridCacheProcessor.java:1801)
	at <unknown class>.apply(Unknown Source)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.lambda$prepareStartCachesIfPossible$16(GridCacheProcessor.java:1771)
	at <unknown class>.handle(Unknown Source)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.prepareStartCaches(GridCacheProcessor.java:1798)
	at org.apache.ignite.internal.processors.cache.GridCacheProcessor.prepareStartCachesIfPossible(GridCacheProcessor.java:1769)
	at org.apache.ignite.internal.processors.cache.CacheAffinitySharedManager.processCacheStartRequests(CacheAffinitySharedManager.java:1000)
	at org.apache.ignite.internal.processors.cache.CacheAffinitySharedManager.onCacheChangeRequest(CacheAffinitySharedManager.java:886)
	at org.apache.ignite.internal.processors.cache.distributed.dht.preloader.GridDhtPartitionsExchangeFuture.onCacheChangeRequest(GridDhtPartitionsExchangeFuture.java:1472)
	at org.apache.ignite.internal.processors.cache.distributed.dht.preloader.GridDhtPartitionsExchangeFuture.init(GridDhtPartitionsExchangeFuture.java:979)
	at org.apache.ignite.internal.processors.cache.GridCachePartitionExchangeManager$ExchangeWorker.body0(GridCachePartitionExchangeManager.java:3348)
	at org.apache.ignite.internal.processors.cache.GridCachePartitionExchangeManager$ExchangeWorker.body(GridCachePartitionExchangeManager.java:3182)
	at org.apache.ignite.internal.util.worker.GridWorker.run(GridWorker.java:125)
	at java.lang.Thread.run(Thread.java:826)
2025-02-27 10:49:26.289 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partition release future [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], waitTime=0ms, futInfo=NA, mode=DISTRIBUTED]
2025-02-27 10:49:26.316 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partitions release latch: ClientLatch [coordinator=TcpDiscoveryNode [id=a375c3dd-35e1-43ae-94bf-ed979156e807, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47500, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47500, LAPTOP-TT07MCS9/**************:47500, /0:0:0:0:0:0:0:1:47500, /127.0.0.1:47500], discPort=47500, order=1, intOrder=1, lastExchangeTime=1740624556771, loc=false, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], ackSent=true, super=CompletableLatch [id=CompletableLatchUid [id=exchange, topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2]]]]
2025-02-27 10:49:26.317 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finished waiting for partition release future [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], waitTime=0ms, futInfo=NA, mode=LOCAL]
2025-02-27 10:49:26.319 [exchange-worker-#57%bonus-calc%] INFO  o.a.ignite.internal.exchange.time - Finished exchange init [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], crd=false]
2025-02-27 10:49:26.355 [sys-#62%bonus-calc%] INFO  o.a.i.i.p.cache.GridCacheProcessor - Can not finish proxy initialization because proxy does not exist, cacheName=empCacheWithPgsqlBlobv1, localNodeId=c3977ab8-a39c-406d-83c2-f823ae1f7a44
2025-02-27 10:49:26.363 [sys-#62%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Finish exchange future [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], err=null, rebalanced=false, wasRebalanced=true]
2025-02-27 10:49:26.365 [sys-#62%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Completed partition exchange [localNode=c3977ab8-a39c-406d-83c2-f823ae1f7a44, exchange=GridDhtPartitionsExchangeFuture [topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], evt=DISCOVERY_CUSTOM_EVT, evtNode=TcpDiscoveryNode [id=c3977ab8-a39c-406d-83c2-f823ae1f7a44, consistentId=0:0:0:0:0:0:0:1,127.0.0.1,************,**************:47501, addrs=ArrayList [0:0:0:0:0:0:0:1, 127.0.0.1, ************, **************], sockAddrs=HashSet [LAPTOP-TT07MCS9.mshome.net/************:47501, /0:0:0:0:0:0:0:1:47501, /127.0.0.1:47501, LAPTOP-TT07MCS9/**************:47501], discPort=47501, order=2, intOrder=2, lastExchangeTime=1740624566358, loc=true, ver=2.15.0#20230425-sha1:f98f7f35, isClient=false], rebalanced=false, done=true, newCrdFut=null], topVer=AffinityTopologyVersion [topVer=2, minorTopVer=2]]
2025-02-27 10:49:26.365 [exchange-worker-#57%bonus-calc%] INFO  o.a.i.i.p.c.GridCachePartitionExchangeManager - Skipping rebalancing (nothing scheduled) [top=AffinityTopologyVersion [topVer=2, minorTopVer=2], force=false, evt=DISCOVERY_CUSTOM_EVT, node=c3977ab8-a39c-406d-83c2-f823ae1f7a44]
2025-02-27 10:49:26.365 [sys-#62%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange timings [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], stage="Waiting in exchange queue" (0 ms), stage="Exchange parameters initialization" (2 ms), stage="Update caches registry" (1 ms), stage="Determine exchange type" (17 ms), stage="Preloading notification" (0 ms), stage="Wait partitions release [latch=exchange]" (0 ms), stage="Wait partitions release latch [latch=exchange]" (27 ms), stage="Wait partitions release [latch=exchange]" (0 ms), stage="After states restored callback" (0 ms), stage="WAL history reservation" (0 ms), stage="Exchange done" (47 ms), stage="Total time" (94 ms)]
2025-02-27 10:49:26.366 [sys-#62%bonus-calc%] INFO  o.a.i.i.p.c.d.d.p.GridDhtPartitionsExchangeFuture - Exchange longest local stages [startVer=AffinityTopologyVersion [topVer=2, minorTopVer=2], resVer=AffinityTopologyVersion [topVer=2, minorTopVer=2]]
2025-02-27 10:49:26.390 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-02-27 10:49:26.390 [Thread-41] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-02-27 10:49:26.390 [Thread-41] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-02-27 10:49:26.390 [shutdown-hook] INFO  o.a.ignite.internal.util.typedef.G - Invoking shutdown hook...
2025-02-27 10:49:26.391 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-02-27 10:49:26.397 [Thread-43] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-02-27 10:49:26.405 [Thread-43] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-02-27 10:49:26.453 [Thread-43] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-02-27 10:49:26.581 [Thread-43] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-02-27 10:49:26.582 [Thread-43] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-02-27 10:49:26.582 [Thread-43] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!

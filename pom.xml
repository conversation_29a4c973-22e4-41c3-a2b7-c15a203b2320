<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.caidaocloud</groupId>
	<artifactId>caidao-pangu-service</artifactId>
	<packaging>pom</packaging>
	<version>0.0.1-SNAPSHOT</version>
	<modules>
		<module>pangu-service</module>
		<module>compute-engine</module>
		<module>pangu-manager</module>
		<module>pangu-node</module>
        <module>pangu-core</module>
		<module>ignite-server</module>
	</modules>
	<parent>
		<groupId>com.caidaocloud</groupId>
		<version>1.0.2-SNAPSHOT</version>
		<artifactId>caidaocloud-parent</artifactId>
	</parent>
	<properties>
		<java.version>1.8</java.version>
		<elasticsearch.version>7.3.1</elasticsearch.version>
		<ignite.version>2.15.0</ignite.version>
		<kotlin.version>${kotlin.stdlib.version}</kotlin.version>
	</properties>

	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
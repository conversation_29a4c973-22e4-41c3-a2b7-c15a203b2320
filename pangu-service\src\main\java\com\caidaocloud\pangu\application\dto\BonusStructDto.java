package com.caidaocloud.pangu.application.dto;

import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.val;
import org.springframework.beans.BeanUtils;

import java.util.Map;

@Data
public class BonusStructDto {

    private String bid;

    private String name;

    private Map<String,String> i18nName;

    private ExecutionType executionType;

}

package com.caidaocloud.pangu.domain.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.ArrayList;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpImportDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.application.event.EmployeeStatsMessageDTO;
import com.caidaocloud.pangu.application.event.EmployeeStatsPublisher;
import com.caidaocloud.pangu.application.event.FinalStepExecMessageDTO;
import com.caidaocloud.pangu.application.event.FinalStepExecPublisher;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.pangu.domain.repository.IBonusReportRepository;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 奖金计算--获取奖金项
 * author Johnny Zhou
 * date 2023/7/24
 */
@Data
@Slf4j
public class BonusLedgerFinalStep extends BonusLedgerStep {

	private String composeName="计件工资项计算";

	private Long execTime;

	public BonusLedgerFinalStep() {
		super();
	}

	public BonusLedgerFinalStep(String ledgerId, String composeId) {
		super();
	}

	@Override
	public void exec(Map<String,String> context) {
		// Create and publish a message to trigger the final step execution
		FinalStepExecMessageDTO messageDto = new FinalStepExecMessageDTO(getBid(),getLedgerId());
		SpringUtil.getBean(FinalStepExecPublisher.class).publish(messageDto);
	}

	public void doExec() {
		EnumSimple status = new EnumSimple();
		status.setValue(String.valueOf(BonusLedgerStepStatus.EXECUTED.getValue()));
		setStatus(status);
		setExecTime(System.currentTimeMillis());
		SpringUtil.getBean(IBonusLedgerRepository.class).updateStep(this);

		// clearBonusResult();
		saveBonusResult();
		SpringUtil.getBean(EmployeeStatsPublisher.class).publish(new EmployeeStatsMessageDTO(getLedgerId()));
	}

	private void clearBonusResult() {
		SpringUtil.getBean(IBonusReportRepository.class).deleteByLedgerId(getLedgerId());
	}

	private void saveBonusResult() {
		BonusLedger ledger = BonusLedger.load(getLedgerId());
		BonusSchema schema = BonusSchema.load(ledger.getSchemaId());
		List<BonusStructItem> itemList = BonusStruct.listItems(schema.getStructId());
		Map<String, List<BonusStructItem>> identifierMap = Sequences.sequence(itemList)
				.toMap(BonusStructItem::getValueIdentifier);

		int pageSize = 1000;
		int pageNumber = 1;
		List<BonusLedgerEmp> empList;

		do {
			List<BonusReport> bonusReports = new ArrayList<>();
			Map<BonusReport, BonusReport> map = new HashMap<>();
			empList = loadEmp(ledger, pageNumber, pageSize);
			if (empList.isEmpty()) {
				break;
			}
			List<String> empIds = empList.stream()
										.map(BonusLedgerEmp::getEmpId)
										.collect(Collectors.toList());
			List<Map<String, String>> workNoLit = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
					.filterProperties(DataFilter.ne("deleted", Boolean.TRUE.toString())
							.andIn("empId", empIds), Lists.list("emp_id", "workno"), System.currentTimeMillis()).getItems();
			Map<String, String> workNoMap = workNoLit.stream().collect(Collectors.toMap(m -> m.get("empId"), m -> m.get("workno")));
			Map<String, String> empIdMap = workNoLit.stream().collect(Collectors.toMap(m -> m.get("workno"), m -> m.get("empId")));

			for (Map.Entry<String, List<BonusStructItem>> entry : identifierMap.entrySet()) {
				String identifier = entry.getKey();
				String empProp = entry.getValue().get(0).getWorknoProperty();
				String finalEmpProp = empProp;
				Option<MetadataPropertyVo> empOpt = Sequences.sequence(SpringUtil.getBean(MetadataOperatorService.class)
						.load(identifier)
						.fetchAllProperties()).find(p -> p.getProperty().equals(finalEmpProp));
				if (empOpt.isEmpty()) {
					throw new ServerException("Employee property '" + finalEmpProp + "' not found in metadata identifier: " + identifier);
				}
				if (empOpt.get().getDataType() == PropertyDataType.Emp) {
					empProp=empProp+"$workno";
				}
				List<String> propList = Sequences.sequence(entry.getValue()).map(BonusStructItem::getValueProperty)
						.toList();
				String month = DateUtil.format(ledger.getMonth(), "yyyyMM");
				Map<String, Map<String, String>> empItemMap = queryItem(identifier, propList, empProp, empIds,workNoMap,empIdMap, entry.getValue()
						.get(0).getMonthProperty(), month);

				for (BonusLedgerEmp emp : empList) {
					BonusReport bonusReport = map.computeIfAbsent(
						new BonusReport(ledger.getBid(), schema.getStructId(), schema.getBid(), ledger.getMonth(), emp.getEmpId()), 
						k -> k);
					Map<String, String> itemMap = empItemMap.getOrDefault(emp.getEmpId(),new HashMap<>());
					for (BonusStructItem item : entry.getValue()) {
						String value = itemMap.get(item.getValueProperty());
						// 检查value是否为数字，如果是则保留2位小数
						if (value != null && value.matches("^-?\\d+(\\.\\d+)?$")) {
							try {
								double numValue = Double.parseDouble(value);
								value = String.format("%.2f", Math.round(numValue * 100) / 100.0);
							} catch (NumberFormatException e) {
								// 解析失败，保持原值
								log.warn("Failed to parse number value: {}, for property: {}, empId: {}", 
									value, item.getValueProperty(), emp.getEmpId(), e);
							}
						}
						bonusReport.addItem(item.getBid(), value);
					}
				}
			}

			// for (BonusLedgerEmp emp : empList) {
			// 	emp.getStatus().setValue(BonusLedgerEmpCalcStatus.DONE.code);
			// 	SpringUtil.getBean(IBonusLedgerRepository.class).updateEmp(emp);
			// }

			saveBonusReports(Sequences.sequence(map.keySet()).toList());
			pageNumber++;
		} while (!empList.isEmpty());

	}

	// 新增方法：根据 ledger ID 分页加载 emp
	private List<BonusLedgerEmp> loadEmp(BonusLedger ledger, int pageNumber, int pageSize) {
		BonusLedgerEmpPageDto dto = new BonusLedgerEmpPageDto();
		dto.setPageNo(pageNumber);
		dto.setPageSize(pageSize);
		return ledger.loadEmp(dto).getItems();
	}

	/**
	 *
	 * @param identifier
	 * @param propList
	 * @param empProp
	 * @param empIds
	 * @param workNoMap
	 * @param empIdMap
	 * @param monthProperty
	 * @param month 格式yyyyMM
	 * @return
	 */
	// 新增方法：根据 identifier 和 empIds 查询数据
	private Map<String, Map<String,String>> queryItem(String identifier, List<String> propList, String empProp, List<String> empIds, Map<String, String> workNoMap, Map<String, String> empIdMap, String monthProperty, String month) {
		if (!propList.contains(empProp)) {
			propList.add(empProp);
		}
		List<String> worknoList = Sequences.sequence(empIds).map(workNoMap::get).toList();
		PageResult<Map<String, String>> result = DataQuery.identifier(identifier).limit(-1, 1)
				.filterProperties(DataFilter.in(empProp, worknoList).andEq(monthProperty, month), propList, System.currentTimeMillis());
		List<Map<String, String>> list = result.getItems();
		return list.stream().collect(Collectors.toMap(d ->
				empIdMap.getOrDefault(d.get(empProp),""), Function.identity(),(a,b)->a
		));
	}

	// 新增方法：保存 BonusReport 数据
	private void saveBonusReports(List<BonusReport> bonusReports) {
		SpringUtil.getBean(IBonusReportRepository.class).saveReport(bonusReports);
	}
}

package com.caidaocloud.pangu.domain.repository;

import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusReportRepository extends BaseRepository<BonusReport> {
	PageResult<BonusReport> selectPageByLedgerId(String ledgerId, BonusReportDto basePage);

	PageResult<BonusReport> selectPageByStructId(String structId, String startMonth, String endMonth, BonusReportDto dto);

	void saveReport(List<BonusReport> bonusReports);

	void deleteByLedgerId(String ledgerId);

	void removeEmpBatch(String bid, List<String> empId);

	int countByLedgerId(String bid);
}

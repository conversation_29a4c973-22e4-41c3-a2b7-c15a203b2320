package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

public class AddTime extends AbstractFunction {
    @Override
    public String getName() {
        return "ADD_TIME";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2, AviatorObject aviatorObject3) {
        LocalDateTime time = AviatorTimestamp.transformAviatorValue(aviatorObject1,env);
        val num = FunctionUtils.getNumberValue(aviatorObject2, env);
        val unit = FunctionUtils.getStringValue(aviatorObject3, env);
        val result = new AviatorTimestamp(time.plus(num.longValue(), ChronoUnit.valueOf(unit)));
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(num).append(",").append(unit).append(")=").append(result.getValue(env)).toString());
        }

        return result;
    }
}

package com.caidaocloud.pangu.application.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/8/14
 */
@Data
@ApiModel("奖金计算结果")
public class BonusReportDto extends BasePage {
	@ApiModelProperty("账套id")
	private String ledgerId;
	@ApiModelProperty("奖金结构id")
	private String structId;
	@ApiModelProperty("开始计算年月")
	private String startMonth;
	@ApiModelProperty("结束计算年月")
	private String endMonth;
	@ApiModelProperty("姓名/工号")
	private String keyword;
	@ApiModelProperty("合同公司")
	private String company;
	@ApiModelProperty("任职组织")
	private String organize;
	@ApiModelProperty("员工类型")
	private String empType;
	@ApiModelProperty("工作地")
	private String workPlace;
}

package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.dto.compose.ComposeExecDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementNodeExecDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@Component
public class ComposeExecFeignFallback implements ComposeExecFeign {

	@Override
	public Result<String> exec(ComposeExecDto dto) {
		return Result.fail();
	}

	@Override
	public Result<List<ArrangementNodeExecDto>> execNodesLogs(String execSeqId) {
		return Result.fail();
	}
}

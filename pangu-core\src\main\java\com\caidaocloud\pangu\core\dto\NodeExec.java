package com.caidaocloud.pangu.core.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class NodeExec {
    private String execSeqId;
    private String execNodeSeqId;
    private String arrangementVid;
    private String arrangementNodeId;
    private boolean skip = false;
    private Map<String, String> context = new HashMap<>();
    private String tenantId;

    @Data
    public static class SubNodeExec extends NodeExec {
        private int from;
        private int to;
        private String cacheId;
        private String resultCacheId;
    }
}

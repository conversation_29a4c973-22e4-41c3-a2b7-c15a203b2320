package com.caidaocloud.pangu.manager.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@TableName("arrangement_node_exec")
@Data
public class ArrangementNodeExecPO {

    @TableId
    private String id;

    public static final String identifier = "entity.pangu.ArrangementNodeExecPO";

    private String arrangementId;

    private String arrangementVid;

    private String execSeqId;

    private String execNodeSeqId;

    private String arrangementNodeId;

    private String arrangementNodeName;

    private String arrangementNodeType;

    private String detail;

    private ArrangementNodeExec.Status status;

    private boolean skip;

    private String context; // JSON 字符串

    private long waitFrom;

    private long started;

    private long ended;

    private String panguNodeId;

    private String host;

    private int port;

    private String cacheId;

    private String resultCacheId;

    private long total;

    private int step;

    private String actualTenantId;

    // 转换方法：从 ArrangementNodeExec 转换为 ArrangementNodeExecPO
    public static ArrangementNodeExecPO fromArrangementNodeExec(ArrangementNodeExec exec) {
        ArrangementNodeExecPO po = ObjectConverter.convert(exec, ArrangementNodeExecPO.class);
        po.setContext(FastjsonUtil.toJson(exec.getContext())); // 将 Map 转换为 JSON 字符串
        return po;
    }

    // 转换方法：从 ArrangementNodeExecPO 转换为 ArrangementNodeExec
    public ArrangementNodeExec toArrangementNodeExec() {
        ArrangementNodeExec exec =ObjectConverter.convert(this, ArrangementNodeExec.class);
        exec.setContext(FastjsonUtil.toObject(context,Map.class)); // 将 JSON 字符串转换为 Map
        return exec;
    }

} 
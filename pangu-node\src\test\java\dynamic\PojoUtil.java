package dynamic;

import net.bytebuddy.ByteBuddy;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public class PojoUtil {
	// public static Class<?> testPojoClass(){
	// 	new ByteBuddy()
	// 			.subclass(Object.class)
	// 			.name("com.example.dynamic.TestPojo");
	// }

	@Test
	public  void test(){
		int a=101;
		int b =3;
		// print(a, b)
		// System.out.println(2==(2/1+(2%1)>1));
	}

	private void print(int a, int b) {
		System.out.println(a % b > 0 ? 1 : 0);
	}
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.val;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class TimeToStr extends AbstractFunction {
    @Override
    public String getName() {
        return "TIME_TO_STR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val time = AviatorTimestamp.transformAviatorValue(aviatorObject1,env);
        val pattern = FunctionUtils.getStringValue(aviatorObject2, env).replaceAll("T", "'T'");
        val result = DateTimeFormatter.ofPattern(pattern).format(time);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(time).append(",").append(pattern).append(")=").append(result).toString());
        }
        return new AviatorString(result);
    }
}

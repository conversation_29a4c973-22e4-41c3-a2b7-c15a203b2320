package com.caidaocloud.pangu.application.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import cn.afterturn.easypoi.handler.inter.IWriter;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.pangu.application.dto.BonusLedgerDto;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.application.dto.emp.EmpInfoDto;
import com.caidaocloud.pangu.application.feign.MasterdataFeign;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.repository.IEmpRepository;
import com.caidaocloud.pangu.infrastructure.util.ExcelUtils;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.pangu.interfaces.vo.BonusReportPageVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.LanguageUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Triple;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/8/14
 */
@Data
@Service
@Slf4j
public class BonusReportService {

	@Autowired
	private BonusLedgerService bonusLedgerService;
	@Autowired
	private BonusSchemaService bonusSchemaService;
	@Autowired
	private MasterdataFeign masterdataFeign;
	@Resource
	private IEmpRepository empRepository;
	@Autowired
	private MetadataOperatorService metadataOperatorService;


	public BonusReportPageVo loadReport(BonusReportDto dto) {
		PageResult<BonusReport> report = BonusReport.loadReport(dto.getStructId(), dto.getStartMonth(), dto.getEndMonth(), dto);
		BonusStruct struct = BonusStruct.load(dto.getStructId());
		List<BonusStructItem> structItems = struct.listItems();
		return formatReport(report,struct, structItems);
	}

	private BonusReportPageVo formatReport(PageResult<BonusReport> report, BonusStruct struct, List<BonusStructItem> bonusItems) {
		Map<String, List<Triple<String, String, String>>> identifierPropertyGroup = Sequences.sequence(struct.getBasic())
				.map(field -> {
					String[] parts = field.split("@");
					return Triple.triple(parts[0], parts[1], field); // Create Pair of (identifier, property)
				}).toMap(Triple::first);

		// Create headers from bonus items that should be shown in statistics
		List<BonusReportPageVo.Header> headers =Lists.list(new BonusReportPageVo.Header("ledger", "奖金账套"),new BonusReportPageVo.Header("month","计算年月"));

		// 遍历 identifierPropertyGroup
		for (Map.Entry<String, List<Triple<String, String, String>>> entry : identifierPropertyGroup.entrySet()) {
			String identifier = entry.getKey();
			List<Triple<String, String, String>> properties = entry.getValue();

			// 获取元数据定义
			MetadataVo metadata = metadataOperatorService.load(identifier);
			if (metadata != null) {
				List<MetadataPropertyVo> metadataPropertyList = metadata.fetchAllProperties();
				for (Triple<String, String, String> property : properties) {
					String propertyName = property.second();
					MetadataPropertyVo metadataPropertyVo = Sequences.sequence(metadataPropertyList)
							.find(prop -> prop.getProperty().equals(propertyName))
							.get();
					// 组装 header
					BonusReportPageVo.Header header = new BonusReportPageVo.Header(property.third(), metadataPropertyVo.getName());
					// 设置 dateFormat 属性
					if (metadataPropertyVo.getDataType() == PropertyDataType.Timestamp) {
						header.setDateFormat(true);
					}
					headers.add(header);
				}
			}
		}
		headers.addAll(Sequences.sequence(bonusItems)
				.map(item -> new BonusReportPageVo.Header(item.getBid(), item.getName()))
				.toList());

		List<String> empIds = Sequences.sequence(report.getItems())
				.map(BonusReport::getEmpId).toList();
		Map<String, Map<String, Object>> empInfoMap = empRepository.loadEmps(empIds, identifierPropertyGroup);
		List<BonusReportPageVo.Header> dateHeaders = Sequences.sequence(headers)
				.filter(BonusReportPageVo.Header::isDateFormat).toList();
		
		// 获取所有ledgerId
		List<String> ledgerIds = Sequences.sequence(report.getItems())
				.map(BonusReport::getLedgerId)
				.toList();

		// 批量查询ledger信息
		List<BonusLedgerDto> ledgers = bonusLedgerService.findByIds(ledgerIds);
		Map<String, String> ledgerNameMap = ledgers.stream()
				.collect(Collectors.toMap(BonusLedgerDto::getBid, ledger -> {
					// 假设i18nName中包含默认语言的名称，或者直接使用第一个值
					Map<String, String> i18nName = ledger.getI18nName();
					return LanguageUtil.getCurrentLangVal(i18nName);
				}));

		
		List<Map<String,Object>> list = Sequences.sequence(report.getItems()).map(br -> {
			Map<String, Object> empInfo = empInfoMap.get(br.getEmpId());
			
			// 添加ledger属性
			String ledgerName = ledgerNameMap.getOrDefault(br.getLedgerId(), "");
			empInfo.put("ledger", ledgerName);
			
			// 添加month属性 - 格式化为yyyyMM
			String formattedMonth = "";
			if (br.getMonth() != null) {
				formattedMonth = DateUtil.format(br.getMonth(), "yyyyMM");
			}
			empInfo.put("month", formattedMonth);
			
			for (BonusReportPageVo.Header header : dateHeaders) {
				String property = header.getDataIndex();
				Object value = empInfo.get(property);
				if (value != null) {
					try {
						empInfo.put(property, DateUtil.formatDate(Long.parseLong(value.toString())));
					}
					catch (NumberFormatException e) {
						log.warn("Failed to parse date value: {}", value);
					}
				}
			}
			List<BonusReport.BonusItem> items = br.getItem();
			for (BonusReport.BonusItem item : items) {
				empInfo.put(item.getBid(), item.getValue());
			}
			return empInfo;
		}).toList();

		// Convert the report to BonusReportPageVo
		BonusReportPageVo vo = new BonusReportPageVo(list, report.getPageNo(), report.getPageSize(), report.getTotal());
		vo.setHeaders(headers);
		return vo;
	}

	public void exportReport(BonusReportDto dto, HttpServletResponse response) {
		try {
			int pageNo = 0;
			dto.setPageSize(500);
			BonusReportPageVo vo = null;
			IWriter<Workbook> writer = null;

			do {
				dto.setPageNo(++pageNo);
				vo = loadReport(dto);
				if (writer == null) {
					List<ExcelExportEntity> header = new ArrayList<>();
					header.addAll(Sequences.sequence(vo.getHeaders())
							.map(h -> new ExcelExportEntity(h.getTitle(), h.getDataIndex())).toList());
					List<String> dateTitle = Sequences.sequence(vo.getHeaders()).filter(h -> h.isDateFormat())
							.map(BonusReportPageVo.Header::getTitle).toList();
					ExportParams exportParams = new ExportParams();
					// exportParams.setDataHandler(new ExcelDataHandlerDefaultImpl() {
					// 	@Override
					// 	public String[] getNeedHandlerFields() {
					// 		return dateTitle.toArray(new String[0]);
					// 	}
					//
					// 	@Override
					// 	public Object exportHandler(Object obj, String name, Object value) {
					// 		if (value == null) {
					// 			return value;
					// 		}
					// 		return DateUtil.format((Long) value, "yyyy-MM");
					// 	}
					// });
					writer = ExcelExportUtil.exportBigExcel(exportParams, header);
				}
				writer.write(vo.getItems());
			}
			while (vo.getTotal() > pageNo * 500);
			final Workbook workbook = writer.get();
			ExcelUtils.downLoadExcel("计件工资报表", response, workbook);
		}
		catch (IOException e) {
			throw new ServerException("报表导出失败", e);
		}
	}

	private List<ExcelExportEntity> baseHeader() {
		return Lists.list(
				new ExcelExportEntity("姓名", "name"),
				new ExcelExportEntity("合同公司", "companyTxt"),
				new ExcelExportEntity("任职组织", "organizeTxt"),
				new ExcelExportEntity("任职岗位", "postTxt"),
				new ExcelExportEntity("员工类型", "empType"),
				new ExcelExportEntity("工作地", "workplaceTxt"),
				new ExcelExportEntity("员工状态", "empStatus"),
				new ExcelExportEntity("入职日期", "hireDate"),
				new ExcelExportEntity("离职日期", "leaveDate"),
				new ExcelExportEntity("计算年月", "month"));
	}

	private List<ExcelExportEntity> resultHeader() {
		return Lists.list(new ExcelExportEntity("工号", "workno"),
				new ExcelExportEntity("员工姓名", "name"));
	}

	public BonusReportPageVo loadResult(BonusReportDto dto) {
		PageResult<BonusReport> report = BonusReport.loadReport(dto.getLedgerId(), dto);
		BonusLedger ledger = BonusLedger.load(dto.getLedgerId());
		List<BonusStructItem> bonusItems = bonusSchemaService.loadBonusItem(ledger.getSchemaId());
		return formatResult(report,bonusItems);
	}

	private BonusReportPageVo formatResult(PageResult<BonusReport> report, List<BonusStructItem> bonusItems) {

		// Create headers from bonus items that should be shown in statistics
		List<BonusReportPageVo.Header> headers = Sequences.sequence(bonusItems)
				.map(item -> new BonusReportPageVo.Header(item.getBid(), item.getName()))
				.toList();
		Map<String, EmpInfoDto> empInfoVoMap = masterdataFeign.loadEmpInfo(Maps.map("empIds", Sequences.sequence(report.getItems())
						.map(BonusReport::getEmpId).toList())).getData()
				.stream()
				.collect(Collectors.toMap(EmpInfoDto::getEmpId, emp -> emp,(a, b) -> {
					log.warn("Duplicate key:{}", a);
					return a;
				}));
		List<BonusLedgerEmpVo> voList = Sequences.sequence(report.getItems()).map(br -> {
			BonusLedgerEmpVo vo = ObjectConverter.convert(br, BonusLedgerEmpVo.class);
			EmpInfoDto empInfoDto = empInfoVoMap.get(vo.getEmpId());
			if (empInfoDto != null) {
				BeanUtils.copyProperties(empInfoDto, vo);
			}
			return vo;
		}).toList();

		List<Map<String,Object>> list = Sequences.sequence(voList).map(data -> {
			Map<String,Object> map = JsonEnhanceUtil.toObject(data, Map.class);
			List<BonusReport.BonusItem> items = data.getItem();
			for (BonusReport.BonusItem item : items) {
				map.put(item.getBid(), item.getValue());
			}
			return map;
		}).toList();
		// Convert the report to BonusReportPageVo
		BonusReportPageVo vo = new BonusReportPageVo(list, report.getPageNo(), report.getPageSize(), report.getTotal());
		vo.setHeaders(headers);
		return vo;
	}

	public void exportResult(BonusReportDto dto, HttpServletResponse response) {
		try {
			int pageNo = 0;
			dto.setPageSize(500);
			BonusReportPageVo vo = null;
			IWriter<Workbook> writer = null;

			do {
				dto.setPageNo(++pageNo);
				vo = loadResult(dto);
				if (writer == null) {
					List<ExcelExportEntity> header = baseHeader();
					header.addAll(Sequences.sequence(vo.getHeaders())
							.map(h -> new ExcelExportEntity(h.getTitle(), h.getDataIndex())).toList());
					writer = ExcelExportUtil.exportBigExcel(new ExportParams(), header);
				}
				List<Map<String,Object>> list = Sequences.sequence(vo.getItems()).map(data -> {
					data.put("name", String.format("%s(%s)", data.get("workno"), data.get("name")));
					if (data.get("hireDate") != null) {
						data.put("hireDate", DateUtil.formatDate((Long) data.get("hireDate")));
					}
					JSONObject empType = (JSONObject) data.get("empType");
					if (empType != null)
						data.put("empType", empType.get("text"));
					if (data.get("leaveDate") != null) {
						data.put("leaveDate", DateUtil.formatDate((Long) data.get("leaveDate")));
					}
					JSONObject empStatus = (JSONObject) data.get("empStatus");
					if (empStatus != null)
						data.put("empStatus", empStatus.get("text"));
					data.put("month", DateUtil.format((Long) data.get("month"), "yyyy-MM"));
					return data;
				}).toList();
				writer.write(list);
			}
			while (vo.getTotal() > pageNo * 500);
			final Workbook workbook = writer.get();
			ExcelUtils.downLoadExcel("计件工资计算结果", response, workbook);
		}
		catch (IOException e) {
			throw new ServerException("报表导出失败", e);
		}
	}
}

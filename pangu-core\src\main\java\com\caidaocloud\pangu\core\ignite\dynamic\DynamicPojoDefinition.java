package com.caidaocloud.pangu.core.ignite.dynamic;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.cache.configuration.Factory;
import javax.sql.DataSource;

import com.caidaocloud.pangu.core.ignite.DatasourceProperties;
import com.googlecode.totallylazy.Sequences;
import com.zaxxer.hikari.HikariDataSource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.postgresql.ds.PGSimpleDataSource;
import org.postgresql.osgi.PGDataSourceFactory;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@NoArgsConstructor
public class DynamicPojoDefinition implements Serializable {

	private String className;
	private Class<?> superClass = Object.class;

	private String tableName;
	private DynamicFieldDefinition keyField;
	private DynamicFieldDefinition[] fields;
	private DatasourceProperties datasourceProperties;
	private DefType defType=DefType.COMMON;

	public enum DefType {
		PAAS,DYNAMIC,COMMON
	}



	public Factory<DataSource> dsf(){
		return () -> {
			// HikariDataSource driverManagerDataSource = new HikariDataSource();
			// driverManagerDataSource.setDriverClassName(datasourceProperties.getDriverClassName());
			// driverManagerDataSource.setJdbcUrl(datasourceProperties.getJdbcUrl());
			// driverManagerDataSource.setUsername(datasourceProperties.getUsername());
			// driverManagerDataSource.setPassword(datasourceProperties.getPassword());
			PGSimpleDataSource dataSource = new PGSimpleDataSource();

			// 设置数据库连接参数
			dataSource.setURL(datasourceProperties.getJdbcUrl()); // 数据库服务器地址
			dataSource.setUser(datasourceProperties.getUsername());                      // 用户名
			dataSource.setPassword(datasourceProperties.getPassword());              // 密码
			return dataSource;
			// // 创建 PGDataSourceFactory 实例
			// PGDataSourceFactory factory = new PGDataSourceFactory();
			//
			// // 配置数据库连接属性
			// Properties props = new Properties();
			// props.setProperty("jdbc.url", datasourceProperties.getJdbcUrl()); // JDBC URL
			// props.setProperty("user", datasourceProperties.getUsername());                                 // 用户名
			// props.setProperty("password", datasourceProperties.getPassword());                         // 密码
			//
			// // 使用工厂创建 DataSource
			// try {
			// 	return factory.createDataSource(props);
			// }
			// catch (SQLException e) {
			// 	throw new RuntimeException("无法创建 DataSource: " + e.getMessage(), e);
			// }
			// return driverManagerDataSource;
		};
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class DynamicFieldDefinition implements Serializable {
		private String name;
		private String dbName;
		private Class<?> type;

		public String fetchName(){
			return name;
		}

	}

	@Data
	@NoArgsConstructor

	public static class PaasDynamicFieldDefinition extends DynamicFieldDefinition implements Serializable {
		private String paasName;


		public PaasDynamicFieldDefinition(String name, String dbName, Class<?> type, String paasName) {
			super(name, dbName, type);
			this.paasName = paasName;
		}

		@Override
		public String fetchName() {
			return paasName;
		}
	}

	public Map<String,String> dbNameMapping(){
		Map<String, String> map = new HashMap<>();
		if (fields != null && fields.length > 0) {
			for (DynamicFieldDefinition field : fields) {
				map.put(field.fetchName(), field.getDbName());
			}
		}
		return map;
	}

	public List<String> fieldNames(){
		return Sequences.sequence(fields).map(DynamicFieldDefinition::fetchName).toList();
	}
}

package com.caidaocloud.compute.remote.framework.core;

import java.util.List;

import com.caidaocloud.compute.remote.framework.actor.ActorInfo;
import com.caidaocloud.compute.remote.framework.util.PortUtil;

/**
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
public interface RemoteInitializer {
	void init(InitializerConfig config);

	void initHandler(ActorInfo actorInfo);

	int getPort();

	default int findAvailablePort(int startPort, int endPort) {
		for (int port = startPort; port <= endPort; port++) {
			if (!PortUtil.isPortInUse(port)) {
				return port;
			}
		}
		throw new RuntimeException("No available ports in the specified range.");
	}
}

package com.caidaocloud.remote.akka.core;

import java.util.HashMap;
import java.util.Map;

import akka.actor.ActorRef;
import akka.actor.ActorSystem;
import akka.routing.RoundRobinPool;
import com.caidaocloud.compute.remote.framework.actor.ActorInfo;
import com.caidaocloud.compute.remote.framework.core.DynamicPortInitializer;
import com.caidaocloud.compute.remote.framework.core.InitializerConfig;
import com.caidaocloud.remote.akka.actor.AkkaProxyActor;
import com.caidaocloud.remote.akka.constant.AkkaConstant;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Slf4j
public class AkkaInitializer extends DynamicPortInitializer {


	private ActorSystem actorSystem;

	public AkkaInitializer(InitializerConfig initializerConfig) {
		super(initializerConfig);
		this.init(initializerConfig);
	}

	public ActorSystem getActorSystem() {
		return actorSystem;
	}

	@Override
	public void init(InitializerConfig config) {
		Map<String, Object> overrides = new HashMap<>();
		overrides.put("akka.remote.artery.canonical.port", config.getPort());
		overrides.put("akka.remote.artery.canonical.hostname", config.getHost());
		Config defaultConfig = ConfigFactory.parseMap(overrides)
				.withFallback(ConfigFactory.load(AkkaConstant.AKKA_CONFIG));
		actorSystem = ActorSystem.create(config.getServerName() , defaultConfig);
	}

	@Override
	public void initHandler(ActorInfo actorInfo) {
		int cores = Runtime.getRuntime().availableProcessors();
		String rootPath = actorInfo.getPath();
		ActorRef actorRef = actorSystem.actorOf(AkkaProxyActor.props(actorInfo)
				.withRouter(new RoundRobinPool(cores)), rootPath);
		if (log.isDebugEnabled()) {
			log.info("Akka actor inited，Actor={}",actorRef);
		}
	}

	@Override
	public int getPort() {
		return actorSystem.provider().getDefaultAddress().getPort().orElse(-1);
	}



}

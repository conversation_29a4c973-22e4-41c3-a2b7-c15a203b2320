package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.application.dto.ApproveConfigDto;
import com.caidaocloud.pangu.domain.entity.PeriodDate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("奖金方案vo")
public class BonusSchemaVo {

    private String bid;

    private String name;

    private String bonusStructId;

    private String bonusStructName;

    private ConditionTree coverage;

    private Integer month;

    private Integer day;

    private String remark;

    @ApiModelProperty("选人周期")
    private PeriodDate selection;

    @ApiModelProperty("特殊条件")
    private Boolean special;

    @ApiModelProperty("入职日期")
    private PeriodDate hireDate;

    @ApiModelProperty("离职日期")
    private PeriodDate terminationDate;

    private ApproveConfigDto approveConfig;
    //
    // public static BonusSchemaVo fromEntity(BonusSchema schema, BonusStruct struct){
    //     val vo = FastjsonUtil.convertObject(schema, BonusSchemaVo.class);
    //     vo.setBonusStructName(struct.getName());
    //     vo.setApproveConfig(FastjsonUtil.convertObject(schema.getApproveConfig(), ApproveConfigDto.class));
    //     return vo;
    // }
    //
    // public static List<BonusSchemaVo> fromEntity(List<BonusSchema> schemas, List<BonusStruct> structs){
    //     val list = FastjsonUtil.convertList(schemas, BonusSchemaVo.class);
    //     list.forEach(schema->{
    //         structs.stream().filter(it->it.getBid()
    //                 .equals(schema.getBonusStructBid())).findFirst()
    //                 .ifPresent(it->schema.setBonusStructName(it.getName()));
    //     });
    //     return list;
    // }

}


package com.caidaocloud.pangu.domain.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.domain.repository.IBonusStructItemRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructRepository;
import com.caidaocloud.pangu.interfaces.vo.BonusStructVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

/**
 * 奖金结构--奖金项配置
 */
@Data
public class BonusStructItem extends DataSimple {

    private String structId;
    private String name;
    private String code;
    private DictSimple type;
    private String valueIdentifier;
    private String valueProperty;
    private String worknoProperty;
    private String monthProperty;
    private String remark;

    private Integer sort;


    public static final String BONUS_STRUCT_ITEM_IDENTIFIER = "entity.bonus.BonusStructItem";

    public BonusStructItem() {
        setIdentifier(BONUS_STRUCT_ITEM_IDENTIFIER);
        sort=65535;
    }

    public String create() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
        if (DataQuery.identifier(BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER)
                .count(DataFilter.eq("name", name).andEq("structId", getStructId())
                        .andNe("deleted", Boolean.TRUE.toString()), System.currentTimeMillis()) > 0) {
            throw new ServerException("名称重复");
        }
        SpringUtil.getBean(IBonusStructItemRepository.class).insert(this);
        return this.getBid();
    }

    public void update(){
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        if (DataQuery.identifier(BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER)
                .count(DataFilter.eq("name", name)
                        .andEq("structId", getStructId())
                        .andNe("deleted", Boolean.TRUE.toString())
                        .andNe("bid", getBid()), System.currentTimeMillis()) > 0) {
            throw new ServerException("名称重复");
        }
        SpringUtil.getBean(IBonusStructItemRepository.class).updateById(this);
    }

    public void delete() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        SpringUtil.getBean(IBonusStructItemRepository.class).delete(this);
    }
}

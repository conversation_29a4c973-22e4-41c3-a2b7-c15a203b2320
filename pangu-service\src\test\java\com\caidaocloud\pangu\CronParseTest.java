package com.caidaocloud.pangu;

import java.util.concurrent.TimeUnit;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.infrastructure.cron.constraint.DailyConstraint;
import com.caidaocloud.pangu.infrastructure.cron.util.CronUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronConstraint;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.TriggerContext;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.scheduling.support.SimpleTriggerContext;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/17
 */
public class CronParseTest {

	@Test
	public void SecondRangeError(){
		try {
			Cron cron = CronUtil.parse("0,2 23 0 ? * 1-5 *");
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}

		// CronSequenceGenerator generator = new CronSequenceGenerator("* 0 0 ? * * *");
	}

	@Test
	public void SecondBTError(){
		try {
			Cron cron = CronUtil.parse("0-2 23 0 ? * 1-5 *");
			cron.validate();
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}

		// CronSequenceGenerator generator = new CronSequenceGenerator("* 0 0 ? * * *");
	}

	@Test
	public void SecondAnyError(){
		try {
			Cron cron = CronUtil.parse("* 23 0 ? * 1-5 *");
			cron.validate();
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}
	}


	@Test
	public void MinuteRangeError(){
		try {
			Cron cron = CronUtil.parse("0 22,23 0 ? * 1-5 *");
			cron.validate();
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}

		// CronSequenceGenerator generator = new CronSequenceGenerator("* 0 0 ? * * *");
	}

	@Test
	public void MinuteBTError(){
		try {
			Cron cron = CronUtil.parse("0 22-23 0 ? * 1-5 *");
			cron.validate();
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}

		// CronSequenceGenerator generator = new CronSequenceGenerator("* 0 0 ? * * *");
	}

	@Test
	public void MinuteAnyError(){
		try {
			Cron cron = CronUtil.parse("0 * 0 ? * 1-5 *");
			cron.validate();
		}
		catch (Exception e) {
			System.out.println(e.getMessage());
			Assert.assertEquals(IllegalArgumentException.class, e.getClass());
		}
	}

	@Test
	public void NextExecTimeTest() {
		long except = DateUtil.getCurrentTimestamp() + TimeUnit.DAYS.toMillis(1);
		long nextExecTime = CronUtil.nextExecTime("0 0 0 ? * * *");
		Assert.assertEquals(except, nextExecTime);
	}

	@Test
	public void t(){
		System.out.println(FastjsonUtil.toJson(DataFilter.in("emp$workno", Lists.newArrayList("01704464", "01753547", "01814819", "01914338", "01564967", "01862021", "01564957", "01910007", "01910008", "01865316", "01865320", "01696071", "01735963", "01886586"))));
	}

	@Test
	public void rp(){
		String s = ".dict.value";
		Assert.assertEquals("$dict$value", s.replaceAll("\\.", "\\$"));
	}


}

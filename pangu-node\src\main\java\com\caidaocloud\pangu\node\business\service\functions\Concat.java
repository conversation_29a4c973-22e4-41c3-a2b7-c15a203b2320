package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class Concat extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "CONCAT";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        StringBuilder log = new StringBuilder(getName()).append("(");
        StringBuilder sb = new StringBuilder("");
        for(AviatorObject aviatorObject : aviatorObjects){
            val param = FunctionUtils.getStringValue(aviatorObject, env);
            sb.append(param);
            log.append(param).append(",");
        }
        val result = sb.toString();
        if(FunctionLogTool.logEnabled()){
            log.setLength(log.length()-1);
            log.append(")=").append(result);
        }
        return new AviatorString(result);
    }
}

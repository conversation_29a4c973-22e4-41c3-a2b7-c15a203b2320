package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Lists;
import lombok.val;

import java.util.Map;

public class Lines extends AbstractVariadicFunction {
    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        val result = Lists.list(aviatorObjects).stream().reduce((a,b)->b).get();
        if(FunctionLogTool.logEnabled()){
            val value = result.getValue(env);
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append("...").append(",").append(value).append(")=").append(value).toString());
        }
        return result;
    }

    @Override
    public String getName() {
        return "LINES";
    }
}

package com.caidaocloud.pangu.application.feign;

import java.util.List;
import java.util.Map;

import com.caidaocloud.pangu.application.dto.emp.EmpInfoDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = "${feign.rename.caidaocloud-masterdata-service:caidaocloud-masterdata-service}", fallback = MasterdataFeignFallback.class, configuration = FeignConfiguration.class, contextId = "masterdataFeign")
public interface MasterdataFeign {

    @PostMapping("/api/masterdata/v2/emp/empInfo/list")
    Result<List<EmpInfoDto>> loadEmpInfo(Map map);
}

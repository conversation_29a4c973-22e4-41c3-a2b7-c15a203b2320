package com.caidaocloud.pangu.node.business.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.core.ignite.IgniteCacheContext;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.node.common.feign.PanguManagerFeign;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class TaskService {

    @Autowired
    private IgniteUtil igniteUtil;

    @Autowired
    private PanguManagerFeign panguManagerFeign;

    public IgniteCacheContext preLoadData(String execSeqId, String cacheId, TaskDef.Detail taskDef, Map<String, String> context, List<Arrangement.Node> nodes) {
        val dsType = taskDef.getDsType();
        DataFilter filter = null;
        if(StringUtils.isNotBlank(taskDef.getFilter())){
            filter = (DataFilter) ExpressionTool.exec(taskDef.getTaskDefVid(), taskDef.getFilter(), context);
        }
        if(TaskDef.SourceType.PAAS.equals(dsType)){
            val identifier = taskDef.getDsIdentifier();
            return igniteUtil.dynamicPojoCache(cacheId, identifier, filter, 0l);
        }else if(TaskDef.SourceType.TASK_OUTPUT.equals(dsType)){
            val scTaskDefId = taskDef.getDsTaskDefId();
            val scNode = nodes.stream().filter(it->scTaskDefId.equals(it.getTaskDefId())).findFirst().orElseThrow(()->new ServerException("sc task not exist"));
            String scCacheId = panguManagerFeign.getResultCacheId(execSeqId, scNode.getId()).getData();
            return igniteUtil.copyCache(cacheId, scCacheId, filter);
        }else{
            throw new ServerException("unsupported datasource type yet");
        }
    }

    public void removePreLoadData(String cacheId, boolean empKeyOpen) {
        igniteUtil.getContext(cacheId).destroy();
        if(empKeyOpen){
            igniteUtil.getContext("work." +cacheId).destroy();
            igniteUtil.getContext("private." +cacheId).destroy();
        }
    }

    public void preLoadEmpData(String cacheId, long time) {
        igniteUtil.dynamicPojoCache("work." +cacheId, "entity.hr.EmpWorkInfo", null, time);
        igniteUtil.dynamicPojoCache("private." +cacheId, "entity.hr.EmpPrivateInfo", null, time);
    }

    public void initResultCache(String resultCacheId, List<TaskDef.Rule> rules, boolean isGroup) {
        Map<String, Class<?>> ruleClass = Maps.map();
        rules.forEach(rule->{
            val key = rule.getRuleId();

            switch (rule.getRuleDataType()) {
                case Timestamp:
                case Int:
                case Number:
                    ruleClass.put(key, BigDecimal.class);
                    break;
                case String:
                    ruleClass.put(key, String.class);
                    break;
            }
        });
        if(isGroup){
            ruleClass.put("key", String.class);
        }
        igniteUtil.keyValueCache(resultCacheId, String.class, ruleClass);
    }

    public static void main(String[] args) {
        System.out.println(FastjsonUtil.toJson(Maps.map("a",String.class)));
    }
}

package com.caidaocloud.pangu.infrastructure.ignite.store;

/**
 *
 * <AUTHOR>
 * @date 2023/7/28
 */
public interface DbContext {
	String getConnUrl();
	String getUser();
	String getPwd();

	String generateCreateTableSql(String cacheName);

	String generateUpdateSql(String cacheName);

	String generateInsertSql(String cacheName);

	String generateDeleteSql(String cacheName);

	String generateLoadSql(String cacheName);
}

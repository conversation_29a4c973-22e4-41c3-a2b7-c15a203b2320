package com.caidaocloud.pangu.core.ignite.store;

import java.io.Serializable;

import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import lombok.Data;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;

/**
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
public class CustomPojoStoreFactory<K,V> extends CacheJdbcPojoStoreFactory<K,V>  implements CustomStoreFactory, Serializable {
	// private static final long serialVersionUID = -2830656873489159544L;
	private static final long serialVersionUID = 0;
	private DynamicPojoDefinition pojoDef;

}

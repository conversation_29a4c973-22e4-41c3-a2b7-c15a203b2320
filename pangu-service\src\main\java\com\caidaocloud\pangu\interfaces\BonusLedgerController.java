package com.caidaocloud.pangu.interfaces;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.locks.Lock;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.application.dto.BonusAccountEmpDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerExecDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerPageDto;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpSimpleVo;
import com.caidaocloud.pangu.application.dto.compose.ArrangementProgressDto;
import com.caidaocloud.pangu.application.service.BonusLedgerEmpImportService;
import com.caidaocloud.pangu.application.service.BonusLedgerService;
import com.caidaocloud.pangu.application.service.BonusReportService;
import com.caidaocloud.pangu.infrastructure.util.LangUtil;
import com.caidaocloud.pangu.interfaces.vo.BonusComposeResult;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerComposeVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerVo;
import com.caidaocloud.pangu.interfaces.vo.BonusReportPageVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/24
 */

@RestController
@RequestMapping("/api/pangu/v1/bonus/ledger")
@Api(value = "/api/pangu/v1/bonus/ledger", tags = "奖金账套")
public class BonusLedgerController {

	@Autowired
	private BonusLedgerService bonusLedgerService;
	@Autowired
	private BonusReportService bonusReportService;
	@Resource
	private BonusLedgerEmpImportService bonusLedgerEmpImportService;
	@Resource
	private Locker lock;

	@PostMapping("/create")
	@ApiOperation("奖金账套新增")
	public Result<String> create(@RequestBody BonusLedgerDto schemeDto) {
		return Result.ok(bonusLedgerService.create(schemeDto));
	}

	@PostMapping("/page")
	@ApiOperation("奖金账套列表")
	public Result<PageResult<BonusLedgerVo>> page(@RequestBody BonusLedgerPageDto dto) {
		PageResult<BonusLedgerVo> result = bonusLedgerService.page(dto);
		for (BonusLedgerVo item : result.getItems()) {
			item.setName(LangUtil.getCurrentLangVal(item.getI18nName()));
		}
		return Result.ok(result);
	}

	@GetMapping("/selectList")
	@ApiOperation("奖金账套下拉列表")
	public Result<List<BonusLedgerVo>> list() {
		List<BonusLedgerVo> list = bonusLedgerService.list();
		for (BonusLedgerVo item : list) {
			item.setName(LangUtil.getCurrentLangVal(item.getI18nName()));
		}
		return Result.ok(list);
	}

	@PostMapping("/update")
	@ApiOperation("奖金账套编辑")
	public Result update(@RequestBody BonusLedgerDto schemeDto) {
		bonusLedgerService.update(schemeDto);
		return Result.ok(true);
	}

	@DeleteMapping("/delete")
	@ApiOperation("奖金账套删除")
	public Result delete(String bid) {
		bonusLedgerService.delete(bid);
		return Result.ok(true);
	}

	@PutMapping("/close")
	@ApiOperation("奖金账套关闭")
	public Result close(@RequestBody BonusLedgerDto schemeDto) {
		bonusLedgerService.close(schemeDto.getBid());
		return Result.ok(true);
	}

	@PostMapping("emp/page")
	@ApiOperation("算薪员工列表")
	public Result<PageResult<BonusLedgerEmpVo>> loadEmp(@RequestBody BonusLedgerEmpPageDto dto) {
		return Result.ok(bonusLedgerService.loadLedgerEmpPage(dto));
	}

	@PostMapping("/emp/sync")
	@ApiOperation("算薪员工同步")
	public Result syncEmp(@RequestBody BonusAccountEmpDto bonusAccountSyncEmpDto) {
		bonusLedgerService.syncEmp(bonusAccountSyncEmpDto.getBid());
		return Result.ok(true);
	}

	@PostMapping("/emp")
	@ApiOperation("算薪员工添加")
	public Result addEmp(@RequestBody BonusAccountEmpDto bonusAccountSyncEmpDto) {
		bonusLedgerService.addEmp(bonusAccountSyncEmpDto);
		return Result.ok(true);
	}

	@ApiOperation(value = "算薪员工导入")
	@PostMapping("emp/import")
	public Result<ImportExcelVo> importData(ImportExcelDto dto) throws IOException {
		String tenantId = SecurityUserUtil.getThreadLocalSecurityUserInfo().getTenantId();
		ImportExcelVo vo = bonusLedgerEmpImportService.importDataWithExcel(dto);
		Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
		bonusLedgerEmpImportService.operateDataFromInputStream(dto.getFile()
				.getInputStream(), vo.getProcessUUid(), userId, tenantId,dto.getExcelCode());
		return Result.ok(vo);
	}

	@ApiOperation(value = "算薪员工导出")
	@PostMapping("emp/export")
	public void empExport(@RequestBody BonusLedgerEmpPageDto dto, HttpServletResponse response) {
		bonusLedgerEmpImportService.export(dto.getBid(), response);
	}


	@ApiOperation(value = "根据导入id查询进度信息")
	@GetMapping("emp/getImportPercentage")
	public Result<ImportExcelProcessVo> getImportPercentage(@RequestParam("processId") String processId, @RequestParam("excelCode") String excelCode) {
		return Result.ok(bonusLedgerEmpImportService.getImportDataPercentage(processId));
	}

	@ApiOperation(value = "根据导入id下载错误数据", produces="application/octet-stream")
	@GetMapping("emp/downloadErrorImportInfo")
	public void downloadErrorImportInfo(HttpServletResponse response, @RequestParam(value = "processId") String processId, @RequestParam("excelCode") String excelCode) {
		bonusLedgerEmpImportService.downloadErrorImportData(response, processId);
	}


	@PostMapping("/emp/delete")
	@ApiOperation("算薪员工删除")
	public Result removeEmp(@RequestBody BonusLedgerEmpDeleteDto dto) {
		bonusLedgerService.removeEmp(dto.getBid(), dto.getEmpIds());
		return Result.ok(true);
	}

	@GetMapping("compose")
	@ApiOperation("奖金计算--编排列表")
	public Result<List<BonusLedgerComposeVo>> loadComposeList(@RequestParam("bid") String bid) {
		return Result.ok(bonusLedgerService.loadComposeList(bid));
	}

	@PostMapping("compose/exec")
	@ApiOperation("奖金计算--编排执行")
	public Result exec(@RequestBody BonusLedgerExecDto execDto) {
		Lock lock = this.lock.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId());
		try {
			lock.lock();
			bonusLedgerService.exec(execDto);
		}
		finally {
			lock.unlock();
		}
		return Result.ok();
	}

	@PostMapping("compose/exec/all")
	@ApiOperation("奖金计算--全部执行")
	public Result execAll(@RequestBody BonusLedgerExecDto execDto) {
		Lock lock = this.lock.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId());
		try {
			lock.lock();
			bonusLedgerService.execAll(execDto);
		}
		finally {
			lock.unlock();
		}
		return Result.ok();
	}

	@GetMapping("compose/progress")
	@ApiOperation("奖金计算--进度")
	public Result<ArrangementProgressDto> composeProgress(@RequestParam  String ledgerId) {
		//单条执行时，找到所有waitingTask，全量执行，找到所有task
		// 已完成的task，返回冗余的total。进行中的task，根据exexId找到已执行的条数。未执行的条数，根据arrangementId找到总条数
		return Result.ok(bonusLedgerService.composeProgress(ledgerId));
	}

	@GetMapping("compose/result")
	@ApiOperation("奖金计算--执行结构")
	public Result<BonusComposeResult> composeResult(@RequestParam  String ledgerId) {
		return Result.ok(bonusLedgerService.composeResult(ledgerId));
	}

	@PostMapping("report")
	@ApiOperation("计算结果")
	public Result<BonusReportPageVo> loadReport(@RequestBody BonusReportDto dto) {
		return Result.ok(bonusReportService.loadResult(dto));
	}

	@PostMapping("report/export")
	@ApiOperation("计算结果导出")
	public void export(@RequestBody BonusReportDto dto, HttpServletResponse response) {
		bonusReportService.exportResult(dto, response);
	}

	@GetMapping("emp/list")
	@ApiOperation("获取算薪员工列表")
	public Result<List<BonusLedgerEmpSimpleVo>> getEmpList(@RequestParam("bid") String bid) {
		List<BonusLedgerEmpSimpleVo> empList = bonusLedgerService.getLedgerEmpList(bid);
		return Result.ok(empList);
	}

	// @PostMapping("/calc")
	// @ApiOperation("算薪")
	// public Result<String> calc(@RequestBody BonusCalcDto dto) {
	// 	return Result.ok(bonusLedgerService.calc(dto.getBid(), dto.getEmpIds()));
	// }
	//
	// @GetMapping("/calc/rate")
	// @ApiOperation("奖金账套计算进度")
	// public Result<List<Integer>> rateOfCalc(@RequestParam String processId) {
	// 	return Result.ok(bonusLedgerService.rateOfCalc(processId));
	// }
}

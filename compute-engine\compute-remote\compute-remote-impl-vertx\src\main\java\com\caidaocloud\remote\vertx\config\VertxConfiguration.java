package com.caidaocloud.remote.vertx.config;

import com.caidaocloud.compute.remote.framework.core.InitializerConfig;
import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.compute.remote.framework.core.RequestHandlerGenerator;
import com.caidaocloud.remote.vertx.core.VertxHandlerGenerator;
import com.caidaocloud.remote.vertx.core.VertxInitializer;
import org.apache.commons.lang3.StringUtils;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Configuration
@ConditionalOnProperty(value = "caidaocloud.remote.communication.type",havingValue = "vertx")
public class VertxConfiguration {

	@Bean
	public RemoteInitializer remoteInitializer(InitializerConfig initializerConfig){
		return new VertxInitializer(initializerConfig);
	}

	@Bean
	public RequestHandlerGenerator requestHandlerGenerator(RemoteInitializer remoteInitializer){
		return new VertxHandlerGenerator(remoteInitializer);
	}

}

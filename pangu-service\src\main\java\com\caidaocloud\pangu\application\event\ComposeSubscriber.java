package com.caidaocloud.pangu.application.event;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.pangu.application.dto.compose.ArrangementProgressDto;
import com.caidaocloud.pangu.application.service.ArrangementService;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerStep;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.Argument;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class ComposeSubscriber {
	@Autowired
	private ArrangementService arrangementService;

	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "bonus.compose.queue", durable = "true"),
					exchange = @Exchange(value = "exchange.compose.fac.direct.exchange"),
					key = {"bonus.compose.queue"}
			)
	)
	@RabbitHandler
	public void process( String msg) {
		ComposeEventDTO event = FastjsonUtil.toObject(msg, ComposeEventDTO.class);
		log.info("Received message: {}", msg);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(event.getTenantId());
			SecurityUserUtil.setSecurityUserInfo(userInfo);
			SpringUtil.getBean(ComposeSubscriber.class).process(event);
		} finally {
		SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	@PaasTransactional
	public void process(ComposeEventDTO event) {
		BonusLedgerStep currentStep = BonusLedgerStep.loadByExecId(event.getExecId());
		if (currentStep == null) {
			log.warn("Current step not found for execId: {}", event.getExecId());
			return;
		}
		SecurityUserUtil.getSecurityUserInfo().setUserId(Long.valueOf(currentStep.getUpdateBy()));
		// 获取ledger
		BonusLedger ledger = BonusLedger.load(currentStep.getLedgerId());
		
		// 检查ledger状态
		if (ledger.isClosed()) {
			log.warn("Ledger is closed, composeId: {}", currentStep.getComposeId());
			return;
		}
		
		// 获取当前step
		if (currentStep.getStatus() == null ||
				!String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue())
						.equals(currentStep.getStatus().getValue())) {
			log.warn("Step is not in progress, composeId: {}, stepId: {}",
					currentStep.getComposeId(), currentStep.getBid());
			return;
		}

		ArrangementProgressDto progressDto = arrangementService.getArrangementProgress(currentStep.getExecId(), currentStep.getComposeId());
		// 更新当前步骤为成功状态
		EnumSimple executedStatus = new EnumSimple();
		executedStatus.setValue(String.valueOf(event.getStatus() == ComposeEventDTO.Status.SUCCESS ? BonusLedgerStepStatus.EXECUTED.getValue() : BonusLedgerStepStatus.ERROR.getValue()));
		currentStep.setStatus(executedStatus);
		currentStep.setTotal(progressDto.getTotalNodes());
		currentStep.setSucceed(progressDto.getCompletedNodes());
		currentStep.update();

		if (event.getStatus() != ComposeEventDTO.Status.SUCCESS) {
			return;
		}
		List<BonusLedgerStep> steps = ledger.loadStep();
		// 获取并执行下一步
		Optional<BonusLedgerStep> nextStep = steps.stream()
				.filter(s -> s.getBid().equals(currentStep.getNextStepId()))
				.findFirst();
		if (!nextStep.isPresent()) {
			log.info("next step is empty,event={},ledger={}", event, ledger);
			return;
		}
		if (ExecutionType.ALL.getCode()
				.equals(ledger.getExecutionType().getValue()) || ledger.getWaitingTask()
				.contains(nextStep.get().getBid())) {
			ledger.exec(nextStep.get());
		}
	}
}
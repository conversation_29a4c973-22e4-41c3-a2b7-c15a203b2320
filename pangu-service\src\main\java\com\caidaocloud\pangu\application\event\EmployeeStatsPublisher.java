package com.caidaocloud.pangu.application.event;

import javax.annotation.Resource;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.util.FastjsonUtil;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmployeeStatsPublisher {
    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;

    public void publish(EmployeeStatsMessageDTO dto) {
        RabbitBaseMessage message = new RabbitBaseMessage();
        message.setExchange(BonusConstant.BONUS_DIRECT_EXCHANGE);
        message.setRoutingKey(BonusConstant.BONUS_EMP_STATS_ROUTINGKEY);
        message.setBody(FastjsonUtil.toJson(dto));
        producer.publish(message);
    }
}
package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorDataFilter;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.util.Map;

public class Or extends AbstractFunction {
    @Override
    public String getName() {
        return "OR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val p1 = aviatorObject1.getValue(env);
        val p2 = aviatorObject2.getValue(env);
        if(p1 instanceof DataFilter){
            val result = ((DataFilter)p1).or((DataFilter)p2);
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(p1).append(",").append(p2).append(")=").append(FastjsonUtil.toJson(result)).toString());
            }
            return new AviatorDataFilter(result);
        }else{
            val result = Boolean.TRUE == p1 || Boolean.TRUE == p2;
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(p1).append(",").append(p2).append(")=").append(result).toString());
            }
            return FunctionUtils.wrapReturn(result);
        }
    }
}

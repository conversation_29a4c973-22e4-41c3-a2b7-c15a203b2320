package com.caidaocloud.compute.remote.framework.actor;

import java.io.Serializable;

import com.caidaocloud.compute.remote.framework.serialize.RemoteSerializable;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Accessors(chain = true)
@Getter
@Setter
@NoArgsConstructor
public class ActorMessageWrapper implements RemoteSerializable {
	private String methodName;
	private Object[] args;
	private SecurityUserInfo userInfo;

	public ActorMessageWrapper(String methodIdentifier) {
		this.methodName = methodIdentifier;
	}

}

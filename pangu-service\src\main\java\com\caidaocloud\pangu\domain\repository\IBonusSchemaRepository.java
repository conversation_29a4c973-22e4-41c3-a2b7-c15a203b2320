package com.caidaocloud.pangu.domain.repository;

import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.domain.entity.ApproveConfigDo;
import com.caidaocloud.pangu.domain.entity.BonusSchema;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusSchemaRepository  {
	ApproveConfigDo loadApproveConfig(String bid);

	PageResult<BonusSchema> selectPage(BasePage basePage);

	ApproveConfigDo loadApproveConfigBySchemaId(String bid);

	int updateById(BonusSchema bonusSchema);

	BonusSchema insert(BonusSchema bonusSchema);

	BonusSchema selectById(String bid, String bonusSchemaIdentifier);

	List<BonusSchema> selectList(String bonusSchemaIdentifier, String tenantId);

	int delete(BonusSchema bonusSchema);
}

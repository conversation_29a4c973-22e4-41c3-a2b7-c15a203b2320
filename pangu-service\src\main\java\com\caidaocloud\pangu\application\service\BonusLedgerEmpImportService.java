package com.caidaocloud.pangu.application.service;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import cn.afterturn.easypoi.handler.inter.IExcelDataHandler;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.application.dto.BonusAccountEmpDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpImportDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.infrastructure.util.ExcelUtils;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/2/17
 */
@Service
@Slf4j
public class BonusLedgerEmpImportService {
	@Resource
	private CacheService cacheService;
	@Resource
	private BonusLedgerService bonusLedgerService;

	private static String ERROR_CACHE_KEY = "%s_ERROR";
	private static String PERCENTAGE_CACHE_KEY = "%s_PERCENTAGE";

	public ImportExcelVo importDataWithExcel(ImportExcelDto dto) {
		String processId = UUID.randomUUID().toString().replaceAll("-", "");
		ImportExcelVo vo = new ImportExcelVo();
		vo.setProcessUUid(processId);
		return vo;
	}

	public ImportExcelProcessVo getImportDataPercentage(String processId) {
		ImportExcelProcessVo vo = FastjsonUtil.toObject(cacheService.getValue(getPercentageCacheKey(processId)), ImportExcelProcessVo.class);
		return vo;
	}

	private String getPercentageCacheKey(String processId){
		return String.format(PERCENTAGE_CACHE_KEY, processId);
	}

	private String getErrorCacheKey(String processId){
		return String.format(ERROR_CACHE_KEY, processId);
	}

	public void downloadErrorImportData(HttpServletResponse response, String processId) {
		List<BonusLedgerEmpImportDto> list = FastjsonUtil.toList(cacheService.getValue(getErrorCacheKey(processId)), BonusLedgerEmpImportDto.class);
		ExcelUtils.downloadDataMapExcel(BonusLedgerEmpImportDto.class,list, "导入失败员工信息", response);
	}

	@Async("taskExecutor")
	public void operateDataFromInputStream(InputStream inputStream, String processId, Long userId, String tenantId, String code) {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId(tenantId);
		userInfo.setUserId(userId);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		int total = 0;
		int completed = 0;
		int successCount = 0;
		int failCount = 0;
		List<BonusLedgerEmpImportDto> errorProList = new ArrayList<>();
		List<BonusLedgerEmpImportDto> passDataList = new ArrayList<>();
		try {
			putImportExcelProcessVo(processId, total, completed, successCount, failCount);
			List<BonusLedgerEmpImportDto> list = getImportDoFromExcel(inputStream);
			if(null == list || list.size()==0){
				putImportExcelProcessVo(processId, total, completed, successCount, failCount);
			}

			List<Map<String, String>> workNoLit = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
					.filterProperties(DataFilter.ne("deleted", Boolean.TRUE.toString())
							.andIn("workno", Sequences.sequence(list).map(BonusLedgerEmpImportDto::getWorkno)
									.toList()), Lists.list("emp_id", "workno","name"), System.currentTimeMillis()).getItems();
			Map<String, Map<String,String>> workNoMap = workNoLit.stream()
					.collect(Collectors.toMap(m -> m.get("workno"), m->m));

			total = list.size();
			int i = 0;
			for (BonusLedgerEmpImportDto data: list) {
				i++;
				completed++;
//                检查空字段
				if (checkEmptyProp(data)) {
					errorProList.add(data);
					failCount++;
					continue;
				}
//                补充需要的字段，如id，枚举类型的数据，如果成功了放到通过的list，失败了放到失败的列表
				if (!installProp(data, workNoMap)) {
					passDataList.add(data);
					successCount++;
				}
				else {
					errorProList.add(data);
					failCount++;
					continue;
				}
				if(passDataList.size()>0 && passDataList.size()%500 == 0){
					errorProList.addAll(batchInsertUpdateData(passDataList,code));
					passDataList.clear();
				}
				if(i>0 && i%50==0){
//                刷新缓存当前解析数据信息
					putImportExcelProcessVo(processId, total, completed, completed - errorProList.size(), errorProList.size());
				}
			}
		}catch (Exception e){
			log.error("导入数据异常，-->error{}", e.getMessage());
			e.printStackTrace();
		}finally {
//          结束后，刷新缓存当前解析数据信息,并把错误信息放到
			errorProList.addAll(batchInsertUpdateData(passDataList,code));
			cacheErrorInfo(processId, errorProList);
			completed = total;
			failCount = errorProList.size();
			putImportExcelProcessVo(processId, total, completed, completed - failCount, failCount);

			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	private Collection<? extends BonusLedgerEmpImportDto> batchInsertUpdateData(List<BonusLedgerEmpImportDto> passDataList, String ledgerId) {
		List<String> empIds = Sequences.sequence(passDataList).map(BonusLedgerEmpImportDto::getEmpId).toList();
		BonusAccountEmpDto dto = new BonusAccountEmpDto();
		dto.setBid(ledgerId);
		dto.setEmpId(empIds);
		bonusLedgerService.addEmp(dto);
		return new ArrayList<>();
	}

	private void cacheErrorInfo(String processId, List<?> errorList){
		cacheService.cacheValue(getErrorCacheKey(processId), FastjsonUtil.toJson(errorList), 1800);
	}

	private boolean installProp(BonusLedgerEmpImportDto data, Map<String, Map<String,String>> workNoMap) {
		if (!workNoMap.containsKey(data.getWorkno()) ||
				!ObjectUtil.nullSafeEquals(workNoMap.get(data.getWorkno()).get("name"), data.getName())) {
			data.addEmpPropTip("员工不存在");
		}
		else {
			data.setEmpId(workNoMap.get(data.getWorkno()).get("empId"));
		}
		return data.isCheckFailFlag();
	}

	private boolean checkEmptyProp(BonusLedgerEmpImportDto data) {
		if(data.getWorkno()==null){
			data.addEmpPropTip("账号不能为空");
		}
		return data.isCheckFailFlag();
	}

	private List<BonusLedgerEmpImportDto> getImportDoFromExcel(InputStream inputStream) {
		if (null == inputStream) {
			return new ArrayList<BonusLedgerEmpImportDto>();
		}
		ImportParams params = new ImportParams();
		params.setTitleRows(2);
		params.setHeadRows(1);
		List<BonusLedgerEmpImportDto> list = null;
		try {
			list = ExcelImportUtil.importExcel(inputStream, BonusLedgerEmpImportDto.class, params);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	private void putImportExcelProcessVo(String processId, int total, int completed, int successCount, int failCount){
		int notDone = total - completed;
		if(total == 0){
			total = 1;
		}
		ImportExcelProcessVo vo = new ImportExcelProcessVo(processId, total, completed, notDone, successCount, failCount);
		if(total == 0){
			vo.setTotal(0);
		}
		boolean flag = cacheService.cacheValue(getPercentageCacheKey(processId), FastjsonUtil.toJson(vo), 300);
	}

	public void export(String ledgerId, HttpServletResponse response) {
		BonusLedger ledger = BonusLedger.load(ledgerId);
		BonusLedgerEmpPageDto dto = new BonusLedgerEmpPageDto();
		dto.setPageSize(1000);
		dto.setBid(ledgerId);
		ExportParams exportParams = new ExportParams();
		exportParams.setDataHandler(new ExcelDataHandlerDefaultImpl() {
			@Override
			public String[] getNeedHandlerFields() {
				return new String[] {"员工状态", "员工类型", "入职日期", "离职日期"};
			}

			@Override
			public Object exportHandler(Object obj, String name, Object value) {
				if (value == null) {
					return null;
				}
				else if (value instanceof EnumSimple) {
					return ((EnumSimple) value).getText();
				}
				else if (value instanceof DictSimple) {
					return ((DictSimple) value).getText();
				}
				else if (name != null && (name.equals("入职日期") || name.equals("离职日期"))) {
					return LocalDateTime.ofEpochSecond(((long) value) / 1000,0, ZoneOffset.UTC)
							.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
				}
				return value;
			}
		});
		Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, BonusLedgerEmpVo.class, new IExcelExportServer() {
			@Override
			public List<Object> selectListForExcelExport(Object queryParams, int page) {
				dto.setPageNo(page);
				List list = bonusLedgerService.loadLedgerEmpPage(dto).getItems();
				return list;
			}
		}, dto);
		try {
			// 获取当前日期并格式化
			String ledgerName = ledger.getName();
			String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			String fileName = String.format("%s-%s", ledgerName, date);
			
			ExcelUtils.downLoadExcel(fileName, response, workbook);
		}
		catch (IOException e) {
			throw new ServerException("员工导出失败", e);
		}
	}
}

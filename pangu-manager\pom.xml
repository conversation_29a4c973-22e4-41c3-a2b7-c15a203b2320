<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caidao-pangu-service</artifactId>
        <groupId>com.caidaocloud</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pangu-manager</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <netty.version>4.1.115.Final</netty.version>
        <remote.version>0.0.1-SNAPSHOT</remote.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.1.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.totallylazy</groupId>
            <artifactId>totallylazy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>compute-remote-impl-akka</artifactId>
            <version>${remote.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>compute-remote-impl-vertx</artifactId>
            <version>${remote.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>paas-sdk</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>galaxy-service-distributedlock</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>pangu-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
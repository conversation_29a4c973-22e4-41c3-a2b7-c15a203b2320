2025-03-13 13:44:18.173 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:44:18.235 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:44:18.655 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:44:18.698 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6bfebca8, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f81a3ac, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@4952f180, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@103cd215, org.springframework.test.context.support.DirtiesContextTestExecutionListener@23f76be3, org.springframework.test.context.transaction.TransactionalTestExecutionListener@8cc3c8aa, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@e2674195, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@a73511db, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@63d5b3de, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@f6ed734c, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@b9501569, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@a84d526d]
2025-03-13 13:44:19.806 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:44:19.811 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:44:21.078 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$556730fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:21.921 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:44:22.486 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:44:22.519 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:44:26.572 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:44:27.142 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:44:27.147 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:44:27.228 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58ms. Found 0 repository interfaces.
2025-03-13 13:44:27.350 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:44:27.377 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:44:27.947 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:44:28.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$a113482c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.434 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.446 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$4e66ec4a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.756 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.915 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.923 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$e751268e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:28.969 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$ffb8ec2e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:29.143 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$6e8e27ab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:29.388 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$74913be8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:29.412 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:29.782 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:44:29.787 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:44:30.319 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.357 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.417 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.422 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.428 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.438 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.440 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:30.498 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$556730fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:44:31.058 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:44:31.058 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:44:31.086 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@ee8e9cbb
2025-03-13 13:44:31.757 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:44:31.867 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#8a4e2367:0/SimpleConnection@971ea307 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 57341]
2025-03-13 13:44:34.684 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:44:34.911 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:44:35.106 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:44:37.618 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:44:38.465 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:44:38.477 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:44:38.560 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:44:38.789 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:44:38.790 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:44:39.710 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:44:41.876 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:44:41.914 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:44:41.917 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:44:41.944 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:44:42.053 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:44:42.833 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:44:42.886 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:44:42.926 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:44:42.929 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:44:42.971 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:44:42.980 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:44:43.011 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:44:43.019 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:44:43.043 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:44:43.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:44:43.332 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:44:43.387 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 24.575 seconds (JVM running for 28.043)
2025-03-13 13:44:43.795 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:44:43.795 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:44:43.796 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:44:44.692 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:44:44.793 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:44:44.803 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:44:44.927 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:44:44.929 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7070cdd7
2025-03-13 13:44:45.330 [main] INFO  org.reflections.Reflections - Reflections took 84 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:44:45.812 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:49:30.838 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:49:30.837 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:49:30.804 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:49:30.848 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-03-13 13:49:30.993 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:49:31.028 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:49:31.149 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 13:49:31.155 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 13:49:31.157 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 13:49:31.158 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 13:49:31.259 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=2143667968292864
com.caidaocloud.excption.ServerException: 仅审批通过后可以关闭账套
	at com.caidaocloud.pangu.domain.entity.BonusLedger.close(BonusLedger.java:193)
	at com.caidaocloud.pangu.application.service.BonusLedgerService.close(BonusLedgerService.java:110)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.BonusLedgerControllerTest.close(BonusLedgerControllerTest.java:283)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2025-03-13 13:49:31.532 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:49:31.534 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:49:31.535 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:49:31.535 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:49:31.582 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@42e324fa: tags=[[amq.ctag-gjH_mSBk_wW4HDz4-fn5cg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@595f59fb Shared Rabbit Connection: SimpleConnection@971ea307 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 57341], acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:49:31.582 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@777c7b3b: tags=[[amq.ctag-KT-cXMWp6jVUNz3g-mfmpA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@595f59fb Shared Rabbit Connection: SimpleConnection@971ea307 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 57341], acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:49:31.595 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:49:31.677 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#8a4e2367:1/SimpleConnection@dd10894b [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 57735]
2025-03-13 13:49:31.699 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:49:31.730 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:49:31.730 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:49:31.730 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:49:31.731 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:49:31.734 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:49:31.734 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:49:31.735 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:49:31.847 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:49:31.848 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:49:31.888 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:49:32.886 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5cefb720: tags=[[amq.ctag-4uRbjplnkg7HAKzYoOhKCA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,2), conn: Proxy@595f59fb Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:49:32.886 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@f6d618b0: tags=[[amq.ctag-_acK3ol5Vst-bfZR9ESyIA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@595f59fb Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:49:33.265 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:49:33.341 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:49:33.362 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 13:49:48.495 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:49:48.505 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:49:48.840 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:49:48.881 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@a66d2521, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@81a943d, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@632acd0, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@2543531a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@19add75, org.springframework.test.context.transaction.TransactionalTestExecutionListener@c68f05d0, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@706884ed, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@d569260c, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@38c14a03, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@da0823b2, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@a099ac1f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@c3412c59]
2025-03-13 13:49:49.790 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:49:49.794 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:49:50.467 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9d668ee3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:51.047 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:49:51.418 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:49:51.430 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:49:53.080 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:49:53.484 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:49:53.488 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:49:53.555 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52ms. Found 0 repository interfaces.
2025-03-13 13:49:53.635 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:49:53.654 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:49:54.030 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:49:54.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$e912a612] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.345 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$96664a30] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.558 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.689 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.696 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.712 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.713 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$2f508474] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$47b84a14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:54.845 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$b68d8591] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.026 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$bc9099ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.298 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:49:55.301 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:49:55.755 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.783 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.812 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.836 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.840 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.841 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.846 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.847 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:55.891 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9d668ee3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:49:56.364 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:49:56.364 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:49:56.384 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@5ca8d68a
2025-03-13 13:49:56.935 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:49:57.031 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#6cb1e31f:0/SimpleConnection@84e09311 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 57801]
2025-03-13 13:49:59.014 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:49:59.192 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:49:59.326 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:50:01.059 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:50:01.728 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:50:01.740 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:50:01.805 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:50:01.980 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:50:01.981 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:50:02.760 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:50:04.529 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:50:04.562 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:50:04.572 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:50:04.598 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:50:04.663 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:50:05.023 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:50:05.054 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:50:05.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:50:05.090 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:50:05.112 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:50:05.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:50:05.132 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:50:05.138 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:50:05.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:50:05.232 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:50:05.363 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:50:05.412 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 16.437 seconds (JVM running for 17.955)
2025-03-13 13:50:05.520 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:50:05.520 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:50:05.520 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:50:05.835 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:05.908 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:50:05.915 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:50:05.984 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:05.986 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@aede7ee5
2025-03-13 13:50:06.306 [main] INFO  org.reflections.Reflections - Reflections took 69 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:50:06.925 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:06.969 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=2143670598187008
com.caidaocloud.excption.ServerException: Bonus ledger name 'new ledger' already exists
	at com.caidaocloud.pangu.domain.entity.BonusLedger.create(BonusLedger.java:136)
	at com.caidaocloud.pangu.application.service.BonusLedgerService.create(BonusLedgerService.java:75)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.interfaces.BonusLedgerController.create(BonusLedgerController.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:215)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:142)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:998)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:901)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:875)
	at org.springframework.test.web.servlet.TestDispatcherServlet.service(TestDispatcherServlet.java:71)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.springframework.mock.web.MockFilterChain$ServletFilterProxy.doFilter(MockFilterChain.java:166)
	at org.springframework.mock.web.MockFilterChain.doFilter(MockFilterChain.java:133)
	at org.springframework.test.web.servlet.MockMvc.perform(MockMvc.java:182)
	at com.caidaocloud.pangu.BonusLedgerControllerTest.testCreateBonusLedger(BonusLedgerControllerTest.java:91)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2025-03-13 13:50:06.997 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:50:06.997 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:50:06.997 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:50:06.997 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:50:06.998 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:50:07.013 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:50:07.342 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:50:07.348 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:50:07.368 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:50:07.389 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:50:07.395 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:50:07.396 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:50:07.396 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:50:07.396 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:50:07.397 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:50:07.397 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:50:07.397 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:50:07.425 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:50:07.425 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:50:07.444 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:50:08.419 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@caa9364a: tags=[[amq.ctag-f0p7PcxMPXh1cNBORLeTzw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@35ab6e4c Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:50:08.434 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6552ed29: tags=[[amq.ctag-p1PiLNlT3zYZ2wvTmrYSOw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@35ab6e4c Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:50:08.580 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:50:08.581 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:50:08.581 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 13:50:28.784 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:50:28.792 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:50:29.095 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:50:29.123 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@d0f342ed, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@22a2607c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@ed557171, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@90608ec8, org.springframework.test.context.support.DirtiesContextTestExecutionListener@adfe7cb3, org.springframework.test.context.transaction.TransactionalTestExecutionListener@c56b6c49, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@20fe5537, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@5de7f689, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@75a37a22, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@d36d38be, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@a461d234, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@c861a59b]
2025-03-13 13:50:29.998 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:50:30.004 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:50:30.736 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3f115993] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:31.348 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:50:31.726 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:50:31.737 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:50:33.333 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:50:33.672 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:50:33.676 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:50:33.739 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48ms. Found 0 repository interfaces.
2025-03-13 13:50:33.820 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:50:33.837 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:50:34.413 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:50:34.793 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$8abd70c2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:34.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:34.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$381114e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.050 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.208 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.215 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.239 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.239 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$d0fb4f24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$e96314c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$58385041] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.600 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$5e3b647e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:35.915 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:50:35.922 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:50:36.551 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.581 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.608 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.630 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.634 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.635 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.639 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.640 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:36.686 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3f115993] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:50:37.162 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:50:37.162 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:50:37.177 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@3b34c7d4
2025-03-13 13:50:37.735 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:50:37.834 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#85d77a99:0/SimpleConnection@3d681b42 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 58022]
2025-03-13 13:50:39.989 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:50:40.152 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:50:40.298 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:50:42.295 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:50:43.038 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:50:43.052 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:50:43.116 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:50:43.293 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:50:43.293 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:50:44.048 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:50:45.865 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:50:45.897 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:50:45.900 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:50:45.931 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:50:46.004 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:50:46.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:50:46.430 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:50:46.462 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:50:46.465 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:50:46.488 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:50:46.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:50:46.508 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:50:46.515 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:50:46.540 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:50:46.624 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:50:46.765 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:50:46.825 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 17.626 seconds (JVM running for 19.091)
2025-03-13 13:50:46.929 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:50:46.929 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:50:46.929 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:50:47.275 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:47.348 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:50:47.355 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:50:47.423 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:47.425 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@80596200
2025-03-13 13:50:47.743 [main] INFO  org.reflections.Reflections - Reflections took 75 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:50:48.360 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:50:48.860 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:50:48.860 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:50:48.860 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:50:48.860 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:50:48.862 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:50:48.874 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:50:49.727 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:50:49.732 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:50:49.770 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:50:49.792 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:50:49.802 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:50:49.802 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:50:49.802 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:50:49.802 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:50:49.803 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:50:49.803 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:50:49.804 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:50:49.834 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:50:49.834 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:50:49.851 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:50:49.854 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@da48609c: tags=[[amq.ctag-HKXBq0EN3mc0c7O0P9G8zA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@2ee52ba5 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:50:50.839 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@4274d636: tags=[[amq.ctag-mbJ-Re-I-ds8veALXgxCqQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@2ee52ba5 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:50:50.972 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:50:50.972 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:50:50.973 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 13:51:51.195 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:51:51.201 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:51:51.368 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:51:51.393 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@d76eec07, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@5fbf0829, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@527eeeea, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@925518c5, org.springframework.test.context.support.DirtiesContextTestExecutionListener@632a3208, org.springframework.test.context.transaction.TransactionalTestExecutionListener@df121ca3, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@e6f14782, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@72207100, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@d78933ad, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@f4d513ae, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@d78dbbfb, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@877261d9]
2025-03-13 13:51:52.000 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:51:52.003 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:51:52.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3951225d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:52.960 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:51:53.261 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:51:53.270 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:51:54.548 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:51:54.855 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:51:54.858 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:51:54.917 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45ms. Found 0 repository interfaces.
2025-03-13 13:51:54.995 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:51:55.021 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:51:55.390 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:51:55.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$84fd398c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.642 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.649 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$3250ddaa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.821 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.956 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$cb3b17ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:55.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$e3a2dd8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.076 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$5278190b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.244 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$587b2d48] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.509 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:51:56.512 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:51:56.896 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.963 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.964 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:56.968 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:57.006 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3951225d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:51:57.400 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:51:57.400 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:51:57.412 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@68ac3817
2025-03-13 13:51:57.867 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:51:57.988 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#761cfc8:0/SimpleConnection@6a14202e [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 58323]
2025-03-13 13:51:59.926 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:52:00.145 [redisson-netty-5-13] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:52:00.427 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:52:02.189 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:52:02.874 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:52:02.882 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:52:02.941 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:52:03.097 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:52:03.097 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:52:03.806 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:52:05.868 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:52:05.908 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:52:05.909 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:52:05.949 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:52:06.044 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:52:06.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:52:06.640 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:52:06.677 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:52:06.680 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:52:06.703 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:52:06.708 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:52:06.727 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:52:06.733 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:52:06.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:52:06.845 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:52:07.013 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:52:07.159 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 15.675 seconds (JVM running for 16.674)
2025-03-13 13:52:07.286 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:52:07.286 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:52:07.287 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:52:07.580 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:52:07.657 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:52:07.666 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:52:07.792 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:52:07.795 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@1b68dde4
2025-03-13 13:52:08.111 [main] INFO  org.reflections.Reflections - Reflections took 66 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:52:08.597 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=2143671595857920
com.caidaocloud.excption.ServerException: 仅审批通过后可以关闭账套
	at com.caidaocloud.pangu.domain.entity.BonusLedger.close(BonusLedger.java:193)
	at com.caidaocloud.pangu.application.service.BonusLedgerService.close(BonusLedgerService.java:109)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.BonusLedgerControllerTest.close(BonusLedgerControllerTest.java:283)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2025-03-13 13:52:08.619 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:52:08.619 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:52:08.620 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:52:08.620 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:52:08.620 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:52:08.637 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:52:08.968 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:52:08.979 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:52:09.013 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:52:09.035 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:52:09.051 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:52:09.051 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:52:09.052 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:52:09.052 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:52:09.052 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:52:09.052 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:52:09.053 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:52:09.090 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:52:09.091 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:52:09.110 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:52:09.111 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@27863262: tags=[[amq.ctag-mg9Y_lLtM8PVPjhygtmWtQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@3cb9d557 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:52:09.173 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@222a44e8: tags=[[amq.ctag-udHudDGFo9D4zSSRou_1ug]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@3cb9d557 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:52:10.236 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:52:10.237 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:52:10.237 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 13:52:36.679 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:52:36.698 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:52:37.307 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:52:37.365 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@7592e9b, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6eb55503, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@955d18a2, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@c97ad980, org.springframework.test.context.support.DirtiesContextTestExecutionListener@16664e0b, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1b593755, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@98b93f2a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@bfe3b558, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@536eac61, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@c7f9a243, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@cd93b508, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1ad0c5e]
2025-03-13 13:52:39.558 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:52:39.563 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:52:41.172 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f4d4ac06] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:42.004 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:52:43.151 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:52:43.163 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:52:45.229 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:52:46.473 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:52:46.477 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:52:46.560 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 65ms. Found 0 repository interfaces.
2025-03-13 13:52:46.753 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:52:46.794 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:52:47.356 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:52:47.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$4080c335] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:47.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:47.840 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$edd46753] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.193 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.435 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.460 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.493 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.493 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$86bea197] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.543 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$9f266737] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:48.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$dfba2b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:49.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$13feb6f1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:49.072 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:49.516 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:52:49.522 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:52:50.076 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.108 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.137 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.156 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.168 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.169 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f4d4ac06] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:52:50.875 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:52:50.876 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:52:50.896 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@9fb795e3
2025-03-13 13:52:51.597 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:52:51.716 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#5d88d255:0/SimpleConnection@9900cd6c [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 58577]
2025-03-13 13:52:54.623 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:52:54.879 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:52:55.073 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:52:57.260 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:52:58.085 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:52:58.098 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:52:58.193 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:52:58.424 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:52:58.424 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:52:59.274 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:53:01.480 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:53:01.551 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:53:01.552 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:53:01.605 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:53:01.766 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:53:02.476 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:53:02.546 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:53:02.597 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:53:02.602 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:53:02.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:53:02.641 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:53:02.671 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:53:02.678 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:53:02.704 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:53:02.813 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:53:03.066 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:53:03.254 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 25.727 seconds (JVM running for 31.341)
2025-03-13 13:53:03.400 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:53:03.400 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:53:03.400 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:53:03.679 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:53:03.769 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:53:03.778 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:53:03.891 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:53:03.896 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@85d25be7
2025-03-13 13:53:04.271 [main] INFO  org.reflections.Reflections - Reflections took 80 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:53:04.789 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:53:11.743 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=2143672055683072
com.caidaocloud.excption.ServerException: 仅审批通过后可以关闭账套
	at com.caidaocloud.pangu.domain.entity.BonusLedger.close(BonusLedger.java:193)
	at com.caidaocloud.pangu.application.service.BonusLedgerService.close(BonusLedgerService.java:109)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.BonusLedgerControllerTest.close(BonusLedgerControllerTest.java:283)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2025-03-13 13:53:11.864 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:53:11.864 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:53:11.864 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:53:11.865 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:53:11.867 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:53:11.886 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:53:12.756 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:53:12.765 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:53:13.769 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:53:13.795 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:53:13.802 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:53:13.803 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:53:13.803 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:53:13.804 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:53:13.804 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:53:13.805 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:53:13.805 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:53:13.853 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:53:13.853 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:53:13.884 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:53:14.762 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@c756e246: tags=[[amq.ctag-WRcOVhK9gr_cEyzLyKy-gw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@c53c5ed2 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:53:14.762 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@561b8e6c: tags=[[amq.ctag-4dW5lrNLs3yvl9f2N6hF2g]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@c53c5ed2 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:53:15.017 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:53:15.018 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:53:15.018 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 13:53:45.842 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-13 13:53:45.849 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 13:53:46.089 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 13:53:46.114 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@3e6d797b, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@648f8513, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@faf0120, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@47041ef5, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6a0c7cf7, org.springframework.test.context.transaction.TransactionalTestExecutionListener@b0ae98d4, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@e11b3313, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@a4e75f64, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@3a08cae5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@1533aa6, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@56d5c2a2, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@a9080295]
2025-03-13 13:53:47.115 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 13:53:47.118 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 13:53:48.065 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e9d9d26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:48.666 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 13:53:49.048 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 13:53:49.057 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-13 13:53:50.666 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 13:53:51.092 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 13:53:51.096 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 13:53:51.161 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49ms. Found 0 repository interfaces.
2025-03-13 13:53:51.234 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 13:53:51.251 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 13:53:51.618 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 13:53:51.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$5a49b455] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:51.904 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:51.912 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$79d5873] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.115 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.251 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$a08792b7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.286 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$b8ef5857] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.415 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$27c493d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$2dc7a811] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:52.868 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 13:53:52.871 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 13:53:53.298 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.320 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.363 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.367 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.368 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.371 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.372 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:53.410 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e9d9d26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 13:53:54.052 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:53:54.052 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:53:54.070 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c0bfbcf
2025-03-13 13:53:54.778 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 13:53:54.898 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#a1b0cd8a:0/SimpleConnection@35d0750f [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 58836]
2025-03-13 13:53:57.551 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 13:53:57.711 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 13:53:57.833 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 13:53:59.495 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 13:54:00.156 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 13:54:00.169 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 13:54:00.226 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 13:54:00.379 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 13:54:00.379 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 13:54:01.184 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 13:54:03.224 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 13:54:03.251 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 13:54:03.252 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 13:54:03.278 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 13:54:03.344 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 13:54:03.729 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 13:54:03.760 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 13:54:03.791 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 13:54:03.793 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 13:54:03.815 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 13:54:03.822 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 13:54:03.839 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 13:54:03.844 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 13:54:03.863 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 13:54:03.939 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 13:54:04.067 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 13:54:04.148 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 17.966 seconds (JVM running for 19.358)
2025-03-13 13:54:04.253 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 13:54:04.253 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 13:54:04.253 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 13:54:04.456 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:54:04.533 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 13:54:04.540 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 13:54:04.645 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:54:04.647 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@cf072ee4
2025-03-13 13:54:04.998 [main] INFO  org.reflections.Reflections - Reflections took 72 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 13:54:05.549 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 13:54:06.149 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 13:54:06.149 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 13:54:06.149 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 13:54:06.150 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 13:54:06.151 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 13:54:06.161 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:54:07.036 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:54:07.043 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 13:54:07.084 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 13:54:07.103 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 13:54:07.113 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 13:54:07.113 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 13:54:07.114 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 13:54:07.114 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 13:54:07.114 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 13:54:07.114 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 13:54:07.115 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 13:54:07.154 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:54:07.155 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 13:54:07.178 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 13:54:07.179 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@6d600b5: tags=[[amq.ctag-Bn3oDqNZevEZeYlpAW_g9w]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@2069a0cd Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:54:08.142 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3f567de4: tags=[[amq.ctag-6woxsF9u3z99anw7alPBkA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@2069a0cd Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 13:54:08.304 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 13:54:08.304 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 13:54:08.304 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 14:11:31.992 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusStructControllerTest], using SpringBootContextLoader
2025-03-13 14:11:32.006 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusStructControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 14:11:32.356 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 14:11:32.389 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@f40ea61c, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@829eebcb, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@642c73a1, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@381b01ba, org.springframework.test.context.support.DirtiesContextTestExecutionListener@f28c876, org.springframework.test.context.transaction.TransactionalTestExecutionListener@771dd97f, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@4eb7c517, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@599c41cb, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@70bed2f0, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@1c832cf6, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@6083c3ae, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@f0b58e94]
2025-03-13 14:11:33.465 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 14:11:33.472 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 14:11:34.509 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e9a1fe2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:35.339 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 14:11:35.820 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 14:11:35.832 [main] INFO  c.c.pangu.BonusStructControllerTest - The following profiles are active: local
2025-03-13 14:11:38.217 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 14:11:38.809 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 14:11:38.817 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 14:11:38.896 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58ms. Found 0 repository interfaces.
2025-03-13 14:11:39.015 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 14:11:39.041 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 14:11:39.480 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 14:11:39.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$354e155a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:39.966 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.012 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$e2a1b978] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.342 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.515 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.532 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.532 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$7b8bf3bc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.558 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$93f3b95c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.703 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$2c8f4d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$8cc0916] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:40.944 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.282 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 14:11:41.286 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 14:11:41.820 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.898 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.903 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.911 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.912 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.919 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:41.971 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e9a1fe2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:11:42.485 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 14:11:42.485 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 14:11:42.506 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7f4dc547
2025-03-13 14:11:43.227 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 14:11:43.350 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#59bceb1f:0/SimpleConnection@8d5ef2a [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 60118]
2025-03-13 14:11:55.899 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 14:11:56.203 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 14:11:56.456 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 14:11:59.269 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 14:12:00.462 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 14:12:00.494 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 14:12:00.597 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 14:12:00.924 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 14:12:00.924 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 14:12:02.184 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 14:12:05.725 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 14:12:05.795 [Thread-44] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 14:12:05.805 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 14:12:05.858 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 14:12:06.013 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 14:12:06.625 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 14:12:06.683 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 14:12:06.753 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 14:12:06.759 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 14:12:06.817 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 14:12:06.831 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 14:12:06.891 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 14:12:06.903 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 14:12:06.944 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 14:12:07.074 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 14:12:07.259 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 14:12:07.334 [main] INFO  c.c.pangu.BonusStructControllerTest - Started BonusStructControllerTest in 34.818 seconds (JVM running for 37.182)
2025-03-13 14:12:07.493 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 14:12:07.493 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 14:12:07.494 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-13 14:12:07.786 [main] INFO  org.reflections.Reflections - Reflections took 135 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 14:12:08.170 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:12:08.272 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 14:12:08.282 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 14:12:08.476 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:12:08.480 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@b61fb4b2
2025-03-13 14:12:09.293 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:16:55.149 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 14:16:55.124 [Thread-48] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 14:16:55.153 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 14:16:55.210 [AMQP Connection ***************:5672] WARN  c.r.c.impl.ForgivingExceptionHandler - An unexpected connection driver error occured (Exception message: Connection reset)
2025-03-13 14:16:55.219 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 14:16:55.252 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 14:16:55.365 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 14:16:55.370 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 14:16:55.371 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 14:16:55.372 [AMQP Connection ***************:5672] ERROR o.s.a.r.c.CachingConnectionFactory - Channel shutdown: connection error
2025-03-13 14:16:55.418 [main] ERROR c.c.h.metadata.sdk.query.DataQuery - 请求异常，identifier:entity.bonus.BonusStructReportItem filter: null
2025-03-13 14:16:55.532 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 14:16:55.533 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 14:16:55.534 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 14:16:55.534 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 14:16:55.689 [main] ERROR c.c.h.m.s.t.aspect.TransactionAspect - Rolling back paas transaction by exception,tx id=2143681431803904
feign.FeignException: status 500 reading IMetadataFeign#one(String)
	at feign.FeignException.errorStatus(FeignException.java:78)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:93)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:149)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:78)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:103)
	at com.sun.proxy.$Proxy182.one(Unknown Source)
	at com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorServiceFeignImpl.load(MetadataOperatorServiceFeignImpl.java:25)
	at com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery.filter(DataQuery.java:231)
	at com.caidaocloud.pangu.infrastructure.repository.BonusStructReportItemRepositoryImpl.selectList(BonusStructReportItemRepositoryImpl.java:57)
	at com.caidaocloud.pangu.infrastructure.repository.BonusStructReportItemRepositoryImpl.saveList(BonusStructReportItemRepositoryImpl.java:29)
	at com.caidaocloud.pangu.infrastructure.repository.BonusStructReportItemRepositoryImpl$$FastClassBySpringCGLIB$$5051dc9c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.infrastructure.repository.BonusStructReportItemRepositoryImpl$$EnhancerBySpringCGLIB$$b7e92160.saveList(<generated>)
	at com.caidaocloud.pangu.domain.entity.BonusStruct.saveReportSetting(BonusStruct.java:175)
	at com.caidaocloud.pangu.application.service.BonusStructService.saveReportSetting(BonusStructService.java:175)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.caidaocloud.hrpaas.metadata.sdk.transaction.aspect.TransactionAspect.doTransaction(TransactionAspect.java:64)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:93)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.caidaocloud.pangu.BonusStructControllerTest.test(BonusStructControllerTest.java:419)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:74)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:251)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:33)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
2025-03-13 14:16:55.713 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 14:16:55.714 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 14:16:55.730 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@11e42b01
2025-03-13 14:16:55.832 [Thread-44] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 14:16:55.838 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 14:16:55.839 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 14:16:55.840 [Thread-46] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 14:16:55.841 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 14:16:55.843 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 14:16:55.843 [Thread-43] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 14:16:55.844 [Thread-46] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 14:16:55.930 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 14:16:55.931 [Thread-46] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 14:16:55.943 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@3c23eb65: tags=[[amq.ctag-LVjUL0R_LWDxV3W_sbkpvw]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@1966c8b0 Shared Rabbit Connection: SimpleConnection@8d5ef2a [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 60118], acknowledgeMode=MANUAL local queue size=0
2025-03-13 14:16:55.948 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 14:16:55.970 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@12d66af7: tags=[[amq.ctag-OBbaNCRei7MHMSb3JKHDiQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@1966c8b0 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 14:16:55.973 [SimpleAsyncTaskExecutor-2] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#59bceb1f:1/SimpleConnection@7002be83 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 60525]
2025-03-13 14:17:08.043 [Thread-46] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 14:17:09.220 [Thread-46] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 14:17:09.239 [Thread-46] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 14:17:09.248 [Thread-46] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-13 14:17:21.822 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusStructControllerTest], using SpringBootContextLoader
2025-03-13 14:17:21.835 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusStructControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-13 14:17:22.265 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-13 14:17:22.307 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6d5ead6f, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@51ec0f07, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@ceb09ee8, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4dc3c348, org.springframework.test.context.support.DirtiesContextTestExecutionListener@91cd713e, org.springframework.test.context.transaction.TransactionalTestExecutionListener@b78dcd41, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@111069ba, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@a3b9a53e, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@7b7bcadf, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@be18c2a9, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@d6eb54c2, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@a5da86b6]
2025-03-13 14:17:23.397 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-13 14:17:23.401 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-13 14:17:24.372 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$83997871] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:25.283 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-13 14:17:25.866 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-13 14:17:25.886 [main] INFO  c.c.pangu.BonusStructControllerTest - The following profiles are active: local
2025-03-13 14:17:28.643 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-13 14:17:29.212 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-13 14:17:29.217 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-13 14:17:29.309 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70ms. Found 0 repository interfaces.
2025-03-13 14:17:29.443 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-13 14:17:29.480 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-13 14:17:30.010 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-13 14:17:30.470 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$cf458fa0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:30.500 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:30.513 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$7c9933be] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:30.821 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:30.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:30.998 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.016 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.016 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$15836e02] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$2deb33a2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.193 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$9cc06f1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.443 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$a2c3835c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.477 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:31.871 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-13 14:17:31.875 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-13 14:17:32.371 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.404 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.438 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.457 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.466 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.476 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.478 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.484 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:32.541 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$83997871] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-13 14:17:33.228 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 14:17:33.229 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 14:17:33.262 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@69530d84
2025-03-13 14:17:34.060 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-13 14:17:34.175 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#4c9258af:0/SimpleConnection@9c74359b [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 60600]
2025-03-13 14:17:38.400 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-13 14:17:38.706 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-13 14:17:38.864 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-13 14:17:41.694 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-13 14:17:43.036 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-13 14:17:43.154 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-13 14:17:43.473 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-13 14:17:43.983 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-13 14:17:43.983 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-13 14:17:45.621 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-13 14:17:49.069 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-13 14:17:49.298 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-13 14:17:49.376 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-13 14:17:49.433 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-13 14:17:49.656 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-13 14:17:50.285 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-13 14:17:50.332 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-13 14:17:50.375 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-13 14:17:50.381 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-13 14:17:50.492 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-13 14:17:50.510 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-13 14:17:50.557 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-13 14:17:50.580 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-13 14:17:50.613 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-13 14:17:50.773 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-13 14:17:51.118 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-13 14:17:51.181 [main] INFO  c.c.pangu.BonusStructControllerTest - Started BonusStructControllerTest in 28.756 seconds (JVM running for 31.592)
2025-03-13 14:17:51.348 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-13 14:17:51.348 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-13 14:17:51.349 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 1 ms
2025-03-13 14:17:51.796 [main] INFO  org.reflections.Reflections - Reflections took 279 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-13 14:17:52.118 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:17:52.235 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-13 14:17:52.247 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-13 14:17:52.380 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:17:52.383 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@fbe308a8
2025-03-13 14:17:53.255 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-13 14:17:54.782 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-13 14:17:54.782 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-13 14:17:54.784 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-13 14:17:54.784 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-13 14:17:54.785 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-13 14:17:54.804 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 14:17:55.136 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 14:17:55.143 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-13 14:17:56.127 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-13 14:17:56.157 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-13 14:17:56.172 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-13 14:17:56.172 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-13 14:17:56.173 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-13 14:17:56.173 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-13 14:17:56.174 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-13 14:17:56.174 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-13 14:17:56.175 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-13 14:17:56.233 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 14:17:56.233 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-13 14:17:56.264 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-13 14:17:57.208 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@409864c6: tags=[[amq.ctag-dl12enn-cwGBVcNk573TBQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@8836b712 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 14:17:57.240 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@5bcbc538: tags=[[amq.ctag-P1adRmYZIr_UH-_J0WswXg]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@8836b712 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-13 14:17:57.400 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-13 14:17:57.400 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-13 14:17:57.400 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!

// package com.caidaocloud.pangu.infrastructure.ignite.store;
//
// import javax.cache.configuration.Factory;
//
// import com.caidaocloud.util.SpringUtil;
// import lombok.val;
// import org.apache.ignite.Ignite;
// import org.apache.ignite.IgniteCache;
// import org.apache.ignite.cache.store.CacheStore;
// import org.apache.ignite.configuration.CacheConfiguration;
//
// /**
//  *
//  * <AUTHOR>
//  * @date 2023/7/28
//  */
// public interface IgniteDbStoreConfigurationFactory<T extends CacheStore> {
//
// 	Factory<T> createFactory(String cacheName);
//
// 	CacheConfiguration createCacheConfiguration(String cacheName);
//
// }

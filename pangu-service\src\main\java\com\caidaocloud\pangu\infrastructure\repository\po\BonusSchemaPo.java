package com.caidaocloud.pangu.infrastructure.repository.po;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.domain.entity.ApproveConfigDo;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.PeriodDate;
import com.caidaocloud.pangu.domain.enums.MonthPeriod;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

@Data
public class BonusSchemaPo extends DataSimple {

    private String name;

    private String structId;

    @DisplayAsObject
    private ConditionTree coverage;

    private EnumSimple month;

    private Integer day;

    private ApproveConfigDo approveConfig;

    private MonthPeriod selectionMonth;
    private Integer selectionDay;

    // 特殊条件
    private Boolean special;

    private MonthPeriod hireDateMonth;
    private Integer hireDateDay;

    private MonthPeriod terminationDateMonth;
    private Integer terminationDateDay;

    public static final String BONUS_SCHEMA_IDENTIFIER = "entity.bonus.BonusSchema";

    public BonusSchema toEntity(){
        BonusSchema schema = ObjectConverter.convert(this, BonusSchema.class);
        schema.setSelection(new PeriodDate(selectionMonth, selectionDay));
        schema.setHireDate(new PeriodDate(hireDateMonth, hireDateDay));
        schema.setTerminationDate(new PeriodDate(terminationDateMonth, terminationDateDay));
        return schema;
    }

    public  static BonusSchemaPo fromEntity(BonusSchema bonusSchema) {
        BonusSchemaPo po = ObjectConverter.convert(bonusSchema, BonusSchemaPo.class);
        if (bonusSchema.getSelection() != null) {
            po.setSelectionMonth(bonusSchema.getSelection().getMonth());
            po.setSelectionDay(bonusSchema.getSelection().getDay());
        }
        if (bonusSchema.getHireDate() != null) {
            po.setHireDateMonth(bonusSchema.getHireDate().getMonth());
            po.setHireDateDay(bonusSchema.getHireDate().getDay());
        }
        if (bonusSchema.getTerminationDate() != null) {
            po.setTerminationDateMonth(bonusSchema.getTerminationDate().getMonth());
            po.setTerminationDateDay(bonusSchema.getTerminationDate().getDay());
        }
        return po;
    }
}
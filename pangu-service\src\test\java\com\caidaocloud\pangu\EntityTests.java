// package com.caidaocloud.pangu;
//
// import java.lang.reflect.Field;
// import java.util.LinkedHashMap;
// import java.util.List;
// import java.util.Map;
//
// import javax.cache.Cache;
// import javax.cache.configuration.Factory;
// import javax.sql.DataSource;
//
// import com.caidaocloud.util.SnowflakeUtil;
// import com.googlecode.totallylazy.Lists;
// import com.zaxxer.hikari.HikariDataSource;
// import lombok.extern.slf4j.Slf4j;
// import model.Address;
// import model.Employee;
// import model.Organization;
// import org.apache.ignite.Ignite;
// import org.apache.ignite.IgniteCache;
// import org.apache.ignite.binary.BinaryObject;
// import org.apache.ignite.cache.QueryEntity;
// import org.apache.ignite.cache.query.FieldsQueryCursor;
// import org.apache.ignite.cache.query.QueryCursor;
// import org.apache.ignite.cache.query.ScanQuery;
// import org.apache.ignite.cache.query.SqlFieldsQuery;
// import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
// import org.apache.ignite.configuration.CacheConfiguration;
// import org.jetbrains.annotations.NotNull;
// import org.junit.Assert;
// import org.junit.Test;
// import org.junit.runner.RunWith;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
// /**
//  *
//  * <AUTHOR> Zhou
//  * @date 2023/7/6
//  */
// @RunWith(SpringJUnit4ClassRunner.class)
// @SpringBootTest(classes = PanguApplication.class)
// @Slf4j
// public class EntityTests {
// 	@Autowired
// 	private Ignite ignite;
// 	private static SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
//
// 	static CacheConfiguration cache_configuration = new CacheConfiguration();
// 	static String sql_cache_name = "EmployeeCachev9";
// 	static String table_name = "Employee";
// 	static String snow_flake_id;
// 	static String snow_flake_id2;
// 	static {
// 		cache_configuration.setName(sql_cache_name);
// 		// QueryEntity entity = new QueryEntity();
// 		// entity.setTableName(table_name);
// 		// entity.setKeyFieldName("empId");
// 		// entity.setKeyType(String.class.getName());
// 		// entity.setValueType(Employee.class.getName());
// 		// Map<String, String> fieldMap = new LinkedHashMap<>();
// 		// for (Field field : Employee.class.getDeclaredFields()) {
// 		// 	fieldMap.put(field.getName(), field.getType().getName());
// 		// }
// 		// log.info("emp fields={}", fieldMap);
// 		// entity.setFields(((LinkedHashMap<String, String>) fieldMap));
// 		// cache_configuration.setQueryEntities(Lists.list(entity));
// 		snow_flake_id = String.valueOf(snowflakeUtil.createId());
// 		snow_flake_id2 = String.valueOf(snowflakeUtil.createId());
//
// 		tmp();
// 	}
//
// 	private static void tmp() {
// 		CacheJdbcBlobStoreFactory<Integer, Employee> blobStore = new CacheJdbcBlobStoreFactory<>();
// 		// blobStore.setDataSource(getDataSourceFactory().create());
// 		blobStore.setCreateTableQuery("CREATE TABLE IF NOT EXISTS \"ENTRIES3\" (\"akey\" bytea NOT NULL,\"val\" bytea);");
// 		blobStore.setUpdateQuery("update \"ENTRIES3\" set val=? where akey=?");
// 		blobStore.setInsertQuery("insert into \"ENTRIES3\" (akey, val) values (?, ?)");
// 		blobStore.setDeleteQuery("delete from \"ENTRIES3\" where akey=?");
// 		blobStore.setLoadQuery("select * from \"ENTRIES3\" where akey=?");
// 		blobStore.setConnectionUrl("****************************************");
// 		blobStore.setUser("postgres");
// 		blobStore.setPassword("Caidao01");
//
// 		// blobStore.setDataSource(getDataSourceFactory().create());
// 		// CacheJdbcPojoStoreFactory<Integer, Employee> factory = new CacheJdbcPojoStoreFactory<>();
// 		//
// 		// factory.setDialect(new BasicJdbcDialect());
// 		//
// 		// factory.setDataSourceFactory(getDataSourceFactory());
// 		//
// 		// JdbcType employeeType = getJdbcType();
// 		//
// 		// factory.setTypes(employeeType);
//
// 		cache_configuration.setCacheStoreFactory(blobStore);
// 		cache_configuration.setReadThrough(true);
// 		cache_configuration.setWriteThrough(true);
// 	}
//
//
// 	@Test
// 	public void entityTest(){
// 		IgniteCache<String, Employee> empCache = dataInit();
//
// 		QueryCursor<Cache.Entry<String, BinaryObject>> result = empCache.withKeepBinary().query(new ScanQuery<>(
// 				(k, p) -> ((long) p.field("salary")) > 1000));
// 		List<Cache.Entry<String, BinaryObject>> all = result.getAll();
// 		for (int i = 0; i < all.size(); i++) {
// 			Cache.Entry<String, BinaryObject> entry = all.get(i);
// 			log.info("emp info={}", entry.getValue());
// 		}
// 		Assert.assertEquals(1, all.size());
// 		Assert.assertEquals("test02", all.get(0).getValue().field("name"));
// 	}
//
// 	@NotNull
// 	private IgniteCache<String, Employee> dataInit() {
// 		IgniteCache<String, Employee> empCache = ignite.getOrCreateCache(cache_configuration);
// 		empCache.removeAll();
// 		Employee emp01 = new Employee(snow_flake_id, "test01", 1000, new Address("1545 Jackson Street", 94612), Lists.list("dept01"));
// 		empCache.put(snow_flake_id, emp01);
// 		empCache.put(snow_flake_id2, new Employee(snow_flake_id2, "test02", 2000, new Address("1545 Jackson Street", 94612), Lists.list("dept01")));
// 		Employee employee = empCache.get(snow_flake_id);
// 		log.info("fetch emp info from ignite,value={}", employee);
// 		Assert.assertNotNull(employee);
// 		return empCache;
// 	}
//
// 	@Test
// 	public void sumTest(){
// 		dataInit();
// 		IgniteCache cache = ignite.getOrCreateCache(cache_configuration);
// 		FieldsQueryCursor<List<?>> result = cache.withKeepBinary()
// 				.query(new SqlFieldsQuery("select * from Employee where  salary = ?").setArgs(1000));
// 		for (List<?> fields : result) {
// 			log.info("emp name:{}", fields.get(1));
// 			Assert.assertEquals("test01", fields.get(1));
// 		}
// 	}
//
//
// 	@Test
// 	public void twoEntityWithSameKeyTest(){
// 		dataInit();
// 		IgniteCache cache = ignite.getOrCreateCache(cache_configuration);
// 		Organization organization = new Organization("test org 01");
// 		organization.setOrgId(Long.valueOf(snow_flake_id));
// 		cache.put(organization.getOrgId(), organization);
// 		FieldsQueryCursor<List<?>> result = cache.withKeepBinary()
// 				.query(new SqlFieldsQuery("select * from Employee "));
// 		List<List<?>> all = result.getAll();
// 		Assert.assertEquals(2, all.size());
// 		for (List<?> field : all) {
// 			log.info("emp name:{}", field.get(1));
// 		}
//
// 		//  result = cache.withKeepBinary()
// 		// 		.query(new SqlFieldsQuery("select * from Organization "));
// 		//  all = result.getAll();
// 		// log.info("org size:{}", all.size());
// 	}
//
// 	private static Factory<DataSource> getDataSourceFactory() {
// 		return () -> {
// 			HikariDataSource driverManagerDataSource = new HikariDataSource();
// 			driverManagerDataSource.setDriverClassName("org.postgresql.Driver");
// 			driverManagerDataSource.setJdbcUrl("****************************************");
// 			driverManagerDataSource.setUsername("postgres");
// 			driverManagerDataSource.setPassword("Caidao01");
// 			return driverManagerDataSource;
// 		};
// 	}
// }
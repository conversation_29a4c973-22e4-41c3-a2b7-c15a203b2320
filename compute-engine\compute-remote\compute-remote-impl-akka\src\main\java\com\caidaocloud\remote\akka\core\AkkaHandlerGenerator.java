package com.caidaocloud.remote.akka.core;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.compute.remote.framework.core.AbsInvokeHandler;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;
import com.caidaocloud.compute.remote.framework.actor.RemoteRequest;
import com.caidaocloud.compute.remote.framework.core.AbsHandlerGenerator;
import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import com.caidaocloud.remote.akka.constant.AkkaConstant;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

/**
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Slf4j
public class AkkaHandlerGenerator extends AbsHandlerGenerator {

	public AkkaHandlerGenerator(RemoteInitializer initializer) {
		super(initializer);
	}

	@Override
	public <T> T createProxy(Class<T> serviceInterface, Map<String, MethodMetadata> methodMetadataMap) {
		RemoteRequest request = serviceInterface.getAnnotation(RemoteRequest.class);
		String path = request.value();
		return (T) Proxy.newProxyInstance(
				serviceInterface.getClassLoader(),
				new Class<?>[] {serviceInterface},
				new AkkaInvokeHandler(path, methodMetadataMap));
	}



	private class AkkaInvokeHandler extends AbsInvokeHandler {
		private Map<String, AkkaMethodHandler> methodHandlerCache = new HashMap<>();


		public AkkaInvokeHandler(String path, Map<String, MethodMetadata> methodMetadataMap) {
			super(path,methodMetadataMap);
		}

		private AkkaMethodHandler methodHandler(String method) {
			AkkaMethodHandler handler = methodHandlerCache.get(method);
			if (handler != null) {
				return handler;
			}
			MethodMetadata methodMetadata = getMethodMetadataMap().get(method);
			ActorSystem actorSystem = ((AkkaInitializer) getInitializer()).getActorSystem();
			handler = methodMetadata.getMethod().getReturnType()
					.equals(void.class) ? new AsyncMethodHandler(actorSystem, methodMetadata) : new FutureMethodHandler(actorSystem, methodMetadata);
			methodHandlerCache.put(method, handler);
			return handler;
		}


		@Override
		protected Object doInvoke(String path, Method method, Object[] args) {
			String methodName = MethodMetadata.parseMethodName(method);
			AkkaMethodHandler handler = methodHandler(methodName);
			MethodMetadata metadata = handler.getMethodMetadata();
			Address address = ((Address) args[metadata.getAddressIndex()]);
			Object[] realArgs = removeAddressIndex(args, metadata.getAddressIndex());
			AkkaInitializer initializer = (AkkaInitializer) getInitializer();
			ActorSelection actorSelection = initializer.getActorSystem()
					.actorSelection(String.format(AkkaConstant.AKKA_REMOTE_PATH, initializer.getInitializerConfig()
							.getServerName(), address.toString(), path));
			ActorMessageWrapper request = new ActorMessageWrapper(method.getName()).setArgs(realArgs);
			return handler.doExecute(actorSelection, request);

		}

	}

}

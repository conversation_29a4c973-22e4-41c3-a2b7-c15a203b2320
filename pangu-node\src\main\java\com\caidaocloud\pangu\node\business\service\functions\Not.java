package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import lombok.val;

import java.util.Map;

public class Not extends AbstractFunction {
    @Override
    public String getName() {
        return "NOT";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1) {
        val param = aviatorObject1.getValue(env);
        if(Boolean.TRUE == param){
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(true).append(")=").append(false).toString());
            }
            return FunctionUtils.wrapReturn(false);
        }else{
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(param).append(")=").append(true).toString());
            }
            return FunctionUtils.wrapReturn(true);
        }
    }
}

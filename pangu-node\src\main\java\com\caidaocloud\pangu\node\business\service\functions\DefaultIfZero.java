package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorDataFilter;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Map;

public class DefaultIfZero extends AbstractFunction {

    @Override
    public String getName() {
        return "DEFAULT_IF_ZERO";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val num = FunctionUtils.getNumberValue(aviatorObject1, env);
        val defaultValue = FunctionUtils.getNumberValue(aviatorObject2, env);
        val isZero = new BigDecimal(num.toString()).compareTo(new BigDecimal(0)) == 0;
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(num).append(",").append(defaultValue).append(")=").append(isZero?defaultValue:num).toString());
        }
        return isZero ? aviatorObject2 : aviatorObject1;
    }
}

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.pangu.core.ignite.IgniteCacheContext;
import com.caidaocloud.pangu.manager.PanguManagerApplication;
import com.caidaocloud.pangu.manager.util.DispatchTool;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguManagerApplication.class)
public class IgniteUtilTest {

	@Test
	public void ts(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		MockitoAnnotations.initMocks(this);
		IgniteCacheContext<String, TaskDispatchDetail> context = SpringUtil.getBean(DispatchTool.class)
				.getDispatchContext();
		TaskDispatchDetail dispatch = new TaskDispatchDetail();
		dispatch.setExecSeqId("1");
		dispatch.setExecNodeSeqId("2");
		dispatch.setDispatchNodeId("3");
		dispatch.setSubTask(true);
		dispatch.setStatus(TaskDispatchDetail.Status.DISPATCHED);
		dispatch.setFrom(0);
		dispatch.setTo(1);
		dispatch.setId(SnowUtil.nextId());
		context.put(dispatch.getId(), dispatch);
		context.get(dispatch.getId());
		System.out.println(FastjsonUtil.toJson(context.loadProperties(DataFilter.eq("dispatchNodeId", "3")
				.andEq("status", dispatch.getStatus()
						.name()), Lists.list("execNodeSeqId", "from"), Lists.list(), 0, -1)));
		// IgniteCacheContext cache = SpringUtil.getBean(IgniteUtil.class)
		// 		.dynamicPojoCache(SnowUtil.nextId(), "entity.hr.EmpWorkInfo", null);
		// List data = cache.loadProperties(null, Lists.list(), Lists.list("bid"), 0, 10);
		// System.out.println(FastjsonUtil.toJson(data));

		// SpringUtil.getBean(DispatchTool.class).getDispatchContext();
	}

}

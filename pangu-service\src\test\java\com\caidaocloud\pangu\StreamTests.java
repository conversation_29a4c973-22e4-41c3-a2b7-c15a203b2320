// package com.caidaocloud.pangu;
//
// import lombok.extern.slf4j.Slf4j;
// import lombok.val;
// import org.apache.ignite.Ignite;
// import org.apache.ignite.IgniteCache;
// import org.apache.ignite.IgniteDataStreamer;
// import org.apache.ignite.binary.Binarylizable;
// import org.apache.ignite.configuration.CacheConfiguration;
// import org.apache.ignite.lang.IgniteBiInClosure;
// import org.apache.ignite.stream.StreamReceiver;
// import org.apache.ignite.stream.StreamTransformer;
// import org.apache.ignite.stream.StreamVisitor;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
// import java.util.Map;
//
// /**
//  *
//  * <AUTHOR>
//  * @date 2023/7/6
//  */
// @RunWith(SpringJUnit4ClassRunner.class)
// @SpringBootTest(classes = PanguApplication.class)
// @Slf4j
// public class StreamTests {
//
//     @Autowired
//     private Ignite ignite;
//
//     @Test
//     public void InsertAndQueryTest(){
//
//         CacheConfiguration<String, Long> cfg = new CacheConfiguration<>("myCache");
//
//         IgniteCache<String, Long> stmCache = ignite.getOrCreateCache(cfg);
//
//         IgniteDataStreamer<Integer, String> stmr = ignite.dataStreamer("myCache");
//
//         try {
//             Thread.sleep(10000);
//         } catch (InterruptedException e) {
//             e.printStackTrace();
//         }
//
//         stmr.allowOverwrite(true);
//
// //        stmr.receiver((StreamReceiver<Integer, String>) (cache, entries) -> entries.forEach(entry -> {
// //            cache.put(entry.getKey(), entry.getValue());
// //        }));
// //        stmr.receiver(StreamTransformer.from((entry, args) -> {
// //            Integer symbol = entry.getKey();
// //            String tick = entry.getValue();
// //            entry.setValue(tick +" ");
// //            return "";
// //        }));
//         stmr.receiver(StreamVisitor.from((cache, e) -> {
//             Integer symbol = e.getKey();
//             String tick = e.getValue();
//             cache.put(symbol, tick);
//         }));
//
//         for (int i = 0; i < 3; i++){
//             stmr.addData(i, Integer.toString(i));
//         }
//         stmr.close();
//         val a = stmr.future().get();
//     }
// }

package com.caidaocloud.remote.akka;

import akka.serialization.JSerializer;
import com.caidaocloud.compute.remote.framework.serialize.SerializerUtils;

public class AkkaSerializer extends JSerializer {

    @Override
    public Object fromBinaryJava(byte[] bytes, Class<?> manifest) {
        return SerializerUtils.deSerialized(bytes);
    }

    @Override
    public int identifier() {
        return 277777;
    }

    @Override
    public byte[] toBinary(Object o) {
        return SerializerUtils.serialize(o);
    }

    @Override
    public boolean includeManifest() {
        return false;
    }
}
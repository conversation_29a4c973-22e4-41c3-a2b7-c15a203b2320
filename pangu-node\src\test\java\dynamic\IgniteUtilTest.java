package dynamic;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.cache.Cache;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.dto.IgniteCacheValue;
import com.caidaocloud.pangu.core.ignite.DatasourceProperties;
import com.caidaocloud.pangu.core.ignite.IgniteCacheContext;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.core.ignite.PgsqlDbContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoContext;
import com.caidaocloud.pangu.node.PanguNodeApplication;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.cache.query.CacheQueryEntryEvent;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.internal.binary.BinaryObjectImpl;
import org.apache.ignite.internal.processors.cache.IgniteCacheProxy;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguNodeApplication.class)
public class IgniteUtilTest {

	@Autowired
	private IgniteUtil igniteUtil;
	@Autowired
	private Ignite ignite;
	@Test
	public void ts(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		MockitoAnnotations.initMocks(this);

		String id = SnowUtil.nextId();
		IgniteCacheContext cache = SpringUtil.getBean(IgniteUtil.class)
				.dynamicPojoCache(id, "entity.hr.CalcFirst", null, 0l);
		System.out.println(cache.sum(null, "intDemo", "org"));
		cache.loadProperties(null, Lists.list(), Lists.list("bid"), 0, 20);
		// igniteUtil.copyCache(SnowUtil.nextId(), id, null);
	}


	@Test
	public void copyTest(){
		String id ="123456";
		String copyId = "654321";
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId("11");
			SecurityUserUtil.setSecurityUserInfo(userInfo);
			MockitoAnnotations.initMocks(this);

			IgniteCacheContext<Integer, Person2> context = igniteUtil.keyValueCache("c" + id, int.class, Person2.class);
			context.put(1,new Person2(1,"a"));
			context.put(2,new Person2(2,"b"));
			context.put(3,new Person2(3,"c"));
			context.put(4,new Person2(4,"d"));
			// System.out.println(cache.sum(null, "intDemo", "org"));
			// cache.loadProperties(null, Lists.list(), Lists.list("bid"), 0, 20);
			IgniteCacheContext copy = igniteUtil.copyCache("c"+copyId, "c"+id, DataFilter.ne("name", "a"));
			int count = 0;
			Iterator iterator = copy.getCache().iterator();
			while (iterator.hasNext()) {
				count += 1;
				iterator.next();
			}

			Assert.assertEquals(3, count);

		}finally {
			// ignite.destroyCache("c" + id);
			// ignite.destroyCache("c" + copyId);
		}

	}
	@Test
	public void dynamicTest(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		String id = SnowUtil.nextId();
		try {
			Map<String, Class<?>> map = Maps.map();
			map.put("a", String.class);
			map.put("b", Long.class);
			map.put("c", double.class);
			IgniteCacheContext cache = SpringUtil.getBean(IgniteUtil.class)
					.keyValueCache("c" + id, String.class, map);
			List<IgniteCacheValue> list = Lists.list();
			list.add(new IgniteCacheValue());
			list.get(0).setProperty("a");
			list.get(0).setValue("01");

			list.add(new IgniteCacheValue());
			list.get(1).setProperty("b");
			list.get(1).setValue(2L);

			list.add(new IgniteCacheValue());
			list.get(2).setProperty("c");
			list.get(2).setValue(3.3D);

			String nextId = SnowUtil.nextId();
			cache.putValue(nextId, list);
			System.out.println(FastjsonUtil.toJson(cache.get(nextId)));
		}finally {
			ignite.destroyCache(id);
		}
	}

	@Test
	public void paasTest(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		String id = SnowUtil.nextId();
		try {

			IgniteCacheContext cache = SpringUtil.getBean(IgniteUtil.class)
					.dynamicPojoCache(id, "entity.hr.EmpWorkInfo", null, 0l);
			for (Object emp :  cache.getCache().withKeepBinary()) {

				System.out.println(FastjsonUtil.toJson(((BinaryObjectImpl) ((Cache.Entry) emp).getValue()).deserialize(DynamicPojoContext.loadClass(cache.getPojoDef().getClassName()).getClassLoader())));
			}
		}finally {
			ignite.destroyCache(id);
		}
	}


	@Test
	public void dynamicPojoTest(){
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		String id = SnowUtil.nextId();
		try {
			Map<String, Class<?>> map = Maps.map();
			map.put("a", String.class);
			map.put("b", Long.class);
			map.put("c", double.class);
			IgniteCacheContext cache = SpringUtil.getBean(IgniteUtil.class)
					.keyValueCache("c" + id, String.class, map);
			List<IgniteCacheValue> list = Lists.list();
			list.add(new IgniteCacheValue());
			list.get(0).setProperty("a");
			list.get(0).setValue("01");

			list.add(new IgniteCacheValue());
			list.get(1).setProperty("b");
			list.get(1).setValue(2L);

			list.add(new IgniteCacheValue());
			list.get(2).setProperty("c");
			list.get(2).setValue(3.3D);

			String nextId = SnowUtil.nextId();
			cache.putValue(nextId, list);
			cache.get(nextId);
		}finally {
			ignite.destroyCache(id);
		}
	}

	@Test
	public void kvTest(){

		CacheConfiguration<Integer, Person> personCacheCfg = new CacheConfiguration<>();
		String cacheName = "PersonCache";
		personCacheCfg.setName("PersonCache");
		personCacheCfg.setStoreKeepBinary(true);

		CacheJdbcBlobStoreFactory<Integer, Person> cacheStoreFactory = new CacheJdbcBlobStoreFactory<>();
		// cacheStoreFactory.setUser("USER_NAME");


		// cacheStoreFactory.setDataSourceBean("pgDataSource");
		DatasourceProperties properties = new DatasourceProperties();
		properties.setJdbcUrl("***************************************************************************");
		properties.setDriverClassName("org.postgresql.Driver");
		properties.setUsername("postgres");
		properties.setPassword("Caidao2022");
		cacheStoreFactory.setConnectionUrl(properties.getJdbcUrl());
		cacheStoreFactory.setUser(properties.getUsername());
		cacheStoreFactory.setPassword(properties.getPassword());
		HikariDataSource driverManagerDataSource = new HikariDataSource();
		driverManagerDataSource.setDriverClassName(properties.getDriverClassName());
		driverManagerDataSource.setJdbcUrl(properties.getJdbcUrl());
		driverManagerDataSource.setUsername(properties.getUsername());
		driverManagerDataSource.setPassword(properties.getPassword());
		cacheStoreFactory.setDataSource(driverManagerDataSource);

		PgsqlDbContext dbContext = new PgsqlDbContext(properties.getJdbcUrl(), properties.getUsername(), properties.getPassword());
		cacheStoreFactory.setLoadQuery(dbContext.generateLoadSql(cacheName));
		cacheStoreFactory.setDeleteQuery(dbContext.generateDeleteSql(cacheName));
		cacheStoreFactory.setInsertQuery(dbContext.generateInsertSql(cacheName));
		cacheStoreFactory.setUpdateQuery(dbContext.generateUpdateSql(cacheName));
		cacheStoreFactory.setCreateTableQuery(dbContext.generateCreateTableSql(cacheName));
		personCacheCfg.setCacheStoreFactory(cacheStoreFactory);
		personCacheCfg.setWriteThrough(true);
		personCacheCfg.setReadThrough(true);


		try (IgniteCache cache = ignite.getOrCreateCache(personCacheCfg)) {
			// System.out.println( ((Person) cache.get(1)).getName());
			cache.put(8, new Person(8, "test3"));

			Assert.assertEquals("test3", ((Person) cache.get(8)).getName());

		}finally {
			// ignite.destroyCache("PersonCache");
		}
	}
}

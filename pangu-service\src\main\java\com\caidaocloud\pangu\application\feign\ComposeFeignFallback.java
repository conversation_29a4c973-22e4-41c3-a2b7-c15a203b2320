package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementQueryDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementDetailDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@Component
public class ComposeFeignFallback implements ComposeFeign {
	@Override
	public Result<List<ComposeDefDto>> listComposeDef(List<String> composeIds) {
		return Result.fail();
	}

	@Override
	public Result<Boolean> reference(String arrangementId) {
		return Result.fail();
	}

	@Override
	public Result<List<ArrangementDetailDto>> loadDetailPublishedList(ArrangementQueryDto dto) {
		return Result.fail();
	}

	@Override
	public Result<ArrangementDetailDto> loadDetailVersion(ArrangementQueryDto dto) {
		return Result.fail();
	}

	@Override
	public Result<ComposeDefDto> loadPublished(String arrangementId) {
		return Result.fail();
	}

	@Override
	public Result<ArrangementDetailDto> loadDetailPublished(String arrangementId) {
		return Result.fail();
	}
}

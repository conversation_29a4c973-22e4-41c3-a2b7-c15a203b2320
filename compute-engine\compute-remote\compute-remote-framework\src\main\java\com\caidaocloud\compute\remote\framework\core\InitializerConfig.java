package com.caidaocloud.compute.remote.framework.core;

import java.net.InetAddress;

import javax.annotation.PostConstruct;

import com.caidaocloud.compute.remote.framework.constant.ServerRole;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/10/23
 */
@Data
@ConfigurationProperties("caidaocloud.remote")
public class InitializerConfig {
	private String serverName;
	private ServerRole role;
	private String host;
	// 默认值
	private int port = 19000;
	private int portRange=1;


	@Autowired
	private InetUtils inetUtils;
	@PostConstruct
	public void init(){
		if (StringUtils.isEmpty(host)) {
			InetAddress address = inetUtils.findFirstNonLoopbackAddress();
			host = address.getHostAddress();
		}
	}

}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class GetWorkNo extends AbstractFunction {
    @Override
    public String getName() {
        return "GET_WORK_NO";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject) {
        String empId = FunctionUtils.getStringValue(aviatorObject, env);
        val workNo = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .filterProperties(
                        DataFilter.eq("empId", empId).andNe("deleted", "true"),
                        Lists.list("workno"), System.currentTimeMillis())
                .getItems().stream().findFirst().map(it-> StringUtils.trimToEmpty(it.get("workno"))).orElse("");
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(empId).append(")=").append(workNo).toString());
        }
        return new AviatorString(workNo);
    }
}

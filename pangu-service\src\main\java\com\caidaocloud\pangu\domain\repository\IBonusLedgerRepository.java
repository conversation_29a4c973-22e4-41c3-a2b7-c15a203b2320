package com.caidaocloud.pangu.domain.repository;

import java.util.List;
import java.util.Map;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerEmp;
import com.caidaocloud.pangu.domain.entity.BonusApproveRecord;
import com.caidaocloud.pangu.domain.entity.BonusLedgerStep;

/**
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public interface IBonusLedgerRepository {
	String insert(BonusLedger bonusLedger);

	void update(BonusLedger bonusLedger);

	void delete(String bid);

	BonusLedger loadByBid(String bid);


	PageResult<BonusLedgerEmp> loadAllEmp(BonusLedgerEmpPageDto queryDto, String ledgerId, int pageNo, int pageSize);

	void syncEmp(String ledgerId, List<BonusLedgerEmp> emps);
	void addEmp(String ledgerId, List<BonusLedgerEmp> emps);


	void removeEmp(BonusLedgerEmp emp);

	PageResult<BonusLedger> page(String name, int pageNo, int pageSize);

	List<BonusLedger> loadOpen();

 	List<BonusLedgerEmp> loadSimpleEmp(String bid, List<String> empIds);

	void updateEmp(BonusLedgerEmp emp);

	void saveApproveReceipt(BonusApproveRecord bonusApproveRecord);

	BonusApproveRecord loadApproveReceiptByBusinessId(String businessId);

	BonusApproveRecord loadApproveReceiptByLedgerId(String accountBid);

	List<BonusLedger> loadBySchemaId(String bid);

	List<BonusLedgerEmp> loadAccountEmpByAccountBidAndEmpId(List<String> accountBids, String empId);

	void saveStep(BonusLedgerStep step);

	List<BonusLedgerStep> loadStep(String bid);

	/**
	 * 根据ID加载步骤
	 */
	BonusLedgerStep loadStepById(String stepId);

	/**
	 * 更新步骤
	 */
	void updateStep(BonusLedgerStep step);

	void exec(BonusLedgerStep currentStep, Map<String, String> context);

	void removeEmpBatch(String bid, List<String> empId);

	BonusLedgerStep loadStepByExecId(String execId);

	void deleteStepByLedgerId(String bid);

	void revoke(String businessKey);

	List<BonusLedger> findByIds(List<String> ids);

	int countEmpByLedgerId(String bid);
}

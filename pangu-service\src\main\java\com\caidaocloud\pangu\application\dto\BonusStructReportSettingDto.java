
package com.caidaocloud.pangu.application.dto;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.pangu.domain.repository.IBonusStructReportItemRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 奖金结构--奖金项配置
 */
@Data
@NoArgsConstructor
public class BonusStructReportSettingDto extends DataSimple {
    private String structId;
    private List<String> basic;
    private List<String> item;

}

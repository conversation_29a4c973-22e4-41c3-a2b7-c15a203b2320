package com.caidaocloud.pangu.manager.feign;

import com.alibaba.nacos.api.naming.pojo.ListView;
import com.caidaocloud.pangu.manager.annotations.FeignThrow;
import com.caidaocloud.pangu.manager.dto.ArrangementDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = "${feign.rename.caidaocloud-pangu-node:caidaocloud-pangu-node}", fallback = PanguNodeFeignFallback.class, configuration = FeignConfiguration.class, contextId = "panguNodeFeign")
public interface PanguNodeFeign {

    @GetMapping("/api/arrangement/v1/arrangement/load/published")
    @FeignThrow
    Result<ArrangementDto> fetchArrangement(@RequestParam String arrangementId);

    @GetMapping("/api/arrangement/v1/arrangement/node/previous")
    @FeignThrow
    Result<List<String>> fetchPreviousNodes(@RequestParam String arrangementVid, @RequestParam String arrangementNodeId);

    @GetMapping("/api/arrangement/v1/arrangement/node/next")
    @FeignThrow
    Result<List<String>> fetchNextNodes(@RequestParam String arrangementVid, @RequestParam String arrangementNodeId);

    @GetMapping("/api/arrangement/v1/arrangement/node/names")
    @FeignThrow
    Result<Map<String, List<String>>> fetchAllNodeNames(@RequestParam String arrangementVid);
}

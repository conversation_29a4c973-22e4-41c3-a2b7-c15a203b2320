package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Maps;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class Avg extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "AVG";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        BigDecimal result = new BigDecimal(0);
        int count = 0;
        val log = new StringBuilder(getName()).append("(");
        for(AviatorObject aviatorObject : aviatorObjects){
            val decimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
            log.append(decimal).append(",");
            result = result.add(decimal);
            count++;
        }
        result = result.divide(new BigDecimal(count), 6, RoundingMode.HALF_UP);
        if(FunctionLogTool.logEnabled()){
            log.setLength(log.length()-1);
            log.append(")=").append(result);
            FunctionLogTool.log(log.toString());
        }
        return new AviatorDecimal(result);
    }

}

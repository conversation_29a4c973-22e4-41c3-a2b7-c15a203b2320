package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.pangu.domain.enums.ApproveStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
@Data
public class BonusLedgerVo {


	private String bid;

	/**
	 * 奖金方案id
	 */
	private String schemaId;


	private String name;

	@ApiModelProperty("账套名称")
	private Map<String,String> i18nName;;

	/**
	 * 计算年月
	 */
	private Long month;


	/**
	 * 开始日期
	 */
	private Long startDate;

	/**
	 * 结束日期
	 */
	private Long endDate;

	@ApiModelProperty("计算状态")
	private String progress;

	private BonusLedgerStatus status;

	private ApproveStatus approveStatus;

	private EnumSimple executionType;

	@ApiModelProperty("选人周期开始日期")
	private Long selectionStartDate;

	@ApiModelProperty("选人周期结束日期")
	private Long selectionEndDate;
}

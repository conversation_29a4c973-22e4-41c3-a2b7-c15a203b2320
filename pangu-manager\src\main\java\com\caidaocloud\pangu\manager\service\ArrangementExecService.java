package com.caidaocloud.pangu.manager.service;

import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.pangu.core.dto.NodeExec;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.core.ignite.KeyValueDto;
import com.caidaocloud.pangu.manager.dto.ArrangementDto;
import com.caidaocloud.pangu.manager.feign.ArrangementClient;
import com.caidaocloud.pangu.manager.feign.PanguNodeFeign;
import com.caidaocloud.pangu.manager.feign.UserFeign;
import com.caidaocloud.pangu.manager.model.ArrangementExec;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExec;
import com.caidaocloud.pangu.manager.util.DispatchTool;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ArrangementExecService {

    @Autowired
    private Locker locker;

    @Autowired
    private PanguNodeFeign panguNodeFeign;

    @Autowired
    private NodeManagerService nodeManagerService;

    @Resource
    private ArrangementClient arrangementClient;

    @Autowired
    private DispatchTool dispatchTool;

    @Autowired
    private UserFeign userFeign;

    @SneakyThrows
    @Transactional
    public String exec(ArrangementDto.Exec arrangement) {
        val actualTenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        val empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
        String arrangementId = arrangement.getArrangementId();
        val lock = locker.getLock("MarkArrangementStatus_" + arrangementId);
        val locked = lock.tryLock(10, TimeUnit.SECONDS);
        if (locked) {
            try {
                val started = ArrangementExec.countByArrangementAndStatus(arrangementId,
                                ArrangementExec.Status.STARTED) > 0;
                if (started) {
                    throw new ServerException("arrangement is being executed, try later");
                }
                val arrangementBasic = panguNodeFeign.fetchArrangement(arrangementId).getData();
                val arrangementVid = arrangementBasic.getArrangementVid();
                val exec = new ArrangementExec();
                exec.setExecSeqId(SnowUtil.nextId());
                exec.setArrangementId(arrangementId);
                exec.setArrangementVid(arrangementVid);
                exec.setStatus(ArrangementExec.Status.STARTED);
                exec.setCreateTime(System.currentTimeMillis());
                exec.setStarted(System.currentTimeMillis());
                if(null != empId){
                    val emp = new EmpSimple();
                    emp.setEmpId(empId.toString());
                    EmpSimple.doEmpSimple(emp);
                    exec.setWorkNo(emp.getWorkno());
                    exec.setEmpName(emp.getName());
                }else{
                    exec.setEmpName((String)userFeign.getUserAndAccountInfo(SecurityUserUtil.getSecurityUserInfo().getUserId())
                            .getData().get("account"));
                }
                exec.setArrangementName(arrangementBasic.getName());
                Map<String, String> appendContext = Maps.map();
                arrangement.getContext().forEach((contextKey, contextValue)->{
                    appendContext.put("$.env." + contextKey, contextValue);
                });
                arrangement.getContext().put("$.env.execSeqId", exec.getExecSeqId());
                arrangement.getContext().put("$.env.arrangementVid", arrangementVid);
                arrangement.getContext().putAll(appendContext);
                exec.setContext(arrangement.getContext());
                exec.setType(arrangementBasic.getType());
                exec.setActualTenantId(actualTenantId);
                exec.create();

                ArrangementNodeExec nodeExec = new ArrangementNodeExec();
                nodeExec.setArrangementId(exec.getArrangementId());
                nodeExec.setArrangementVid(exec.getArrangementVid());
                nodeExec.setExecSeqId(exec.getExecSeqId());
                nodeExec.setExecNodeSeqId(SnowUtil.nextId());
                nodeExec.setStatus(ArrangementNodeExec.Status.WAITING);
                nodeExec.setContext(exec.getContext());
                nodeExec.setArrangementNodeId("START");
                nodeExec.setWaitFrom(System.currentTimeMillis());
                nodeExec.setActualTenantId(actualTenantId);
                nodeExec.create();

                return exec.getExecSeqId();
            } finally {
                lock.unlock();
            }
        } else {
            throw new ServerException("locker fetch error, try later");
        }
    }

    @Transactional
    public void skipNode(ArrangementNodeExec skip){
        skip.setStatus(ArrangementNodeExec.Status.SKIPPED);
        skip.setWaitFrom(System.currentTimeMillis());
        skip.setEnded(skip.getWaitFrom());
        skip.update();
        try{
            val user = new SecurityUserInfo();
            user.setTenantId(skip.getActualTenantId());
            SecurityUserUtil.setSecurityUserInfo(user);
            List<String> nextNodeIds = panguNodeFeign.fetchNextNodes(skip.getArrangementVid(), skip.getArrangementNodeId()).getData();
            if(nextNodeIds.isEmpty()){
                return;
            }
            List<ArrangementNodeExec> existedTask = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(skip.getExecSeqId(), nextNodeIds);
            nextNodeIds.forEach(next -> {
                val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                if(!matched.isPresent()){
                    initNext(skip, next, true, ArrangementNodeExec.Status.CHECK);
                }
            });
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }



    }

    @Transactional
    public void startNode(ArrangementNodeExec nodeExec) {
        log.info("try to start node:");
        nodeManagerService.applyUtilSuccess((node)->{
            log.info("start nodeExec:"+ FastjsonUtil.toJson(node));
            val address = new Address();
            address.setHost(node.getHost());
            address.setPort(Integer.valueOf(node.getPort()));
            nodeExec.setStarted(System.currentTimeMillis());
            nodeExec.setStatus(ArrangementNodeExec.Status.STARTED);
            nodeExec.setHost(address.getHost());
            nodeExec.setPort(address.getPort());
            nodeExec.setPanguNodeId(node.getId());
            nodeExec.update();
            log.info("update nodeExec:");
            val execDto = new NodeExec();
            execDto.setArrangementNodeId(nodeExec.getArrangementNodeId());
            execDto.setArrangementVid(nodeExec.getArrangementVid());
            execDto.setExecSeqId(nodeExec.getExecSeqId());
            execDto.setExecNodeSeqId(nodeExec.getExecNodeSeqId());
            execDto.setContext(nodeExec.getContext());
            execDto.setSkip(nodeExec.isSkip());
            execDto.setTenantId(nodeExec.getActualTenantId());
            arrangementClient.startNode(address, execDto);
            log.info("update nodeExec:");
        });
    }

    @Transactional
    public void initNext(ArrangementNodeExec previous, String nextNode, boolean skip, ArrangementNodeExec.Status status){
        ArrangementNodeExec nodeExec = new ArrangementNodeExec();
        nodeExec.setArrangementId(previous.getArrangementId());
        nodeExec.setArrangementVid(previous.getArrangementVid());
        nodeExec.setExecSeqId(previous.getExecSeqId());
        nodeExec.setExecNodeSeqId(SnowUtil.nextId());
        nodeExec.setStatus(status);
        nodeExec.setSkip(skip);
        nodeExec.setContext(previous.getContext());
        nodeExec.setArrangementNodeId(nextNode);
        nodeExec.setWaitFrom(System.currentTimeMillis());
        nodeExec.setActualTenantId(previous.getActualTenantId());
        nodeExec.create();
    }


    public String getResultCacheId(String execSeqId, String nodeId) {
        val exec = ArrangementNodeExec
                .listByExecSeqIdAndArrangementNodeIdList(execSeqId, Lists.list(nodeId)).stream()
                .filter(it->it.getStatus().equals(ArrangementNodeExec.Status.SUCCESS)).findAny()
                .orElseThrow(()->new ServerException("node exec not exist"));
        return exec.getResultCacheId();
    }

    @SneakyThrows
    public void redispatch(){
        val each = dispatchTool.getInvalidNodeContext().getCache()
                .iterator();
        val dispatchContext = dispatchTool.getDispatchContext();
        while (each.hasNext()) {
            val invalidNodeId = each.next().getKey();
            List<ArrangementNodeExec> redispatch = ArrangementNodeExec.listByPanguNodeIdAndStatus(invalidNodeId, ArrangementNodeExec.Status.STARTED);
            redispatch.forEach(it->{
                it.setStatus(ArrangementNodeExec.Status.LOADED);
                it.update();
            });
            val dispatchedList =
                    dispatchContext.loadProperties(DataFilter.eq("dispatchNodeId", invalidNodeId).andEq("status", "DISPATCHED"), Lists.list("execNodeSeqId", "from"), Lists.list(), 0 , -1);
            for(List<KeyValueDto> dispatched : dispatchedList){
                val execNodeSeqId = (String)dispatched.get(1).getValue();
                val from = (String)dispatched.get(2).getValue().toString();
                val nodeExec = ArrangementNodeExec.loadByExecNodeSeqId(execNodeSeqId).get();
                dispatchContext.getCache().remove(execNodeSeqId + "-" + from);
                nodeExec.setStatus(ArrangementNodeExec.Status.LOADED);
                nodeExec.update();
            }
            each.remove();
        }
        Thread.sleep(5000);
    }

    public PageResult<ArrangementExec> execLogs(String keywords, String arrangementId, ArrangementType type, ArrangementExec.Status status,
                                                Long start, Long end, int pageNo, int pageSize) {
        return ArrangementExec.execLogs(keywords, arrangementId, type, status, start, end, pageNo, pageSize);
    }

    public List<ArrangementNodeExec> execNodesLogs(String execSeqId) {
        val result = ArrangementNodeExec.listByExecSeqId(execSeqId).stream()
                .filter(it-> !ArrangementNodeExec.Status.SKIPPED.equals(it.getStatus()) &&
                        !ArrangementNodeExec.Status.WAITING.equals(it.getStatus()) &&
                        !ArrangementNodeExec.Status.CHECK.equals(it.getStatus()))
                .sorted(Comparator.comparing(it->it.getStarted())).collect(Collectors.toList());
        if(!result.isEmpty()){
            val arrangementVid = result.get(0).getArrangementVid();
            Map<String, List<String>> names = panguNodeFeign.fetchAllNodeNames(arrangementVid).getData();
            result.forEach(it->{
                it.setArrangementNodeName(names.get(it.getArrangementNodeId()).get(0));
                it.setArrangementNodeType(names.get(it.getArrangementNodeId()).get(1));
                if(ArrangementNodeExec.Status.LOADED.equals(it.getStatus()) || ArrangementNodeExec.Status.DISPATCHED.equals(it.getStatus())){
                    it.setStatus(ArrangementNodeExec.Status.STARTED);
                }
            });
        }
        return result;
    }
}

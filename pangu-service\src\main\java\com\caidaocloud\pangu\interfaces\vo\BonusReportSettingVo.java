package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("奖金报表设置")
@AllArgsConstructor
public class BonusReportSettingVo {
    @ApiModelProperty("基本信息")
    public List<MetadataPropertyVo> basic;

    @ApiModelProperty("奖金项")
    public List<BonusStructItemVo> item;
}

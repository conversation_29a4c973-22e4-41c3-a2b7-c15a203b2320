package com.caidaocloud.pangu.application.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.caidaocloud.pangu.application.dto.compose.ArrangementDetailDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementNodeExecDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementProgressDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementQueryDto;
import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.feign.ComposeFeign;
import com.caidaocloud.pangu.application.feign.ComposeExecFeign;
import com.caidaocloud.web.Result;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

/**
 * 编排服务
 * 
 * <AUTHOR>
 * @date 2025/2/10
 */
@Slf4j
@Service
public class ArrangementService {

    @Autowired
    private ComposeFeign composeFeign;

    @Autowired
    private ComposeExecFeign composeExecFeign;

    /**
     * 获取编排执行进度信息
     * 
     * @param execId        执行ID
     * @param arrangementId 编排ID
     * @return 包含进度信息的DTO
     */
    public ArrangementProgressDto getArrangementProgress(String execId, String arrangementId) {
        List<ArrangementNodeExecDto> nodeExecList = loadNodeExecList(execId);
        log.info("node exec result,result={}",nodeExecList);

        int totalNodes = 0;
        int completedNodes = 0;

        // node未执行
        if (nodeExecList == null || nodeExecList.isEmpty()) {
            Result<ComposeDefDto> arrangementResult = composeFeign.loadPublished(arrangementId);
            if (!arrangementResult.isSuccess() || arrangementResult.getData() == null) {
                log.error("Failed to get arrangement detail for arrangementId: {}", arrangementId);
                return ArrangementProgressDto.builder()
                        .totalNodes(0)
                        .completedNodes(0)
                        .build();
            }

            // 获取编排详情
            Result<ArrangementDetailDto> detailResult = composeFeign.loadDetailPublished(arrangementId);

            if (detailResult.isSuccess() && detailResult.getData() != null ) {
                ArrangementDetailDto detail = detailResult.getData();
                totalNodes = detail.getNodes().size();
            } else {
                log.error("Failed to get arrangement detail for arrangementId: {}", arrangementId);
                totalNodes = 0;
            }

            completedNodes = 0;
        } else {
            // 若list不为空，使用arrangementVid获取编排详情，获取node总数
            String arrangementVid = nodeExecList.get(0).getArrangementVid();

            ArrangementQueryDto queryDto = new ArrangementQueryDto();
            queryDto.setArrangementVerId(arrangementVid);
            Result<ArrangementDetailDto> detailResult = composeFeign.loadDetailVersion(queryDto);

            if (detailResult.isSuccess() && detailResult.getData() != null) {
                ArrangementDetailDto detail = detailResult.getData();
                totalNodes = detail.getNodes().size();
            } else {
                log.error("Failed to get arrangement detail for arrangementVid: {}", arrangementVid);
                totalNodes = nodeExecList.size(); // 使用执行列表的大小作为备选
            }

            // 计算已完成的节点数
            completedNodes = (int) nodeExecList.stream()
                    .filter(node -> "SUCCESS".equals(node.getStatus()) || "SKIPPED".equals(node.getStatus()))
                    .count();
        }


        // 构建并返回DTO
        return ArrangementProgressDto.builder()
                .totalNodes(totalNodes)
                .completedNodes(completedNodes)
                .build();
    }

    public List<ArrangementNodeExecDto> loadNodeExecList(String execId) {
        // 调用manager接口获取nodeExecList
        Result<List<ArrangementNodeExecDto>> execNodesResult = composeExecFeign.execNodesLogs(execId);
        List<ArrangementNodeExecDto> nodeExecList = execNodesResult.getData();
        return nodeExecList;
    }

    public ArrangementProgressDto loadTotal(String execId) {
        List<ArrangementNodeExecDto> data = composeExecFeign.execNodesLogs(execId).getData();
        if (CollectionUtils.isEmpty(data)) {
            log.warn("node exec is empty");
            return ArrangementProgressDto.builder().totalNodes(0).completedNodes(0).build();
        }
        String arrangementVid = data.get(0).getArrangementVid();
        ArrangementQueryDto dto = new ArrangementQueryDto();
        dto.setArrangementVerId(arrangementVid);
        ArrangementDetailDto arrangementDetailDto = composeFeign.loadDetailVersion(dto).getData();
        return ArrangementProgressDto.builder().totalNodes(arrangementDetailDto.getNodes().size())
                .completedNodes(arrangementDetailDto.getNodes().size()).build();

    }

    public List<ArrangementDetailDto> loadArrangementList(List<String> arrangementIds) {

        ArrangementQueryDto dto = new ArrangementQueryDto();
        dto.setArrangementIds(arrangementIds);
        return composeFeign.loadDetailPublishedList(dto).getData();
    }
}

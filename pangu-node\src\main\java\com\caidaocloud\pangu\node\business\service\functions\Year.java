package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDateTime;
import java.util.Map;

public class Year extends AbstractFunction {
    @Override
    public String getName() {
        return "YEAR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1) {
        LocalDateTime time = AviatorTimestamp.transformAviatorValue(aviatorObject1,env);
        val result = time.getYear();
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(time).append(")=").append(result).toString());
        }
        return new AviatorDecimal(result);
    }
}

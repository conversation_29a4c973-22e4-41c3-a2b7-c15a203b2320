package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("奖金报表设置-可选项")
@AllArgsConstructor
public class BonusReportSettingAllVo {
    @ApiModelProperty("任职信息")
    public BonusMetadataVo workInfo;

    @ApiModelProperty("个人信息")
    public BonusMetadataVo privateInfo;


    @ApiModelProperty("奖金项")
    public List<BonusStructItemVo> item;
}

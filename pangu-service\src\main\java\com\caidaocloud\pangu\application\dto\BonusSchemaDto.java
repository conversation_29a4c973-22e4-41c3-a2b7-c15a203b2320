package com.caidaocloud.pangu.application.dto;

import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.domain.entity.PeriodDate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("奖金方案dto")
public class BonusSchemaDto {

    private String bid;

    private String name;

    private String bonusStructBid;

    private ConditionTree coverage;

    private Integer month;

    private Integer day;

    private String remark;

    private ApproveConfigDto approveConfig;


    @ApiModelProperty("选人周期")
    private PeriodDate selection;

    @ApiModelProperty("特殊条件")
    private Boolean special;

    @ApiModelProperty("入职日期")
    private PeriodDate hireDate;

    @ApiModelProperty("离职日期")
    private PeriodDate terminationDate;

}

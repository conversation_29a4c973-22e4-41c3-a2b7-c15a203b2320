package com.caidaocloud.pangu.core.ignite.store;

import java.util.Map;

import javax.cache.Cache;
import javax.cache.integration.CacheLoaderException;
import javax.cache.integration.CacheWriterException;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.cache.store.CacheStoreSession;
import org.apache.ignite.lang.IgniteBiInClosure;
import org.apache.ignite.resources.CacheStoreSessionResource;

@Slf4j
public class CachePaasMapStore extends CachePaasStore<String, Map> {

	@CacheStoreSessionResource
	private CacheStoreSession ses;
	@Override
	public Map load(String key) throws CacheLoaderException {
		log.info("loading paas data,key:{}", key);
		try (CacheSession cacheSession = session()) {
			DataSimple data = DataQuery.identifier(getIdentifier()).oneOrNull(key, DataSimple.class);
			NestPropertyValue properties = data.getProperties();
			// Map<String, Object> map = new HashMap<>();
			// map.put("bid", key);
			// map.put("name", ((SimplePropertyValue) properties.get("name")).getValue());
			return properties;
		}
	}

	@Override
	public void loadCache(IgniteBiInClosure<String, Map> clo, Object... args) {
		try (CacheSession cacheSession = session()) {

			DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString());
			if (args != null && args.length > 0) {
				DataFilter filter = (DataFilter) args[0];
				dataFilter = dataFilter.and(filter);
			}
			for (DataSimple item : DataQuery.identifier(getIdentifier()).limit(-1, 1)
					.filter(dataFilter, DataSimple.class).getItems()) {
				clo.apply(item.getBid(), item.getProperties());
			}
		}

	}

	@Override
	public void write(Cache.Entry<? extends String, ? extends Map> entry) throws CacheWriterException {
		// String bid = entry.getKey();
	}

	public CacheSession session() {
		return new CacheSession(getTenantId());
	}

	@Override
	public void delete(Object key) throws CacheWriterException {
		try (CacheSession cacheSession = session()) {
			String bid = (String) key;
			DataDelete.identifier(getIdentifier()).softDelete(bid);
		}
	}

	private class CacheSession implements AutoCloseable {
		private String tenantId;

		CacheSession(String tenantId) {
			this.tenantId = tenantId;
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(tenantId);
			userInfo.setEmpId(0L);
			userInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);
		}

		@Override
		public void close() {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

}
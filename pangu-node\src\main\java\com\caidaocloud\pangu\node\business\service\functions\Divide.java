package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class Divide extends AbstractFunction {
    @Override
    public String getName() {
        return "DIVIDE";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val first = FunctionUtils.getNumberValue(aviatorObject1, env);
        val second= FunctionUtils.getNumberValue(aviatorObject2, env);
        BigDecimal result = new BigDecimal(first.toString())
                .divide(new BigDecimal(second.toString()), 6, RoundingMode.HALF_UP);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(first).append(",").append(second).append(")=").append(result).toString());
        }

        return new AviatorDecimal(result);
    }

}

package com.caidaocloud.pangu.node.business.service;

import com.caidaocloud.compute.remote.framework.actor.RemoteHandler;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.match.*;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.dto.NodeExec;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.core.ignite.KeyValueDto;
import com.caidaocloud.pangu.node.business.enums.EmpKeyType;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.service.load.PreLoadDataInterface;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.caidaocloud.pangu.node.common.feign.PanguManagerFeign;
import com.caidaocloud.pangu.node.common.register.ServerRegister;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
@RemoteHandler("arrangementClient")
public class ArrangementNodeService {

    @Autowired
    @Qualifier("taskProcessors")
    private ExecutorService taskProcessors;

    @Autowired
    private PanguManagerFeign panguManagerFeign;

    @Autowired
    private TaskService taskService;

    @Autowired
    private IgniteUtil igniteUtil;

    @Autowired
    private MetadataOperatorService metadataOperatorService;

    @Autowired
    private CalcService calcService;

    @RemoteHandler
    public boolean startNode(NodeExec exec){
        taskProcessors.submit(()->{
            try{
                TaskProcessorConfiguration.availableProcessCount.incrementAndGet();
                val user = new SecurityUserInfo();
                user.setTenantId(exec.getTenantId());
                SecurityUserUtil.setSecurityUserInfo(user);
                val detail = Arrangement.Detail.loadVersion(exec.getArrangementVid());
                val arrangementNode = detail.getNodes().stream().filter(it->exec.getArrangementNodeId().equals(it.getId()))
                        .findAny().orElseThrow(()->new ServerException("node not exist"));
                Thread.sleep(5000);//report after manager commit transaction
                switch (arrangementNode.getType()){
                    case START:{
                        val report = new CalcReport.Start();
                        report.setExecNodeSeqId(exec.getExecNodeSeqId());
                        report.setNextNodeIds(arrangementNode.getNextNodeId());
                        report.setExecSeqId(exec.getExecSeqId());
                        report(report);
                        return;
                    }
                    case TASK:{
                        val taskDefId = arrangementNode.getTaskDefId();
                        val taskDef = TaskDef.Detail.loadVersion(taskDefId, exec.getArrangementVid());
                        val rules = TaskDef.Rule.listByTaskVersion(taskDef.getTaskDefVid());
                        String cacheId = SnowUtil.nextId();
                        String resultCacheId = "r" + SnowUtil.nextId();
                        log.info("Task pre load data start,seqId={},cacheId={},taskDef={},context={},node={}", exec.getExecSeqId(), cacheId, taskDef, exec.getContext(), detail.getNodes());
                        val igniteContext = taskService.preLoadData(exec.getExecSeqId(), cacheId, taskDef, exec.getContext(), detail.getNodes());



                        if(taskDef.isEmpKeyOpen()){
                            long time = 0l;
                            if(null != exec.getContext().get("$.env."+ EnvironmentContext.BONUS_CALC_END)){
                                time = Long.valueOf(exec.getContext().get("$.env."+ EnvironmentContext.BONUS_CALC_END));
                            }
                            taskService.preLoadEmpData(cacheId, time);
                        }
                        taskService.initResultCache(resultCacheId, rules,taskDef.isGroup());
                        val report = new CalcReport.TaskDataLoaded();
                        report.setExecNodeSeqId(exec.getExecNodeSeqId());
                        long total;
                        int step = 1;
                        if(taskDef.isGroup()){
                            total = TaskDef.Rule.listByTaskVersion(taskDef.getTaskDefVid()).size();
                        }else{
                            total = igniteContext.count(null, "1");
                            step = 20;
                        }
                        report.setTotal(total);
                        report.setStep(step);
                        report.setCacheId(cacheId);
                        report.setResultCacheId(resultCacheId);
                        report.setExecSeqId(exec.getExecSeqId());
                        log.info("Node task report,report={}", report);
                        if(!report(report)){
                            taskService.removePreLoadData(cacheId, taskDef.isEmpKeyOpen());
                            igniteUtil.getContext(resultCacheId).destroy();
                        }
                        break;
                    }
                    case CONDITION:{
                        val conditionNextNodeIds = conditionNextNodeIds(arrangementNode.getConditionToNextNodeId(), exec.getContext(), arrangementNode.getNextNodeId());
                        val report = new CalcReport.Condition();
                        report.setExecNodeSeqId(exec.getExecNodeSeqId());
                        report.setSkipNextNodes(conditionNextNodeIds.first());
                        report.setNotSkipNextNodes(conditionNextNodeIds.second());
                        report.setExecSeqId(exec.getExecSeqId());
                        report(report);
                        return;
                    }
                    case END:{
                        val report = new CalcReport.End();
                        report.setExecNodeSeqId(exec.getExecNodeSeqId());
                        report.setExecSeqId(exec.getExecSeqId());
                        report(report);
                        return;
                    }
                    case DATA_PRE_LOAD:{
                        val report = new CalcReport.PreLoad();
                        PreLoadDataInterface.implementations.get(arrangementNode.getPreLoadData())
                                .exec(exec.getContext());
                        report.setExecNodeSeqId(exec.getExecNodeSeqId());
                        report.setNextNodeIds(arrangementNode.getNextNodeId());
                        report.setExecSeqId(exec.getExecSeqId());
                        report(report);
                        return;
                    }
                }
            }catch (Throwable e){
                log.error("task exec error ", e);
                val report = new CalcReport.Fail();
                report.setExecSeqId(exec.getExecSeqId());
                report.setExecNodeSeqId(exec.getExecNodeSeqId());
                report.setExecSeqId(exec.getExecSeqId());

                val error = new StringWriter();
                val output = new PrintWriter(error);
                e.printStackTrace(output);
                report.setDetail(StringUtils.substring(error.toString(), 0, 5000));


                report(report);
                return;
            }finally {
                TaskProcessorConfiguration.availableProcessCount.decrementAndGet();
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        return true;
    }

    @RemoteHandler
    public boolean dispatchSubTask(NodeExec.SubNodeExec execDto){
        taskProcessors.submit(()->{
            try{
                log.info("Exec task running,dto={}",execDto);
                val user = new SecurityUserInfo();
                user.setTenantId(execDto.getTenantId());
                SecurityUserUtil.setSecurityUserInfo(user);
                val report = new CalcReport.SubTask();
                report.setExecNodeSeqId(execDto.getExecNodeSeqId());
                val detail = Arrangement.Detail.loadVersion(execDto.getArrangementVid());
                val node = detail.getNodes().stream().filter(it->it.getId().equals(execDto.getArrangementNodeId()))
                        .findFirst().get();
                TaskDef.Detail taskDefDetail = TaskDef.Detail.loadVersion(node.getTaskDefId(), execDto.getArrangementVid());
                val group = taskDefDetail.isGroup();
                val ruleList = TaskDef.Rule.listByTaskVersion(taskDefDetail.getTaskDefVid());
                Map<String, String> context = Maps.map();
                context.putAll(execDto.getContext());
                context.put("cacheId", execDto.getCacheId());
                if(group){
                    calcService.calcGroup(execDto, ruleList, context, taskDefDetail);
                }else{
                    calcService.calc(execDto, ruleList, context, taskDefDetail);
                }
                report.setFrom(execDto.getFrom());
                report.setTo(execDto.getTo());
                report.setExecSeqId(execDto.getExecSeqId());
                report(report);
            }catch(Throwable e) {
                log.error("sub task exec error ", e);
                val report = new CalcReport.Fail();
                report.setExecSeqId(execDto.getExecSeqId());
                report.setExecNodeSeqId(execDto.getExecNodeSeqId());
                report.setExecSeqId(execDto.getExecSeqId());
                val error = new StringWriter();
                val output = new PrintWriter(error);
                e.printStackTrace(output);
                report.setDetail(StringUtils.substring(error.toString(), 0, 5000));
                report(report);
            }finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        return true;
    }



    @SneakyThrows
    private boolean report(CalcReport report){
        while(StringUtils.isEmpty(ServerRegister.registerId)){
            Thread.sleep(500);
        }
        report.setPanguNodeId(ServerRegister.registerId);
        while(true){
            try {
                if(report instanceof CalcReport.Start){
                    return panguManagerFeign.report((CalcReport.Start)report).getData();
                }
                if(report instanceof CalcReport.End){
                    return panguManagerFeign.report((CalcReport.End)report).getData();
                }
                if(report instanceof CalcReport.Condition){
                    return panguManagerFeign.report((CalcReport.Condition)report).getData();
                }
                if(report instanceof CalcReport.TaskDataLoaded){
                    return panguManagerFeign.report((CalcReport.TaskDataLoaded)report).getData();
                }
                if(report instanceof CalcReport.SubTask){
                    return panguManagerFeign.report((CalcReport.SubTask)report).getData();
                }
                if(report instanceof CalcReport.Fail){
                    return panguManagerFeign.report((CalcReport.Fail)report).getData();
                }
                if(report instanceof CalcReport.PreLoad){
                    return panguManagerFeign.report((CalcReport.PreLoad)report).getData();
                }
            } catch (Throwable e) {
                log.error("report error " + FastjsonUtil.toJson(report), e);
                continue;
            }
            return false;
        }
    }

    private Pair<List<String>, List<String>> conditionNextNodeIds(List<Arrangement.NodeCondition> conditions, Map<String, String> context, List<String> defaultNextNodeIds){
        List<String> skipNextNodes = Lists.list();
        List<String> notSkipNextNodes = Lists.list();
        boolean matchedBefore = false;
        for (Arrangement.NodeCondition nodeCondition : conditions) {
            if (matchedBefore) {
                skipNextNodes.addAll(nodeCondition.getNextNodeId());
            } else {
                val condition = nodeCondition.getCondition();
                boolean matched;
                if(ConditionNodeRelationEnum.and.equals(condition.getRelation())){
                    matched = condition.getChildren().stream().allMatch(it->match(it, context));
                }else{
                    matched = condition.getChildren().stream().anyMatch(it->match(it, context));
                }
                if(matched){
                    notSkipNextNodes.addAll(nodeCondition.getNextNodeId());
                    matchedBefore = true;
                }else{
                    skipNextNodes.addAll(nodeCondition.getNextNodeId());
                }
            }
        }
        if(notSkipNextNodes.isEmpty()){
            notSkipNextNodes.addAll(defaultNextNodeIds);
        }else{
            skipNextNodes.addAll(defaultNextNodeIds);
        }
        return Pair.pair(skipNextNodes, notSkipNextNodes);
    }

    private boolean match(ConditionNode conditionNode, Map<String, String> context){
        if(ConditionNodeTypeEnum.group.equals(conditionNode.getType())){
            if(ConditionNodeRelationEnum.and.equals(conditionNode.getRelation())){
                return conditionNode.getChildren().stream().allMatch(it->match(it, context));
            }else{
                return conditionNode.getChildren().stream().anyMatch(it->match(it, context));
            }
        }else{
            val conditionExp = conditionNode.getCondition();
            val contextValue = context.get(conditionExp.getName());
            val symbol = conditionExp.getSymbol();
            val compareValue = conditionExp.getSimpleValue();
            if(ConditionOperatorEnum.EQ.equals(symbol)){
                return StringUtils.equals(contextValue, compareValue);
            }else if(ConditionOperatorEnum.NE.equals(symbol)){
                return !StringUtils.equals(contextValue, compareValue);
            }else if(ConditionOperatorEnum.GT.equals(symbol)){
                return StringUtils.trimToEmpty(contextValue).compareTo(StringUtils.trimToEmpty(contextValue)) > 0;
            }else if(ConditionOperatorEnum.GE.equals(symbol)){
                return StringUtils.trimToEmpty(contextValue).compareTo(StringUtils.trimToEmpty(contextValue)) >= 0;
            }else if(ConditionOperatorEnum.LT.equals(symbol)){
                return StringUtils.trimToEmpty(contextValue).compareTo(StringUtils.trimToEmpty(contextValue)) < 0;
            }else if(ConditionOperatorEnum.LE.equals(symbol)){
                return StringUtils.trimToEmpty(contextValue).compareTo(StringUtils.trimToEmpty(contextValue)) <= 0;
            }else{
                throw new ServerException("unsupported comparator");
            }
        }
    }

    public static void main(String[] args) {

        try{
            throw new ServerException("ttt");
        }catch(Exception e){
            val string = new StringWriter();
            val output = new PrintWriter(string);
            e.printStackTrace(output);
            System.out.println(string.toString());
        }


    }



}

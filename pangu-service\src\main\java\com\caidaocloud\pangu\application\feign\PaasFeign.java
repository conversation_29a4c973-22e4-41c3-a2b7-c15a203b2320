package com.caidaocloud.pangu.application.feign;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}", fallback = PaasFeignFallback.class, configuration = FeignConfiguration.class, contextId = "paasFeign")
public interface PaasFeign {

    @PostMapping("/api/${service.name.short:hrpaas}/v1/metadata")
    Result initEntity(@RequestBody MetadataDto metadata);


}

package com.caidaocloud.pangu.node.common.feign;

import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.dto.NodeSyncStatus;
import com.caidaocloud.pangu.core.dto.RegisterResult;
import com.caidaocloud.pangu.core.dto.ServerInstance;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class PanguManagerFeignFallback implements PanguManagerFeign{

    @Override
    public Result<RegisterResult> register(ServerInstance serverInstance) {
        return Result.fail();
    }


    @Override
    public Result<NodeSyncStatus> heartBeat(ServerInstance serverInstance) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.Start report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.End report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.Condition report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.TaskDataLoaded report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.SubTask report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.Fail report) {
        return Result.fail();
    }

    @Override
    public Result<Boolean> report(CalcReport.PreLoad report) {
        return Result.fail();
    }

    @Override
    public Result<String> getResultCacheId(String execSeqId, String nodeId) {
        return Result.fail();
    }
}

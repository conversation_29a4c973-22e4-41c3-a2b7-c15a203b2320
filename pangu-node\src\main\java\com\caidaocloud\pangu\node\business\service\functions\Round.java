package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class Round extends AbstractFunction {
    @Override
    public String getName() {
        return "ROUND";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject, AviatorObject roundingMode, AviatorObject precise) {
        val value = FunctionUtils.getNumberValue(aviatorObject, env).toString();
        val precision= FunctionUtils.getNumberValue(precise, env).intValue();
        val round = FunctionUtils.getStringValue(roundingMode, env);
        BigDecimal result = new BigDecimal(value)
                .setScale(precision, RoundingMode.valueOf(round));
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(value)
                    .append(",").append(round).append(",").append(precision).append(")=").append(result).toString());
        }
        return new AviatorDecimal(result);
    }

}

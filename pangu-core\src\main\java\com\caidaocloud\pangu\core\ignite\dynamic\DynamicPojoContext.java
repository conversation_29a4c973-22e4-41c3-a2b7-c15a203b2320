package com.caidaocloud.pangu.core.ignite.dynamic;

import java.lang.reflect.Modifier;
import java.util.Map;

import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.implementation.FieldAccessor;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public class DynamicPojoContext {

	private static Map<String, Class<?>> pojoClassContainer = Maps.newConcurrentMap();

	public static Class<?> loadClass(String name) {
		 return pojoClassContainer.get(name);
	}

	@SneakyThrows
	public static Class<?> loadClass(DynamicPojoDefinition dpd){
		// TODO: 2025/3/25 paas模型字段更新后，卸载旧的类
		if (dpd.getDefType() == DynamicPojoDefinition.DefType.COMMON) {
			return Class.forName(dpd.getClassName());
		}
		return loadClass(dpd.getSuperClass(), dpd.getClassName(), dpd.getFields());
	}
	public static Class<?> loadClass(String name, DynamicPojoDefinition.DynamicFieldDefinition... property) {
		return loadClass(Object.class, name, property);
	}

	@SneakyThrows
	public static Class<?> loadClass(Class<?> superClass, String name, DynamicPojoDefinition.DynamicFieldDefinition... property) {
		if (pojoClassContainer.containsKey(name)) {
			return pojoClassContainer.get(name);
		}
		DynamicType.Builder<?> builder = new ByteBuddy()
				.subclass(superClass)
				.name(name);
		if (property != null && property.length > 0) {
			for (DynamicPojoDefinition.DynamicFieldDefinition p : property) {
				builder = addField(builder, p);
			}
		}

		DynamicType.Unloaded<?> dynamicType = builder.make();
		Class<?> clazz = dynamicType.load(DynamicPojoContext.class.getClassLoader()).getLoaded();
		pojoClassContainer.put(name, clazz);
		return clazz;
	}

	public static synchronized void remove(String name) {
		pojoClassContainer.remove(name);
	}


	private static DynamicType.Builder.MethodDefinition.ReceiverTypeDefinition addField(DynamicType.Builder builder, DynamicPojoDefinition.DynamicFieldDefinition property) {
		String property_ = capitalizeFirstLetter(property.getName());
		String setterMethod = "set" + property_;
		String getterMethod = "get" + property_;
	return	builder.defineField(property.getName(), property.getType())
				.defineMethod(getterMethod, property.getType(), Modifier.PUBLIC)
				.intercept(FieldAccessor.ofBeanProperty())
				.defineMethod(setterMethod, void.class, Modifier.PUBLIC)
				.withParameters(property.getType())
				.intercept(FieldAccessor.ofBeanProperty());

	}

	/**
	 * 将字段名的首字母大写
	 * @param field 字段名
	 * @return 首字母大写后的字符串
	 */
	private static String capitalizeFirstLetter(String field) {
		if (field == null || field.isEmpty()) {
			return field;
		}
		return field.substring(0, 1).toUpperCase() + field.substring(1);
	}
}

package com.caidaocloud.pangu.core.ignite.config;

import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.spi.discovery.tcp.TcpDiscoverySpi;
import org.apache.ignite.spi.discovery.tcp.ipfinder.vm.TcpDiscoveryVmIpFinder;
import org.apache.ignite.springframework.boot.autoconfigure.IgniteConfigurer;

/**
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
public class StaticIpConfigurer implements IgniteConfigurer {
	private final StaticIpConfig staticIpConfig;

	public StaticIpConfigurer(StaticIpConfig staticIpConfig) {
		this.staticIpConfig = staticIpConfig;
	}

	@Override
	public void accept(IgniteConfiguration configuration) {


		if (!staticIpConfig.getIpConfigs().isEmpty()) {
			// 创建 TcpDiscoverySpi 实例
			TcpDiscoverySpi spi = new TcpDiscoverySpi();
			// 配置 IP 查找器（使用配置文件中的 IP 列表）
			TcpDiscoveryVmIpFinder ipFinder = new TcpDiscoveryVmIpFinder();

			// 从配置中获取 IP 地址和端口范围
			List<String> addresses = staticIpConfig.getIpConfigs().stream()
					.map(ipConfig -> ipConfig.getIp() + ":" + ipConfig.getBasePort() + ".." + ipConfig.getEndPort())
					.collect(Collectors.toList());

			ipFinder.setAddresses(addresses);
			spi.setIpFinder(ipFinder);

			// 将配置好的 SPI 设置到 IgniteConfiguration
			configuration.setDiscoverySpi(spi);
		}

	}
}

server:
  port: 8081
ignite:
  peerClassLoadingEnabled: true
  deploymentMode: CONTINUOUS
#  clientMode: true
  igniteInstanceName: bonus-calc
  workDirectory: C:\ignite233
#  dataStorageConfiguration:
#    defaultDataRegionConfiguration:
#      initialSize: ******** #10MB
#    dataRegionConfigurations:
#      - name: my-dataregion
#        initialSize: ********0 #100MB
spring:
  profiles:
    active: dev
#caidaocloud:
#  swagger:
#    open: true
#    base-package:
#      - com.caidaocloud.pangu.interfaces
#
#msg:
#  middleware:
#    type: rabbitmq
#rabbitmq:
#  topics:
#    - topic: BONUS_CALC
#      exchange: caidaocloud.bonus.calc.fac.direct.exchange
#      routingKey: routingKey.bonus.calc
#      queue: queue.bonus.calc
#      exchangeType: DIRECT
#      tenantIsolated: false
#      consumersCount: ${bonus.calc.threads:20}
#    - topic: BONUS_PRE_CALC
#      exchange: caidaocloud.bonus.calc.fac.direct.exchange
#      routingKey: routingKey.bonus.pre.calc
#      queue: queue.bonus.pre.calc
#      exchangeType: DIRECT
#      tenantIsolated: false
#      consumersCount: ${bonus.pre.calc.threads:5}
#
#authScope:
#  /api/pangu/v1/bonus/ledger/emp/page: entity.hr.EmpWorkInfo
#  /api/pangu/v1/bonus/report: entity.hr.EmpWorkInfo
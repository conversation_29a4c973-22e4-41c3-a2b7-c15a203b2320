package com.caidaocloud.pangu.application.event;


import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.util.FastjsonUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class FinalStepExecPublisher {
    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;

    public void publish(FinalStepExecMessageDTO dto) {
        RabbitBaseMessage message = new RabbitBaseMessage();
        message.setExchange(BonusConstant.BONUS_DIRECT_EXCHANGE);
        message.setRoutingKey(BonusConstant.BONUS_FINAL_STEP_EXEC_ROUTINGKEY);
        message.setBody(FastjsonUtil.toJson(dto));
        producer.publish(message);
    }
}
package com.caidaocloud.pangu.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
@ApiModel("奖金账套DTO")
public class BonusLedgerDto {
	private String bid;

	@ApiModelProperty("账套名称")
	private Map<String,String> i18nName;

	@ApiModelProperty("奖金方案id")
	private String schemaId;

	@ApiModelProperty("计算年月")
	private Long month;

	@ApiModelProperty("开始日期")
	private Long startDate;

	@ApiModelProperty("结束日期")
	private Long endDate;

	@ApiModelProperty("选人周期开始日期")
	private Long selectionStartDate;

	@ApiModelProperty("选人周期结束日期")
	private Long selectionEndDate;
}

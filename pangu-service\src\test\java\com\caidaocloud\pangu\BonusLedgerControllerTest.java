package com.caidaocloud.pangu;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.Lock;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.application.dto.BonusAccountEmpDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerExecDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerPageDto;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.application.dto.BonusStructQueryDto;
import com.caidaocloud.pangu.application.feign.ComposeFeign;
import com.caidaocloud.pangu.application.service.BonusLedgerService;
import com.caidaocloud.pangu.application.service.BonusReportService;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerEmp;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.PeriodDate;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import com.caidaocloud.pangu.domain.enums.MonthPeriod;
import com.caidaocloud.pangu.domain.repository.IBonusSchemaRepository;
import com.caidaocloud.pangu.infrastructure.repository.po.BonusSchemaPo;
import com.caidaocloud.pangu.interfaces.BonusLedgerController;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerComposeVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguApplication.class)
public class BonusLedgerControllerTest {

    private MockMvc mockMvc;

    @SpyBean
    private BonusLedgerService bonusLedgerService;
    @SpyBean
    private BonusReportService bonusReportService;

    @SpyBean
    private BonusLedgerController bonusLedgerController;
    @SpyBean
    private Locker locker;

    @Autowired
    private IBonusSchemaRepository bonusSchemaRepository;

    private final String SCHEMA_ID = "2130255977674752";
    private final String LEDGER_ID = "2144299172730880";
    @Before
    public void setUp() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(1715461739550728L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(bonusLedgerController).build();
    }

    @Test
    public void testCreateBonusLedger() throws Exception {
        BonusLedgerDto bonusLedgerDto = new BonusLedgerDto();
        bonusLedgerDto.setSchemaId(SCHEMA_ID);
        bonusLedgerDto.setI18nName(Maps.map("default","new ledger001"));
        bonusLedgerDto.setMonth(202502L);
        bonusLedgerDto.setStartDate(1738339200000L);
        bonusLedgerDto.setEndDate(1740758400000L);


        // when(bonusLedgerService.create(bonusLedgerDto)).thenReturn(response);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/create")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(bonusLedgerDto)))
                .andExpect(status().isOk());
                // .andExpect(content().json("{\"code\":0,\"message\":\"\",\"data\":\"Created Bonus Ledger ID\"}"));

    }

    @Test
    public void testPageBonusLedger() throws Exception {
        BonusLedgerPageDto dto = new BonusLedgerPageDto();
        dto.setPageNo(1);
        dto.setPageSize(10);

        // when(bonusLedgerService.page(dto)).thenReturn(pageResult);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.items[0].bid").exists());

    }

    @Test
    public void testUpdateBonusLedger() throws Exception {
        BonusLedgerDto bonusLedgerDto = new BonusLedgerDto();
        bonusLedgerDto.setBid(LEDGER_ID);
        bonusLedgerDto.setSchemaId(SCHEMA_ID);
        // bonusLedgerDto.setName("update ledger");
        bonusLedgerDto.setMonth(202502L);
        bonusLedgerDto.setStartDate(1738339200000L);
        bonusLedgerDto.setEndDate(1740758400000L);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(bonusLedgerDto)))
                .andExpect(status().isOk());

        // verify(bonusLedgerService, times(1)).update(bonusLedgerDto);
    }

    @Test
    public void testDeleteBonusLedger() throws Exception {
        String bid = LEDGER_ID;

        mockMvc.perform(delete("/api/pangu/v1/bonus/ledger/delete")
                .param("bid", bid))
                .andExpect(status().isOk())
                .andExpect(content().json("{\"code\":0,\"message\":\"\",\"data\":true}"));

    }

    @Test
    public void testLoadEmp() throws Exception {
        AuthScopeFilterUtil.addIdentifiers(Lists.list("entity.hr.EmpWorkInfo"));
        BonusLedgerEmpPageDto dto = new BonusLedgerEmpPageDto();
        dto.setBid(LEDGER_ID);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp/page")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(dto))) // Add JSON content for dto
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.total").value(1));
    }

    @Test
    public void testSyncEmp() throws Exception {
        BonusAccountEmpDto bonusAccountSyncEmpDto = new BonusAccountEmpDto();
        bonusAccountSyncEmpDto.setBid("****************");

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp/sync")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(bonusAccountSyncEmpDto))) // Add JSON content for bonusAccountSyncEmpDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).syncEmp("****************");
    }

    @Test
    public void testAddEmp() throws Exception {
        BonusAccountEmpDto bonusAccountSyncEmpDto = new BonusAccountEmpDto();
        bonusAccountSyncEmpDto.setBid(LEDGER_ID);
        bonusAccountSyncEmpDto.setEmpId(Lists.list("****************","****************"));

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(bonusAccountSyncEmpDto))) // Add JSON content for bonusAccountSyncEmpDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).addEmp(bonusAccountSyncEmpDto);
    }

    @Test
    public void testRemoveEmp() throws Exception {
        BonusLedgerEmpDeleteDto dto = new BonusLedgerEmpDeleteDto();
        dto.setBid(LEDGER_ID);
        dto.setEmpIds(Lists.list("****************","****************"));

        mockMvc.perform(delete("/api/pangu/v1/bonus/ledger/emp")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(dto))) // Add JSON content for dto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).removeEmp(LEDGER_ID,dto.getEmpIds());
    }


    @Test
    public void testLoadComposeList() throws Exception {
        String bid = LEDGER_ID;
        // List<BonusLedgerComposeVo> composeList = Collections.emptyList();
        // when(bonusLedgerService.loadComposeList(bid)).thenReturn(composeList);

        mockMvc.perform(get("/api/pangu/v1/bonus/ledger/compose")
                        .param("bid", bid))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    public void testExec() throws Exception {
        BonusLedgerExecDto execDto = new BonusLedgerExecDto();
        execDto.setLedgerId("testLedgerId");

        Lock mockLock = spy(Lock.class);
        when(locker.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId())).thenReturn(mockLock);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/compose/exec")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{\"ledgerId\":\"testLedgerId\"}")) // Add JSON content for execDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).exec(execDto);
        verify(mockLock, times(1)).lock();
        verify(mockLock, times(1)).unlock();
    }

    @Test
    public void testExecAll() throws Exception {
        BonusLedgerExecDto execDto = new BonusLedgerExecDto();
        execDto.setLedgerId(LEDGER_ID);

        Lock mockLock = mock(Lock.class);
        when(locker.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId())).thenReturn(mockLock);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/compose/exec/all")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(FastjsonUtil.toJson(execDto))) // Add JSON content for execDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).execAll(execDto);
        verify(mockLock, times(1)).lock();
        verify(mockLock, times(1)).unlock();
    }

    @Test
    public void test123(){
        // Fetch all ledgerEmp records based on ledgerId
        BonusLedger ledger = BonusLedger.load("2123212477904896");

        // Initialize counters
        int total = 0;
        int succeed = 0;

        int pageSize = 1000;
        int pageNumber = 1;
        List<BonusLedgerEmp> empList;

        do {
            empList = loadEmp(ledger, pageNumber, pageSize);
            total += empList.size();
            succeed += empList.stream().filter(emp -> BonusLedgerEmpCalcStatus.DONE.code.equals(emp.getStatus().getValue())).count();
            pageNumber++;
        } while (!empList.isEmpty());

        ledger.setTotal(total);
        ledger.setSucceed(succeed);
        ledger.update();
    }

    private List<BonusLedgerEmp> loadEmp(BonusLedger ledger, int pageNumber, int pageSize) {
        BonusLedgerEmpPageDto dto = new BonusLedgerEmpPageDto();
        dto.setPageNo(pageNumber);
        dto.setPageSize(pageSize);
        return ledger.loadEmp(dto).getItems();
    }


    @Test
    public void close(){
        bonusLedgerService.close("2143670950336512");
    }

    @Test
    public void export(){
        BonusReportDto dto = FastjsonUtil.toObject("{\"pageNo\":1,\"pageSize\":10,\"keyword\":\"\",\"structId\":\"2139473605933056\",\"startMonth\":1735660800000,\"endMonth\":1743436799999,\"type\":\"xlsx\",\"fileName\":\"计件工资报表\"}", BonusReportDto.class);
        bonusReportService.exportReport(dto,new MockHttpServletResponse());
    }

    @Test
    public  void po(){
        BonusSchema schema = bonusSchemaRepository.selectById("2130255977674752", BonusSchemaPo.BONUS_SCHEMA_IDENTIFIER);
        schema.setSelection(new PeriodDate(MonthPeriod.CURRENT_MONTH, 1));
        schema.setHireDate(new PeriodDate(MonthPeriod.PREVIOUS_MONTH, 15));
        schema.setTerminationDate(new PeriodDate(MonthPeriod.CURRENT_MONTH, 31));
        schema.setSpecial(true);
        bonusSchemaRepository.updateById(schema);
        BonusSchema actual = bonusSchemaRepository.selectById("2130255977674752", BonusSchemaPo.BONUS_SCHEMA_IDENTIFIER);
        Assert.assertEquals(schema, actual);
    }


}

package com.caidaocloud.pangu.core.ignite;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "caidaocloud.ignite.datasource")
public class DatasourceProperties  implements Serializable {
    private String driverClassName;
    private String jdbcUrl;
    private String username;
    private String password;

    @Override
    public String toString() {
        return "DatasourceProperties{" +
                "driverClassName='" + driverClassName + '\'' +
                ", jdbcUrl='" + jdbcUrl + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
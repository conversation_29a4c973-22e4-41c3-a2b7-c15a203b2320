package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class MatchConditionVo {

    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("code")
    private String code;
    @ApiModelProperty("比较符号")
    private List<ConditionOperatorEnum> operators;
    @ApiModelProperty("组件")
    private ConditionComponentEnum component;
    @ApiModelProperty
    private Map<String, Object> dataSourceParams;
    @ApiModelProperty("枚举类型类型componentValueEnum参数")
    private List<Map<String, Object>> componentValueEnum;

}

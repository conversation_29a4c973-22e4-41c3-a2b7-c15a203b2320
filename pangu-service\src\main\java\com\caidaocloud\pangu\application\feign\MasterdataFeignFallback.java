package com.caidaocloud.pangu.application.feign;

import java.util.List;
import java.util.Map;

import com.caidaocloud.pangu.application.dto.emp.EmpInfoDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;


@Component
public class MasterdataFeignFallback implements MasterdataFeign {

    @Override
    public Result<List<EmpInfoDto>> loadEmpInfo(Map map) {
        return null;
    }
}

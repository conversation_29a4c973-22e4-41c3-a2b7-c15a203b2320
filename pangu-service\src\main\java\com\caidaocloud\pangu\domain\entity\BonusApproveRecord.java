package com.caidaocloud.pangu.domain.entity;

import javax.swing.Spring;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.pangu.application.factory.BonusWorkflowFactory;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
@Data
public class BonusApproveRecord extends DataSimple {
	private  String ledgerId;
	private String approveConfigId;
	private String formId;
	private String formDataId;
	private String businessId;
	@DisplayAsObject
	private BonusApproveSummary summary;

	public static  final   String RECEIPT_IDENTIFIER = "entity.bonus.BonusApproveRecord";

	public BonusApproveRecord(){
		setIdentifier(RECEIPT_IDENTIFIER);
		setTenantId(SecurityUserUtil.getThreadLocalSecurityUserInfo().getTenantId());
	}

	public void create() {
		setCreateTime(System.currentTimeMillis());
		setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setUpdateTime(getCreateTime());
		setUpdateBy(getCreateBy());
		SpringUtil.getBean(IBonusLedgerRepository.class).saveApproveReceipt(this);
	}

	public void revoke() {
		String businessKey = businessId + "_" + BonusWorkflowFactory.createFunCode(ledgerId);
		SpringUtil.getBean(IBonusLedgerRepository.class).revoke(businessKey);
	}
}

package com.caidaocloud.pangu.domain.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.domain.repository.IBonusReportRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 奖金计算员工
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
@NoArgsConstructor
public class BonusReport extends DataSimple {

	/**
	 * 账套id
	 */
	private String ledgerId;

	/**
	 * 奖金结构id
	 */
	private String structId;

	/**
	 * 奖金方案id
	 */
	private String schemaId;

	/**
	 * 计算年月
	 */
	private Long month;

	/**
	 * 员工id
	 */
	private String empId;
	/**
	 *	奖金项
 	 */
	@DisplayAsArray
	private List<BonusItem> item = Lists.list();

	public static final String BONUS_REPORT_IDENTIFIER = "entity.bonus.BonusReportEmp";

	public static PageResult<BonusReport> loadReport(String ledgerId, BonusReportDto dto) {
		return SpringUtil.getBean(IBonusReportRepository.class).selectPageByLedgerId(ledgerId, dto);
	}

	public static PageResult<BonusReport> loadReport(String structId, String startMonth, String endMonth, BonusReportDto dto) {
		return SpringUtil.getBean(IBonusReportRepository.class)
				.selectPageByStructId(structId, startMonth, endMonth, dto);
	}

	public BonusReport(String ledgerId, String structId, Long month, String empId) {
		this.ledgerId = ledgerId;
		this.structId = structId;
		this.month = month;
		this.empId = empId;
		this.item = new ArrayList<>();

		setUpdateTime(System.currentTimeMillis());
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setCreateTime(getUpdateTime());
		setCreateBy(getUpdateBy());
		setIdentifier(BONUS_REPORT_IDENTIFIER);
	}

	public BonusReport(String ledgerId, String structId, String schemaId, Long month, String empId) {
		this(ledgerId, structId, month, empId);
		this.schemaId = schemaId;
	}

	public void addItem(String id, String value) {
		BonusItem item = new BonusItem();
		item.setBid(id);
		item.setValue(value);
		this.item.add(item);
	}

	@Data
	public static class BonusItem{
		private String bid;
		private String value;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		if (!super.equals(o)) return false;
		BonusReport that = (BonusReport) o;
		return Objects.equals(ledgerId, that.ledgerId) && Objects.equals(empId, that.empId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), ledgerId, empId);
	}
}

package com.caidaocloud.pangu.node.business.controller;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.dto.ExpAvailableParamDto;
import com.caidaocloud.pangu.node.business.dto.SourceTaskDefDto;
import com.caidaocloud.pangu.node.business.dto.TaskDefDto;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.service.TaskDefService;
import com.caidaocloud.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api/arrangement/v1/taskDef")
public class TaskDefController {

    @Autowired
    private TaskDefService taskDefService;

    @PostMapping(value = "/create")
    public Result<String> create(@RequestBody TaskDefDto.Create create){
        return Result.ok(taskDefService.create(create.getArrangementId()));
    }

    @PostMapping(value = "/update")
    public Result<Boolean> update(@RequestBody TaskDefDto taskDef){
        taskDefService.update(taskDef);
        return Result.ok();
    }

    @PostMapping(value = "/updateDetail")
    public Result<Boolean> updateDetail(@RequestBody TaskDefDto.Detail detail){
        taskDefService.updateDetail(detail);
        return Result.ok();
    }

    @GetMapping(value = "/source/list/draft")
    public Result<List<SourceTaskDefDto>> listDraftSourceTask(@RequestParam String arrangementId){
        return Result.ok(taskDefService.listDraftSourceTask(arrangementId));
    }

    @GetMapping(value = "/source/list/published")
    public Result<List<SourceTaskDefDto>> listPublishedSourceTask(@RequestParam String arrangementId){
        return Result.ok(taskDefService.listPublishedSourceTask(arrangementId));
    }

    @GetMapping(value = "/list/draft")
    public Result<List<TaskDef>> listDraft(@RequestParam String arrangementId){
        return Result.ok(taskDefService.listDraft(arrangementId));
    }

    @GetMapping(value = "/list/published")
    public Result<List<TaskDef>> listPublished(@RequestParam String arrangementId){
        return Result.ok(taskDefService.listPublished(arrangementId));
    }

    @GetMapping(value = "/key/input/available/published")
    public Result<List<KeyValue>> availableInputKeyPublished(@RequestParam String arrangementId,
                                                             @RequestParam TaskDef.SourceType dsType,
                                                             @RequestParam String source){
        return Result.ok(taskDefService.availableInputKeyPublished(arrangementId, dsType, source));
    }

    @GetMapping(value = "/key/input/available/draft")
    public Result<List<KeyValue>> availableInputKeyDraft(@RequestParam String arrangementId,
                                                         @RequestParam TaskDef.SourceType dsType,
                                                         @RequestParam String source){
        return Result.ok(taskDefService.availableInputKeyDraft(arrangementId, dsType, source));
    }

    @GetMapping(value = "/detail/published")
    public Result<TaskDef.Detail> loadDetailPublished(@RequestParam String taskDefId){
        return Result.ok(taskDefService.loadDetailPublished(taskDefId));
    }

    @GetMapping(value = "/detail/draft")
    public Result<TaskDef.Detail> loadDetailDraft(@RequestParam String taskDefId){
        return Result.ok(taskDefService.loadDetailDraft(taskDefId));
    }

    @PostMapping(value = "/rule/create")
    public Result<String> addRule(@RequestBody TaskDefDto.Rule ruleDto){
        return Result.ok(taskDefService.addRule(ruleDto));
    }

    @PostMapping(value = "/rule/update")
    public Result<Boolean> updateRule(@RequestBody TaskDefDto.Rule ruleDto){
        taskDefService.updateRule(ruleDto);
        return Result.ok();
    }

    @GetMapping(value = "/rule/page/draft")
    public Result<PageResult<TaskDef.Rule>> listRuleDraft(
            @RequestParam String taskDefId,
            @RequestParam int pageSize, @RequestParam int pageNo){
        return Result.ok(taskDefService.pageRuleDraft(taskDefId, pageSize, pageNo));
    }

    @GetMapping(value = "/rule/page/published")
    public Result<PageResult<TaskDef.Rule>> listRulePublished(
            @RequestParam String taskDefId,
            @RequestParam int pageSize, @RequestParam int pageNo){
        return Result.ok(taskDefService.pageRulePublished(taskDefId, pageSize, pageNo));
    }

    @GetMapping(value = "/rule/params/available")
    public Result<Map<ExpAvailableParamDto.ParamType, List<ExpAvailableParamDto>>> listExpAvailableParam(@RequestParam String taskDefId, @RequestParam TaskDef.Status status){
        return Result.ok(taskDefService.listExpAvailableParam(taskDefId, status));
    }

    @PostMapping(value = "/rule/delete")
    public Result<Boolean> deleteRule(@RequestBody TaskDefDto.Rule.Delete delete){
        taskDefService.deleteRule(delete.getRuleId());
        return Result.ok();
    }

}

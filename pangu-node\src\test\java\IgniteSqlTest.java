import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

import com.caidaocloud.pangu.core.ignite.KeyValueDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.CacheAtomicityMode;
import org.apache.ignite.cache.CacheMode;
import org.apache.ignite.cache.CachePeekMode;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.QueryCursor;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.apache.ignite.internal.binary.BinaryObjectImpl;
import org.apache.ignite.lang.IgniteBiPredicate;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/8
 */
@Slf4j
public class IgniteSqlTest {
	static CacheConfiguration cfg ;
	static IgniteConfiguration igniteCfg;
	static String cache_name = "PersonCache";

	static {
		igniteCfg = new IgniteConfiguration();
		 cfg = new MyCacheConfiguration();

		cfg.setName(cache_name);
		cfg.setCacheMode(CacheMode.PARTITIONED);
		cfg.setAtomicityMode(CacheAtomicityMode.ATOMIC);

		cfg.setReadThrough(true);
		cfg.setWriteThrough(true);

		CacheJdbcPojoStoreFactory<Integer, Person2> factory = new MyPojoStoreFactory();
		factory.setDialect(new BasicJdbcDialect());
		factory.setDataSourceFactory(IgniteThickClientTest.getDataSourceFactory());

		JdbcType personType = new JdbcType();
		personType.setCacheName(cache_name);
		personType.setDatabaseTable("PERSON");
		personType.setKeyType(Integer.class);
		personType.setValueType(Person2.class);
// Specify the schema if applicable
// personType.setDatabaseSchema("MY_DB_SCHEMA");

		personType.setKeyFields(new JdbcTypeField(java.sql.Types.INTEGER, "id", Integer.class, "id"));
		personType.setValueFields(
				new JdbcTypeField(java.sql.Types.INTEGER, "id", Integer.class, "id"),
				new JdbcTypeField(java.sql.Types.VARCHAR, "name", String.class, "name"));
		factory.setTypes(personType);

		cfg.setCacheStoreFactory(factory);


		QueryEntity qryEntity = new QueryEntity();

		qryEntity.setKeyType(Integer.class.getName());
		qryEntity.setValueType(Person2.class.getName());
		qryEntity.setKeyFieldName("id");

		Set<String> keyFields = new HashSet<>();
		keyFields.add("id");
		qryEntity.setKeyFields(keyFields);

		LinkedHashMap<String, String> fields = new LinkedHashMap<>();
		fields.put("id", "java.lang.Integer");
		fields.put("name", "java.lang.String");

		qryEntity.setFields(fields);

		cfg.setQueryEntities(Collections.singletonList(qryEntity));
		igniteCfg.setCacheConfiguration(cfg);
	}
	@Test
	public void kv(){
	}


	@Test
	public void read_test(){
		try (Ignite ignite=Ignition.start(igniteCfg)) {

			try (IgniteCache<Integer, Person2> cache = ignite.cache(cache_name);) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				cache.loadCache(null, Integer.class.getName(), "select * from person where id >3");
				Assert.assertEquals(2,cache.size(CachePeekMode.ALL));
			}finally {
				ignite.destroyCache(cache_name);
			}

		}
	}

	@Test
	public void read_predicate_test(){
		try (Ignite ignite=Ignition.start(igniteCfg)) {

			try (IgniteCache<Integer, Person2> cache = ignite.cache(cache_name);) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				// ignite.destroyCache(cache_name);
				cache.loadCache(new IgniteBiPredicate() {
					@SneakyThrows
					@Override
					public boolean apply(Object key, Object person) {
						BinaryObjectImpl binaryObject = (BinaryObjectImpl) person;
						Person2 value = binaryObject.context().marshaller().unmarshal(binaryObject.array(), null);
						return value.getId()>3;
					}
				});
				Assert.assertEquals(2,cache.size(CachePeekMode.ALL));
			}finally {
				ignite.destroyCache(cache_name);
			}

		}
	}

	@Test
	public void read_sql_test(){
		try (Ignite ignite=Ignition.start(igniteCfg)) {

			try (IgniteCache<Integer, Person2> cache = ignite.cache(cache_name);) {
				// cache.loadCache(null);
				Assert.assertEquals(0,cache.size(CachePeekMode.ALL));
				SqlFieldsQuery sql = new SqlFieldsQuery(
						"select concat(id, ' ', name) from Person2");

// Iterate over the result set.
				try (QueryCursor<List<?>> cursor = cache.query(sql)) {
					for (List<?> row : cursor)
						System.out.println("personName=" + row.get(0));
				}
				cache.localLoadCache(null);
				Assert.assertEquals(5,cache.size(CachePeekMode.ALL));

				 sql = new SqlFieldsQuery(
						"select concat(id, ' ', name) from Person2");

// Iterate over the result set.
				try (QueryCursor<List<?>> cursor = cache.query(sql)) {
					for (List<?> row : cursor)
						System.out.println("personName=" + row.get(0));
				}
				 sql = new SqlFieldsQuery(
						"select * from Person2");
				cache.put(6,new Person2(6,"Fina"));
				List<List<?>> all = cache.query(sql).getAll();
				Assert.assertEquals(6, all.size());
				for (List<?> row : all)
					System.out.println("personName=" + row.get(1));
			}finally {
				ignite.destroyCache(cache_name);
			}

		}
	}
}

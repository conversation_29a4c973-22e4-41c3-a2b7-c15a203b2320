package com.caidaocloud.pangu.node.business.enums;

import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.googlecode.totallylazy.Lists;
import lombok.Getter;

import java.util.List;

@Getter
public enum EnvironmentContext {
    BONUS_ACCOUNT_ID("BONUS_ACCOUNT_ID", "计件工资账套ID", ArrangementType.BONUS,
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT, ConditionOperatorEnum.GE, ConditionOperatorEnum.LT, ConditionOperatorEnum.LE), ConditionComponentEnum.STRING_INPUT),
    BONUS_ACCOUNT_NAME("BONUS_ACCOUNT_NAME", "计件工资账套名称", ArrangementType.BONUS,
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT, ConditionOperatorEnum.GE, ConditionOperatorEnum.LT, ConditionOperatorEnum.LE), ConditionComponentEnum.STRING_INPUT),
    BONUS_CALC_YEAR_MONTH("BONUS_CALC_YEAR_MONTH", "计算年月", ArrangementType.BONUS,
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT, ConditionOperatorEnum.GE, ConditionOperatorEnum.LT, ConditionOperatorEnum.LE), ConditionComponentEnum.STRING_INPUT),
    BONUS_CALC_START("BONUS_CALC_START", "开始日期", ArrangementType.BONUS,
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT, ConditionOperatorEnum.GE, ConditionOperatorEnum.LT, ConditionOperatorEnum.LE), ConditionComponentEnum.STRING_INPUT),
    BONUS_CALC_END("BONUS_CALC_END", "结束日期", ArrangementType.BONUS,
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT, ConditionOperatorEnum.GE, ConditionOperatorEnum.LT, ConditionOperatorEnum.LE), ConditionComponentEnum.STRING_INPUT);
    private String property;
    private String name;
    private ArrangementType type;
    private List<ConditionOperatorEnum> operators;
    private ConditionComponentEnum component;

    EnvironmentContext(String property, String name, ArrangementType type,
                       List<ConditionOperatorEnum> operators, ConditionComponentEnum component){
        this.property = property;
        this.name = name;
        this.type = type;
        this.operators = operators;
        this.component = component;
    }



}

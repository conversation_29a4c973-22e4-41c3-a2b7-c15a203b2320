package com.caidaocloud.compute.remote.framework.core;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;

/**
 *
 * <AUTHOR>
 * @date 2024/12/30
 */
public abstract class AbsInvokeHandler implements InvocationHandler {
	private String path;
	private Map<String, MethodMetadata> methodMetadataMap;
	public AbsInvokeHandler(String path, Map<String, MethodMetadata> methodMetadataMap) {
		this.path = path;
		this.methodMetadataMap = methodMetadataMap;
	}

	@Override
	public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
		if ("equals".equals(method.getName())) {
			try {
				Object otherHandler =
						args.length > 0 && args[0] != null ? Proxy.getInvocationHandler(args[0]) : null;
				return equals(otherHandler);
			} catch (IllegalArgumentException e) {
				return false;
			}
		} else if ("hashCode".equals(method.getName())) {
			return hashCode();
		} else if ("toString".equals(method.getName())) {
			return toString();
		}


		return doInvoke(path, method, args);
	}

	protected Object[] removeAddressIndex(Object[] args, int addressIndex) {
		List<Integer> list = new ArrayList(Arrays.asList(args));
		list.remove(addressIndex);
		return list.toArray(new Object[0]);
	}

	protected abstract Object doInvoke(String path, Method method, Object[] args);

	protected String getPath() {
		return path;
	}

	protected Map<String, MethodMetadata> getMethodMetadataMap() {
		return methodMetadataMap;
	}
}

package com.caidaocloud.pangu.core.ignite.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration for static IP addresses and port ranges for Ignite server
 *
 * <AUTHOR>
 * @date 2025/3/7
 */
@Data
@Component
@ConfigurationProperties(prefix = "caidaocloud.ignite.server")
public class StaticIpConfig {

    /**
     * List of IP configurations with port settings
     */
    private List<IpConfig> ipConfigs = new ArrayList<>();
    
    /**
     * Inner class representing an IP address with its port configuration
     */
    @Data
    public static class IpConfig {
        /**
         * IP address
         */
        private String ip;
        
        /**
         * Base port number (default: 47500)
         */
        private int basePort = 47500;
        
        /**
         * Number of ports in range (default: 10)
         */
        private int rangeSize = 10;
        
        /**
         * Get the ending port of the range
         * @return the last port in the range
         */
        public int getEndPort() {
            return basePort + rangeSize - 1;
        }
        
        /**
         * Get the port range as a string (e.g., "47500-47509")
         * @return formatted port range
         */
        public String getPortRangeString() {
            return basePort + "-" + getEndPort();
        }
    }
}

package com.caidaocloud.pangu.node.business.model;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("Arrangement")
public class Arrangement extends DataSimple {

    public final static String identifier = "entity.pangu.Arrangement";

    private String name;

    private String description;

    private ArrangementType type;

    private TriggerType triggerType;

    private String cron;

    private String arrangementId;

    private String arrangementVid;

    private Status status;

    public static Arrangement loadDraft(String arrangementId) {
        return DataQuery.identifier(identifier).limit(1, 1)
                .filter(DataFilter.eq("arrangementId", arrangementId)
                        .andIn("status", Lists.list(
                                Status.DRAFT.name(),
                                Status.PUBLISHED.name(),
                                Status.RE_DRAFT.name())), Arrangement.class)
                .getItems().stream().findAny().orElseThrow(()->new ServerException("arrangement not exist"));
    }

    public static Arrangement loadPublished(String arrangementId) {
        return DataQuery.identifier(identifier).limit(1, 1)
                .filter(DataFilter.eq("arrangementId", arrangementId)
                        .andIn("status", Lists.list(
                                Status.PUBLISHED.name(),
                                Status.UPDATED.name())), Arrangement.class)
                .getItems().stream().findAny().orElseThrow(()->new ServerException("arrangement not exist"));
    }

    public static List<Arrangement> listDraft(String name, ArrangementType type) {
        return DataQuery.identifier(identifier).limit(-1, 1)
                .filter(DataFilter.in("status", Lists.list(
                                Status.DRAFT.name(),
                                Status.PUBLISHED.name(),
                                Status.RE_DRAFT.name()))
                        .andRegexIf("name", name, ()-> StringUtils.isNotEmpty(name))
                        .andEqIf("type", String.valueOf(type), ()->null != type), Arrangement.class)
                .getItems();
    }

    public static List<Arrangement> listPublished(String name, ArrangementType type) {
        return DataQuery.identifier(identifier).limit(-1, 1)
                .filter(DataFilter.in("status", Lists.list(
                        Status.PUBLISHED.name(),
                        Status.UPDATED.name()))
                        .andRegexIf("name", name, ()-> StringUtils.isNotEmpty(name))
                        .andEqIf("type", String.valueOf(type), ()->null != type), Arrangement.class)
                .getItems();
    }

    public String create(){
        DataInsert.identifier(identifier).insert(this);
        val detail = new Detail();
        detail.setArrangementId(arrangementId);
        detail.status = Status.DRAFT;
        detail.setLastUpdate(System.currentTimeMillis());
        detail.create();
        return arrangementId;
    }

    public void update() {
        DataUpdate.identifier(identifier).update(this);
    }

    public void createBasic() {
        DataInsert.identifier(identifier).insert(this);
    }

    public void delete() {
        DataDelete.identifier(identifier).delete(getBid());
    }

    @Data
    @ApiModel("Arrangement.Detail")
    public static class Detail extends DataSimple{

        public final static String identifier = "entity.pangu.ArrangementDetail";

        private String name;//return to frontend when necessary

        private String arrangementId;

        private String arrangementVid;

        private Status status;

        @DisplayAsArray
        private List<Node> nodes = Lists.list();

        @DisplayAsArray
        private List<Note> notes = Lists.list();

        @DisplayAsArray
        private List<Map> edges = Lists.list();

        @DisplayAsObject
        private Map viewport = Maps.map();

        private Long lastUpdate;

        private Long lastPublished;

        public static Detail loadDraft(String arrangementId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("arrangementId", arrangementId)
                            .andIn("status", Lists.list(
                                    Status.DRAFT.name(),
                                    Status.PUBLISHED.name(),
                                    Status.RE_DRAFT.name())), Detail.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("arrangement not exist"));
        }

        public static Detail loadPublished(String arrangementId) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.eq("arrangementId", arrangementId)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Detail.class)
                    .getItems().stream().findAny().orElseThrow(()->new ServerException("arrangement not exist"));
        }

        public static Detail loadVersion(String arrangementVid) {
            return DataQuery.identifier(identifier).filter(DataFilter.eq("arrangementVid", arrangementVid),
                    Detail.class).getItems().stream().findAny().orElseThrow(()->new ServerException("arrangement not exist"));
        }

        public static List<Detail> listPublished(List<String> arrangementIds) {
            return DataQuery.identifier(identifier)
                    .filter(DataFilter.in("arrangementId", arrangementIds)
                            .andIn("status", Lists.list(
                                    Status.PUBLISHED.name(),
                                    Status.UPDATED.name())), Detail.class)
                    .getItems();
        }

        public void update() {
            DataUpdate.identifier(identifier).update(this);
        }

        public void create() {
            DataInsert.identifier(identifier).insert(this);
        }

        public void delete() {
            DataDelete.identifier(identifier).delete(getBid());
        }
    }

    @Data
    @ApiModel("Arrangement.Note")
    public static class Note {

        private String id;

        private String note;

        private String x;

        private String y;

        private String width;

        private String height;
    }

    @Data
    @ApiModel("Arrangement.Node")
    public static class Node {

        private String id;

        private NodeType type;

        private String title;

        private String description;

        private String taskDefId;

        private String targetPosition;

        private String sourcePosition;

        private String x;

        private String y;

        private String width;

        private String height;

        private List<String> previousNodeId = Lists.list();

        private List<String> nextNodeId = Lists.list();

        private List<NodeCondition> conditionToNextNodeId = Lists.list();

        private String preLoadData;

    }

    @Data
    @ApiModel("Arrangement.NodeCondition")
    public static class NodeCondition{

        private String id;

        private ConditionTree condition;

        private List<String> nextNodeId = Lists.list();
    }

    public enum Status {

        DRAFT, PUBLISHED, RE_DRAFT, UPDATED, REPLACED;

        public Status toDisplay(){
            switch (this){
                case DRAFT: return DRAFT;
                case PUBLISHED: return PUBLISHED;
                case RE_DRAFT:
                case UPDATED: return RE_DRAFT;
                case REPLACED: return REPLACED;
                default: return null;
            }
        }
    }

    public enum NodeType{
        START, END, TASK, CONDITION, DATA_PRE_LOAD
    }

    public enum TriggerType{
        BY_BUSINESS, BY_DATA, BY_CRON
    }

    public enum Error{
        MISSING_START_NODE, MISSING_END_NODE, NODE_WITHOUT_INFLOW,
        NODE_WITHOUT_OUTFLOW, IF_ELSE_WITHOUT_OUTFLOW, NODE_NOT_SET
    }




}

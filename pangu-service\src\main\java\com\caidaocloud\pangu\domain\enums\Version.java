package com.caidaocloud.pangu.domain.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
public enum Version {
	ENABLE("启用"){
		@Override
		public EnumSimple toValue() {
			EnumSimple enumSimple = new EnumSimple();
			enumSimple.setValue("1");
			return enumSimple;
		}
	}, SUSPEND("停用") {
		@Override
		public EnumSimple toValue() {
			EnumSimple enumSimple = new EnumSimple();
			enumSimple.setValue("0");
			return enumSimple;
		}
	};

	private String text;

	Version(String text) {
		this.text = text;
	}

	public abstract EnumSimple toValue();
}

package com.caidaocloud.pangu.manager.controller;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.manager.dto.ArrangementDto;
import com.caidaocloud.pangu.manager.model.ArrangementExec;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExec;
import com.caidaocloud.pangu.manager.service.ArrangementExecService;
import com.caidaocloud.pangu.manager.service.ReportService;
import com.caidaocloud.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/calc/v1")
public class ArrangementExecController {

    @Autowired
    private ReportService reportService;

    @Autowired
    private ArrangementExecService arrangementExecService;

    @PostMapping(value = "/exec")
    public Result<String> exec(@RequestBody ArrangementDto.Exec arrangement){
        return Result.ok(arrangementExecService.exec(arrangement));
    }

    @PostMapping(value = "/start/report")
    public Result<Boolean> report(@RequestBody CalcReport.Start report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/end/report")
    public Result<Boolean> report(@RequestBody CalcReport.End report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/condition/report")
    public Result<Boolean> report(@RequestBody CalcReport.Condition report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/loaded/report")
    public Result<Boolean> report(@RequestBody CalcReport.TaskDataLoaded report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/subTask/report")
    public Result<Boolean> report(@RequestBody CalcReport.SubTask report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/failed/report")
    public Result<Boolean> report(@RequestBody CalcReport.Fail report){
        return Result.ok(reportService.report(report));
    }

    @PostMapping(value = "/data/pre/load/report")
    public Result<Boolean> report(@RequestBody CalcReport.PreLoad report){
        return Result.ok(reportService.report(report));
    }

    @GetMapping(value = "/exec/node/result/cache")
    public Result<String> getResultCacheId(@RequestParam String execSeqId, @RequestParam String nodeId){
        return Result.ok(arrangementExecService.getResultCacheId(execSeqId, nodeId));
    }

    @GetMapping(value = "/exec/logs")
    public Result<PageResult<ArrangementExec>> execLogs(@RequestParam(required = false) String keywords,
                                                       @RequestParam(required = false) String arrangementId,
                                                       @RequestParam(required = false) ArrangementType type,
                                                       @RequestParam(required = false) ArrangementExec.Status status,
                                                       @RequestParam(required = false) Long start,
                                                       @RequestParam(required = false) Long end,
                                                       @RequestParam int pageNo,
                                                       @RequestParam int pageSize){
        return Result.ok(arrangementExecService.execLogs(keywords, arrangementId, type, status, start, end, pageNo, pageSize));
    }

    @GetMapping(value = "/exec/nodes/logs")
    public Result<List<ArrangementNodeExec>> execNodesLogs(@RequestParam String execSeqId){
        return Result.ok(arrangementExecService.execNodesLogs(execSeqId));
    }

}

package com.caidaocloud.compute.remote.framework.core;

import com.caidaocloud.compute.remote.framework.actor.RemoteRequest;

/**
 *
 * <AUTHOR>
 * @date 2024/10/23
 */
public abstract class AbsHandlerGenerator implements RequestHandlerGenerator {
	private final RemoteInitializer initializer;

	protected AbsHandlerGenerator(RemoteInitializer initializer) {
		this.initializer = initializer;
	}


	public RemoteInitializer getInitializer() {
		return initializer;
	}
}

package com.caidaocloud.pangu.node.business.controller;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.dto.ArrangeMonthAnalyze;
import com.caidaocloud.pangu.node.business.dto.ArrangementDto;
import com.caidaocloud.pangu.node.business.dto.ArrangementMatchConditionDto;
import com.caidaocloud.pangu.node.business.dto.ArrangementQueryDto;
import com.caidaocloud.pangu.node.business.dto.SimpleTree;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.service.ArrangementService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/arrangement/v1/arrangement")
@Slf4j
public class ArrangementController {

    @Autowired
    private ArrangementService arrangementService;

    @PostMapping(value = "/create")
    public Result<String> create(@RequestBody ArrangementDto arrangement) {
        return Result.ok(arrangementService.create(arrangement));
    }

    @PostMapping(value = "/update")
    public Result<Boolean> update(@RequestBody ArrangementDto arrangement) {
        arrangementService.update(arrangement);
        return Result.ok();
    }

    @PostMapping(value = "/updateDetail")
    public Result<Boolean> updateDetail(@RequestBody ArrangementDto.Detail arrangementDetail) {
        arrangementService.updateDetail(arrangementDetail);
        return Result.ok();
    }

    @PostMapping(value = "/publish")
    public Result<ArrangementDto.PublishResult> publish(@RequestBody ArrangementDto.Publish publish) {
        return Result.ok(arrangementService.publish(publish.getArrangementId()));
    }

    @GetMapping(value = "/list/draft")
    public Result<List<Arrangement>> listDraft(@RequestParam(required = false) String name, @RequestParam(required = false) ArrangementType type){
        return Result.ok(arrangementService.listDraft(name, type));
    }

    @GetMapping(value = "/load/published")
    public Result<Arrangement> loadPublished(@RequestParam(required = false) String arrangementId) {
        return Result.ok(arrangementService.loadPublished(arrangementId));
    }

    @GetMapping(value = "/list/published")
    public Result<List<Arrangement>> listPublished(@RequestParam(required = false) String name, @RequestParam(required = false) ArrangementType type){
        return Result.ok(arrangementService.listPublished(name, type));
    }

    @GetMapping(value = "/detail/draft")
    public Result<Arrangement.Detail> loadDetailDraft(@RequestParam String arrangementId) {
        return Result.ok(arrangementService.loadDetailDraft(arrangementId));
    }

    @GetMapping(value = "/detail/published")
    public Result<Arrangement.Detail> loadDetailPublished(@RequestParam String arrangementId) {
        return Result.ok(arrangementService.loadDetailPublished(arrangementId));
    }

    @PostMapping(value = "/detail/published/list")
    public Result<List<Arrangement.Detail>> loadDetailPublishedList(@RequestBody ArrangementQueryDto dto) {
        return Result.ok(arrangementService.listDetailPublished(dto.getArrangementIds()));
    }

    @PostMapping(value = "/detail/version")
    public Result<Arrangement.Detail> loadDetailVersion(@RequestBody ArrangementQueryDto dto) {
        return Result.ok(arrangementService.loadDetailVersion(dto.getArrangementVerId()));
    }

    @PostMapping(value = "/copy")
    public Result<String> copy(@RequestBody ArrangementDto.Copy copy) {
        return Result.ok(arrangementService.copy(copy.getArrangementId(), copy.getName()));
    }

    @GetMapping(value = "/node/delete/check")
    public Result<Boolean> checkNodeDeletable(@RequestParam String arrangementId, @RequestParam String taskDefId) {
        return Result.ok(arrangementService.checkNodeDeletable(arrangementId, taskDefId));
    }

    @PostMapping(value = "/delete")
    public Result<Boolean> delete(@RequestBody ArrangementDto.Delete arrangement) {
        arrangementService.delete(arrangement.getArrangementId());
        return Result.ok();
    }

    @GetMapping(value = "/conditions/available")
    public Result<List<ArrangementMatchConditionDto>> availableConditions(@RequestParam ArrangementType type) {
        return Result.ok(arrangementService.availableConditions(type));
    }

    @GetMapping(value = "/data/load/functions")
    public Result<List<KeyValue>> preLoadDataFunctions() {
        return Result.ok(arrangementService.preLoadDataFunctions());
    }

    @PostMapping(value = "/recover")
    public Result<Boolean> recover(@RequestBody ArrangementDto.Recover arrangement) {
        arrangementService.recover(arrangement.getArrangementId());
        return Result.ok();
    }

    @GetMapping(value = "/node/previous")
    public Result<List<String>> fetchPreviousNodes(@RequestParam String arrangementVid,
            @RequestParam String arrangementNodeId) {
        return Result.ok(arrangementService.fetchPreviousNodes(arrangementVid, arrangementNodeId));
    }

    @GetMapping(value = "/node/next")
    public Result<List<String>> fetchNextNodes(@RequestParam String arrangementVid,
            @RequestParam String arrangementNodeId) {
        return Result.ok(arrangementService.fetchNextNodes(arrangementVid, arrangementNodeId));
    }

    @PostMapping(value = "/reference")
    public Result<Boolean> reference(@RequestParam String arrangementId) {
        arrangementService.reference(arrangementId);
        return Result.ok();
    }

    @GetMapping(value = "/node/names")
    public Result<Map<String, List<String>>> fetchAllNodeNames(@RequestParam String arrangementVid) {
        return Result.ok(arrangementService.fetchAllNodeNames(arrangementVid));
    }


    @GetMapping(value = "/task/rule/tree/published")
    public Result<List<SimpleTree>> fetchTaskRuleTreePublished(@RequestParam String arrangementId) {
        return Result.ok(arrangementService.fetchTaskRuleTreePublished(arrangementId));
    }

    @GetMapping(value = "/task/rule/tree/draft")
    public Result<List<SimpleTree>> fetchTaskRuleTreeDraft(@RequestParam String arrangementId) {
        return Result.ok(arrangementService.fetchTaskRuleTreeDraft(arrangementId));
    }



    // 工序
    // 工序编号随机 工序名称见excel 工段 前道后道对应的字典 与工序有对应关系 所属产品名称 171 172 118 每个产品下建一个工序
    // 所属团队 ORGPRESSURE0 计薪工序：产品名称工序名称



    // 一次分配：entity.form.2139520079804476
    // 生效年月 202501-202503 表单状态 已审批

    // entity.form.2139520079804476_CRUD6Dcrfs 一个工序一个月一条
    // 工段 前道 后道



    @SneakyThrows
    @PostMapping(value = "/test/data/run")
    public Result<Boolean> runTestData() {
        val orgId = DataQuery.identifier("entity.hr.Org").limit(1, 1)
                .filter(DataFilter.eq("code", "ORGPRESSURE0"), DataSimple.class).getItems()
                .get(0).getBid();
        val postIds = DataQuery.identifier("entity.hr.Post").limit(2, 1)
                .filter(DataFilter.in("code", Lists.list("POSPRESSURE1", "POSPRESSURE2")), DataSimple.class).getItems()
                .stream().map(it -> it.getBid()).collect(Collectors.toList());
        val employType = Lists.list(DictSimple.code2Dict("EmployType", "waibao"),
                DictSimple.code2Dict("EmployType", "paiqian"));
        val tSkill = Lists.list(DictSimple.code2Dict("skillLevel", "001"),
                DictSimple.code2Dict("skillLevel", "002"),
                DictSimple.code2Dict("skillLevel", "003"),
                DictSimple.code2Dict("skillLevel", "004"));
        val status = new EnumSimple();
        status.setValue("0");
        val hireDate = new SimpleDateFormat("yyyyMMdd HH:mm:ss:SSS").parse("20250101 00:00:00:000").getTime();
        val gd = Lists.list(DictSimple.code2Dict("WorkshopSection", "hsdfhg"),
                DictSimple.code2Dict("WorkshopSection", "sudfj"));
        val performancePeriods = DataQuery.identifier("entity.wfm.PerformancePeriod")
                .filterProperties(DataFilter.in("period", Lists.list("202503", "202504", "202505")),
                        Lists.list("bid","period", "startDate", "endDate"), System.currentTimeMillis()).getItems();
        val monthScores = Lists.list("1","2","2.2","2.4","2.6","2.8","3","3.2","3.4","3.6","3.8","4","4.2","4.4","4.6","4.8","5");
        for (int i = 0; i < 10000; i++) {
            val empId = SnowUtil.nextId();
            val privateInfo = new DataSimple();
            privateInfo.getProperties().add("empId", empId);
            privateInfo.getProperties().add("name", "Pressure" + empId);
            DataInsert.identifier("entity.hr.EmpPrivateInfo").insert(privateInfo);
            val workInfo = new DataSimple();
            workInfo.getProperties().add("empId", empId);
            workInfo.getProperties().add("name", "Pressure" + empId);
            workInfo.getProperties().add("workno", "pw" + empId);
            workInfo.getProperties().add("organize", orgId);
            workInfo.getProperties().add("post", postIds.get(new Random().nextInt(2)));
            workInfo.getProperties().add("empType", employType.get(new Random().nextInt(2)));
            workInfo.getProperties().add("skillQulificationLevel", tSkill.get(new Random().nextInt(4)));
            workInfo.getProperties().add("hireDate", String.valueOf(hireDate));
            workInfo.getProperties().add("empStatus", status);
            workInfo.getProperties().add("workshopSection", gd.get(new Random().nextInt(2)));
            workInfo.setDataStartTime(hireDate);
            DataInsert.identifier("entity.hr.EmpWorkInfo").insert(workInfo);
            val basicInfo = new DataSimple();
            basicInfo.setBid(empId);
            basicInfo.getProperties().add("workno", "pw" + empId);
            basicInfo.getProperties().add("name", "Pressure" + empId);
            basicInfo.getProperties().add("empStatus", status);
            basicInfo.getProperties().add("empType", workInfo.getProperties().get("empType"));
            basicInfo.getProperties().add("organize", workInfo.getProperties().get("organize"));
            basicInfo.getProperties().add("post", workInfo.getProperties().get("post"));
            basicInfo.setDataStartTime(hireDate);
            DataInsert.identifier("entity.hr.EmpBasicInfo").insert(basicInfo);
            performancePeriods.forEach(performancePeriod -> {
                // val period = performancePeriod.get("period");
                val startDate = performancePeriod.get("startDate");
                val endDate = performancePeriod.get("endDate");
                val periodId = performancePeriod.get("bid");
                val empPerformance = new DataSimple();
                empPerformance.getProperties().add("empId", empId);
                empPerformance.getProperties().add("performancePeriodId", periodId);
                empPerformance.getProperties().add("startDate", startDate);
                empPerformance.getProperties().add("endDate", endDate);
                empPerformance.getProperties().add("monthScore", monthScores.get(new Random().nextInt(monthScores.size())));
                val emp = new EmpSimple();
                emp.setEmpId(empId);
                EmpSimple.doEmpSimple(emp);
                empPerformance.getProperties().add("emp", emp);
                DataInsert.identifier("entity.wfm.PerformanceEmp").insert(empPerformance);
            });
        }


        // 工段 工序 参考模具人数（一拖二） 人均工时 单工序工时 比例 工序单价-按照比例 调整额 工序单价-调整后 工序小时工资 人均计件单价

        return Result.ok();

    }


    @SneakyThrows
    @PostMapping(value = "/test/data/run/workhour")
    public Result a(@RequestParam long start, @RequestParam long end,
            @RequestParam String accountId, @RequestParam String accountName) {
        val user = SecurityUserUtil.getSecurityUserInfo();
        new Thread(() -> {
            try {
                SecurityUserUtil.setSecurityUserInfo(user);
                val emps = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
                        .filterProperties(DataFilter.regex("name", "Pressure"),
                                Lists.list("empId", "workno", "name"), System.currentTimeMillis()).getItems();
                val originProcessDatas = Lists.list(
                        Lists.list("前道","合模A","18","10","180","12.33%","1232.88 ","437.57 ","1670.45 ","9.28 ","92.80"),
                        Lists.list("前道","合模B","18","10","180","12.33%","1232.88 ","437.57 ","1670.45 ","9.28 ","92.80"),
                        Lists.list("前道","壳体灌注","9","11","99","6.78%","678.08 ","(16.37),","661.71 ","6.68 ","73.52"),
                        Lists.list("前道","铺层-PS","21","9","189","12.95%","1294.52 ","199.03 ","1493.55 ","7.90 ","71.12"),
                        Lists.list("前道","铺层-SS","21","9","189","12.95%","1294.52 ","112.02 ","1406.54 ","7.44 ","66.98"),
                        Lists.list("前道","机动","4","9","36","2.47%","246.58 ","31.83 ","278.41 ","7.73 ","69.60"),
                        Lists.list("前道","腹板铺层","9","8","72","4.93%","493.15 ","173.87 ","667.02 ","9.26 ","74.11"),
                        Lists.list("前道","腹板扒皮","9","2","18","1.23%","123.29 ","(7.28),","116.00 ","6.44 ","12.89"),
                        Lists.list("前道","腹板灌注","4","8","32","2.19%","219.18 ","(88.67),","130.50 ","4.08 ","32.63"),
                        Lists.list("前道","腹板后处理","2","11","22","1.51%","150.68 ","37.82 ","188.51 ","8.57 ","94.25"),
                        Lists.list("前道","大梁铺层","8","9","72","4.93%","493.15 ","173.87 ","667.02 ","9.26 ","83.38"),
                        Lists.list("前道","大梁扒皮","8","1.5","12","0.82%","82.19 ","(9.69),","72.50 ","6.04 ","9.06"),
                        Lists.list("前道","大梁灌注","3","8","24","1.64%","164.38 ","24.12 ","188.51 ","7.85 ","62.84"),
                        Lists.list("前道","预制瓦铺层","7","10","70","4.79%","479.45 ","187.57 ","667.02 ","9.53 ","95.29"),
                        Lists.list("前道","预制瓦扒皮","7","1","7","0.48%","47.95 ","(18.94),","29.00 ","4.14 ","4.14"),
                        Lists.list("前道","预制瓦灌注","2","8","16","1.10%","109.59 ","6.41 ","116.00 ","7.25 ","58.00"),
                        Lists.list("前道","UD铺层","6","4","24","1.64%","164.38 ","24.12 ","188.51 ","7.85 ","31.42"),
                        Lists.list("前道","UD扒皮","6","1","6","0.41%","41.10 ","(19.35),","21.75 ","3.63 ","3.63"),
                        Lists.list("前道","UD灌注","2","4","8","0.55%","54.79 ","(2.59),","52.20 ","6.53 ","26.10"),
                        Lists.list("前道","大梁后处理","2","9","18","1.23%","123.29 ","(7.28),","116.00 ","6.44 ","58.00"),
                        Lists.list("前道","UD后处理","2","2","4","0.27%","27.40 ","(5.65),","21.75 ","5.44 ","10.88"),
                        Lists.list("前道","预制辅材","5","8","40","2.74%","273.97 ","66.21 ","340.18 ","8.50 ","68.04"),
                        Lists.list("前道", "喷砂", "1", "6", "6", "0.41%", "41.10 ", "(4.26),", "36.83 ", "6.14 ", "36.83"),
                        Lists.list("前道","行车","6","8","48","3.29%","328.77 ","48.24 ","377.01 ","7.85 ","62.84"),
                        Lists.list("前道","胶房","8","8","64","4.38%","438.36 ","(90.35),","348.01 ","5.44 ","43.50"),
                        Lists.list("前道", "小件行车", "1", "8", "8", "0.55%", "54.79 ", "3.21 ", "58.00 ", "7.25 ", "58.00"),
                        Lists.list("前道","小件胶房","2","8","16","1.10%","109.59 ","(22.59),","87.00 ","5.44 ","43.50"),
                        Lists.list("前道","合模一粘","18","10","180","1.10%","109.59 ","1560.86 ","1670.45 ","0.61 ","92.80"),
                        Lists.list("前道","合模二粘","18","10","180","1.10%","109.59 ","1560.86 ","1670.45 ","0.61 ","92.80"),
                        Lists.list("后道","切割","4","6.00 ","24 ","4.07%","700.00 ","(100.00),","600 ","29.17 ","150.00"),
                        Lists.list("后道","手糊","12","7.00 ","84 ","14.25%","2500.00 ","0.00 ","2500 ","29.76 ","208.33"),
                        Lists.list("后道","铣面","1","6.00 ","6 ","1.02%","180.00 ","0.00 ","180 ","30.00 ","180.00"),
                        Lists.list("后道","五金","4","7.00 ","28 ","4.75%","900.00 ","0.00 ","900 ","32.14 ","225.00"),
                        Lists.list("后道","防雨罩","5","4.50 ","23 ","3.82%","731.00 ","(131.00),","600 ","31.78 ","120.00"),
                        Lists.list("后道","打磨","19","6.47 ","123 ","20.86%","3780.00 ","(200.00),","3580 ","30.73 ","188.42"),
                        Lists.list("后道","辊涂","10","8.00 ","80 ","13.57%","2500.00 ","731.00 ","3231 ","31.25 ","323.10"),
                        Lists.list("后道","LEP","2","10.00 ","20 ","3.39%","600.00 ","0.00 ","600 ","30.00 ","300.00"),
                        Lists.list("后道","VG","4","3.00 ","12 ","2.04%","400.00 ","100.00 ","500 ","33.33 ","125.00"),
                        Lists.list("后道","配重","2","5.00 ","10 ","1.70%","300.00 ","0.00 ","300 ","30.00 ","150.00"),
                        Lists.list("后道","称重","6","4.00 ","24 ","4.07%","1000.00 ","(400.00),","600 ","41.67 ","100.00"),
                        Lists.list("后道","NCR维修","4","8.00 ","32 ","5.43%","1500.00 ","0.00 ","1500 ","46.88 ","375.00"),
                        Lists.list("后道","内腔维修","4","11.00 ","44 ","7.46%","2000.00 ","0.00 ","2000 ","45.45 ","500.00"),
                        Lists.list("后道","外堆场精修","7","8.00 ","56 ","9.50%","1500.00 ","0.00 ","1500 ","26.79 ","214.29"),
                        Lists.list("后道","后道行车","4","6.00 ","24 ","4.07%","750.00 ","0.00 ","750 ","31.25 ","187.50")
                );
                Map<String, String> workSectionMap = Maps.map("后道", "sudfj", "前道", "hsdfhg");
                val products = Lists.list("171", "172", "118");
                originProcessDatas.forEach(originProcessData -> {
                    products.forEach(product -> {
                        val analyze = new ArrangeMonthAnalyze();
                        analyze.setStartDate(start);
                        analyze.setEndDate(end);
                        // analyze.setProductId();
                        analyze.setProductName(product);
                        // analyze.setProcessId();
                        // analyze.setProcessCode();
                        analyze.setProcessName(product + originProcessData.get(1));
                        analyze.setCompletedQuantity(20);
                        analyze.setInventoryQuantity(20);
                        val section = DictSimple.code2Dict("WorkshopSection", workSectionMap.get(originProcessData.get(0)));
                        analyze.setWorkshopSection(section);
                        analyze.setAccountId(accountId);
                        analyze.setAccountName(accountName);
                        DataInsert.identifier("entity.pangu.WfmProcessData").insert(analyze);
                        emps.forEach(emp -> {
                            if (new Random().nextInt(44) <= 3) {
                                analyze.setBid(null);
                                analyze.setId(null);
                                analyze.setIdentifier(null);
                                analyze.setEmpId(emp.get("empId"));
                                analyze.setWorkno(emp.get("workno"));
                                analyze.setEmpName(emp.get("name"));
                                DataInsert.identifier("entity.pangu.WfmEmpProcessData").insert(analyze);
                            }
                        });
                    });
                });

            } catch (Throwable e) {
                log.error("generate data error");
                throw e;
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }).start();
        return Result.ok();
    }

    public static void main(String[] args) {
        val list = Lists.list("a", "b");
        for (int i = 0; i < 22; i++) {
            System.out.println(new Random().nextInt(44) <= 3);
        }
    }
}

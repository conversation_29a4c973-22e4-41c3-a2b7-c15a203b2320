package dynamic;

import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.dynamic.loading.ClassLoadingStrategy;
import net.bytebuddy.implementation.FieldAccessor;
import net.bytebuddy.implementation.FixedValue;
import net.bytebuddy.implementation.MethodCall;
import net.bytebuddy.matcher.ElementMatchers;
import net.bytebuddy.utility.JavaModule;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class ByteBuddyFieldGetterSetterExample {

    public static void main(String[] args) throws Exception {
        // 创建动态类
        DynamicType.Unloaded<?> dynamicType = new ByteBuddy()
                .subclass(Object.class) // 继承自 Object 类
                .name("com.example.generated.Person") // 设置类名
               // 添加字段：name
                .defineField("age", int.class) // 添加字段：age
                // 添加 getter 和 setter 方法
                .defineField("name", String.class)
                .defineMethod("getName", String.class, Modifier.PUBLIC) // getter 方法
                .intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
                .defineMethod("setName", void.class,Modifier.PUBLIC) // setter 方法
                .withParameters(String.class) // 接受 String 类型参数
                .intercept(FieldAccessor.ofBeanProperty()) // 不做操作，只生成方法
                .defineMethod("getAge", int.class,Modifier.PUBLIC) // getter 方法
                .intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
                .defineMethod("setAge", void.class,Modifier.PUBLIC) // setter 方法
                .withParameters(int.class) // 接受 int 类型参数
                .intercept(FieldAccessor.ofBeanProperty()) // 返回固定的值
                .make(); // 生成类

        // 加载类到 JVM 中
        Class<?> dynamicClass = dynamicType.load(Object.class.getClassLoader()).getLoaded();

        // 创建类实例
        Object personInstance = dynamicClass.getDeclaredConstructor().newInstance();

        // 调用 getter 方法
        Method getNameMethod = dynamicClass.getDeclaredMethod("getName");
        String name = (String) getNameMethod.invoke(personInstance);
        Assert.assertNull(name); // 输出：Name: John Doe

        name = "Jane Doe_2";
        // 调用 setter 方法并设置字段值
        Method setNameMethod = dynamicClass.getDeclaredMethod("setName", String.class);
        setNameMethod.invoke(personInstance, name);

        // 再次调用 getter 方法查看值变化
        Assert.assertEquals(name, (String) getNameMethod.invoke(personInstance));

        // 调用 getAge 方法
        Method getAgeMethod = dynamicClass.getDeclaredMethod("getAge");
        int age = (int) getAgeMethod.invoke(personInstance);
        Assert.assertEquals(0, age);

        age = 35;
        // 调用 setAge 方法并设置新的年龄
        Method setAgeMethod = dynamicClass.getDeclaredMethod("setAge", int.class);
        setAgeMethod.invoke(personInstance, age);

        // 再次调用 getAge 方法查看值变化
        Assert.assertEquals(age, (int) getAgeMethod.invoke(personInstance));
        System.out.println("data:" + personInstance.toString());
    }

    @Test
        public  void classTest(){
            // 使用 Byte Buddy 生成一个类
            DynamicType.Unloaded<?> dynamicType = new ByteBuddy()
                    .subclass(Object.class) // 生成一个继承 Object 的类
                    .name("com.example.GeneratedClass") // 指定全限定名
                    .make();

            // 将生成的类加载到当前类的类加载器中
            Class<?> generatedClass = dynamicType.load(Object.class.getClassLoader())
                    .getLoaded();

            // 使用 Class.forName 加载生成的类
        Class<?> loadedClass = null;
        try {
            loadedClass = Class.forName("com.example.GeneratedClass", true, Object.class.getClassLoader());
        }
        catch (ClassNotFoundException e) {
            e.printStackTrace();
        }

        // 输出类名，验证是否成功加载
            System.out.println("加载的类名: " + loadedClass.getName());
        }
}

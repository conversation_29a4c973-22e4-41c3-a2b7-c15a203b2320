
package com.caidaocloud.pangu.domain.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.pangu.domain.repository.IBonusStructItemRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructReportItemRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 奖金结构--奖金项配置
 */
@Data
public class BonusStructReportItem extends DataSimple {

    private String structId;
    private String itemId;

    private Integer sort;

    public BonusStructReportItem() {
        setIdentifier(BONUS_STRUCT_ITEM_IDENTIFIER);
    }

    public BonusStructReportItem(String structId, String itemId) {
        this(); // 调用无参构造函数
        this.structId = structId;
        this.itemId = itemId;
        // 排序默认最大值
        this.sort = 65535;
    }

    // 带参构造函数，调用无参构造函数
    public BonusStructReportItem(String structId, String itemId, Integer sort) {
        this(); // 调用无参构造函数
        this.structId = structId;
        this.itemId = itemId;
        this.sort = sort;
    }

    public static final String BONUS_STRUCT_ITEM_IDENTIFIER = "entity.bonus.BonusStructReportItem";
    public String create() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
        SpringUtil.getBean(IBonusStructReportItemRepository.class).insert(this);
        return this.getBid();
    }

    public void update(){
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        SpringUtil.getBean(IBonusStructReportItemRepository.class).updateById(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BonusStructReportItem)) return false;
        if (!super.equals(o)) return false;
        BonusStructReportItem that = (BonusStructReportItem) o;
        return Objects.equals(structId, that.structId) && Objects.equals(itemId, that.itemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), structId, itemId);
    }
}

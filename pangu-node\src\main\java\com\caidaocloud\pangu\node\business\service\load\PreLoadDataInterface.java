package com.caidaocloud.pangu.node.business.service.load;

import com.caidaocloud.dto.KeyValue;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

public interface PreLoadDataInterface {

    KeyValue textToValue();

    void exec(Map<String, String> context);

    Map<String, PreLoadDataInterface> implementations = Maps.map();

    @PostConstruct
    default void register(){
        implementations.put((String)this.textToValue().getValue(), this);
    }
}

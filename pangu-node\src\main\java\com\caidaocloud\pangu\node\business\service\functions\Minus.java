package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class Minus extends AbstractFunction {
    @Override
    public String getName() {
        return "MINUS";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1,AviatorObject aviatorObject2) {
        val first = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject1, env).toString());
        val second = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject2, env).toString());
        val result = first.subtract(second);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(first).append(",").append(second).append(")=").append(result).toString());
        }
        return new AviatorDecimal(result);
    }
}

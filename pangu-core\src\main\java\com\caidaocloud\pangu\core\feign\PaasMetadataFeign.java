package com.caidaocloud.pangu.core.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@FeignClient(value = "${feign.rename.caidaocloud-hr-paas-service:caidaocloud-hr-paas-service}", configuration = FeignConfiguration.class,contextId = "PaasMetadataFeign")
public interface PaasMetadataFeign {
	@GetMapping("/api/hrpaas/v1/metadata/def/mapping")
	Result<Map<String, String>> getIdentifierFieldMapping(@RequestParam("identifier") String identifier, @RequestParam("tenantId") String tenantId);
}

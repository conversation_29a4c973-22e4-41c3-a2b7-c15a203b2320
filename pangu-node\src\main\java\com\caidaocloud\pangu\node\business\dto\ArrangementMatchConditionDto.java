package com.caidaocloud.pangu.node.business.dto;

import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.val;

import java.util.List;

@Data
public class ArrangementMatchConditionDto {

    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("code")
    private String code;
    @ApiModelProperty("比较符号")
    private List<ConditionOperatorEnum> operators;
    @ApiModelProperty("组件")
    private ConditionComponentEnum component;

    public static ArrangementMatchConditionDto fromEnvironmentContext(EnvironmentContext context) {
        val condition = new ArrangementMatchConditionDto();
        condition.setCode(context.getProperty());
        condition.setName(context.getName());
        condition.setOperators(context.getOperators());
        condition.setComponent(context.getComponent());
        return condition;
    }
}

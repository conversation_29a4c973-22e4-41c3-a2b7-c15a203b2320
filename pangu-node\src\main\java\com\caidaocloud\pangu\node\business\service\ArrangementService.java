package com.caidaocloud.pangu.node.business.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.dto.ArrangementDto;
import com.caidaocloud.pangu.node.business.dto.ArrangementMatchConditionDto;
import com.caidaocloud.pangu.node.business.dto.ExpAvailableParamDto;
import com.caidaocloud.pangu.node.business.dto.SimpleTree;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.pangu.node.business.model.ArrangementReferenced;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.service.load.PreLoadDataInterface;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sets;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ArrangementService {

    @Autowired(required = false)
    private TaskDefService taskDefService;

    @PaasTransactional
    public String create(ArrangementDto arrangementDto){
        val arrangement = arrangementDto.toEntity();
        return arrangement.create();
    }

    @PaasTransactional
    public void update(ArrangementDto arrangementDto){
        val exist = Arrangement.loadDraft(arrangementDto.getArrangementId());
        if(Arrangement.Status.PUBLISHED.equals(exist.getStatus())){
            val arrangement = arrangementDto.toEntity(exist, true);
            arrangement.createBasic();
            exist.update();
        }else{
            val arrangement = arrangementDto.toEntity(exist, false);
            arrangement.update();
        }
    }

    public void updateLastUpdate(String arrangementId){
        val exist = Arrangement.Detail.loadDraft(arrangementId);
        if(Arrangement.Status.PUBLISHED.equals(exist.getStatus())){
            exist.setStatus(Arrangement.Status.UPDATED);
            exist.update();
            exist.setId(null);
            exist.setBid(null);
            exist.setStatus(Arrangement.Status.RE_DRAFT);
            exist.setLastUpdate(System.currentTimeMillis());
            exist.create();
        }else{
            exist.setLastUpdate(System.currentTimeMillis());
            exist.update();
        }
    }

    @PaasTransactional
    public void updateDetail(ArrangementDto.Detail detailDto){
        val exist = Arrangement.Detail.loadDraft(detailDto.getArrangementId());
        if(Arrangement.Status.PUBLISHED.equals(exist.getStatus())){
            val detail = detailDto.toEntity(exist, true);
            detail.setLastUpdate(System.currentTimeMillis());
            detail.create();
            exist.update();
        }else{
            val detail = detailDto.toEntity(exist, false);
            detail.setLastUpdate(System.currentTimeMillis());
            detail.update();
        }
    }

    @PaasTransactional
    public ArrangementDto.PublishResult publish(String arrangementId){
        val result = new ArrangementDto.PublishResult();
        val errors = checkArrangementLegal(arrangementId);
        if(!errors.isEmpty()){
            result.setPublished(false);
            result.setErrors(errors);
            return result;
        }
        val arrangement = Arrangement.loadDraft(arrangementId);
        var arrangementVid = SnowUtil.nextId();
        if(Arrangement.Status.DRAFT.equals(arrangement.getStatus())){
            arrangement.setStatus(Arrangement.Status.PUBLISHED);
            arrangement.setArrangementVid(arrangementVid);
            arrangement.update();
        }else if(Arrangement.Status.PUBLISHED.equals(arrangement.getStatus())){
            arrangement.setStatus(Arrangement.Status.REPLACED);
            arrangement.update();
            arrangement.setBid(null);
            arrangement.setId(null);
            arrangement.setStatus(Arrangement.Status.PUBLISHED);
            arrangement.setArrangementVid(arrangementVid);
            arrangement.createBasic();
        }else if(Arrangement.Status.RE_DRAFT.equals(arrangement.getStatus())){
            val arrangementPublished = Arrangement.loadPublished(arrangementId);
            arrangementPublished.setStatus(Arrangement.Status.REPLACED);
            arrangementPublished.update();

            arrangement.setStatus(Arrangement.Status.PUBLISHED);
            arrangement.setArrangementVid(arrangementVid);
            arrangement.update();
        }
        publishDetail(arrangementId, arrangementVid);
        taskDefService.publishTaskDef(arrangementId, arrangementVid);
        return result;
    }

    private void publishDetail(String arrangementId, String arrangementVid){
        val detail = Arrangement.Detail.loadDraft(arrangementId);
        if(Arrangement.Status.DRAFT.equals(detail.getStatus())){
            detail.setStatus(Arrangement.Status.PUBLISHED);
            detail.setArrangementVid(arrangementVid);
            detail.setLastPublished(System.currentTimeMillis());
            detail.update();
        }else if(Arrangement.Status.PUBLISHED.equals(detail.getStatus())){
            detail.setStatus(Arrangement.Status.REPLACED);
            detail.update();
            detail.setBid(null);
            detail.setId(null);
            detail.setStatus(Arrangement.Status.PUBLISHED);
            detail.setArrangementVid(arrangementVid);
            detail.setLastPublished(System.currentTimeMillis());
            detail.create();
        }else if(Arrangement.Status.RE_DRAFT.equals(detail.getStatus())){
            val detailPublished = Arrangement.Detail.loadPublished(arrangementId);
            detailPublished.setStatus(Arrangement.Status.REPLACED);
            detailPublished.update();

            detail.setStatus(Arrangement.Status.PUBLISHED);
            detail.setArrangementVid(arrangementVid);
            detail.setLastPublished(System.currentTimeMillis());
            detail.update();
        }
    }


    public List<Arrangement> listDraft(String name, ArrangementType type) {
        return Arrangement.listDraft(name, type);
    }

    public List<Arrangement> listPublished(String name, ArrangementType type) {
        return Arrangement.listPublished(name, type);
    }

    public Arrangement.Detail loadDetailDraft(String arrangementId) {
        val detail = Arrangement.Detail.loadDraft(arrangementId);
        detail.setName(Arrangement.loadDraft(arrangementId).getName());
        return detail;
    }

    public Arrangement.Detail loadDetailPublished(String arrangementId) {
        val detail = Arrangement.Detail.loadPublished(arrangementId);
        detail.setName(Arrangement.loadPublished(arrangementId).getName());
        return detail;
    }

    @PaasTransactional
    public String copy(String fromId, String name) {
        val arrangement = Arrangement.loadDraft(fromId);
        val arrangementDto = FastjsonUtil.convertObject(arrangement, ArrangementDto.class);
        arrangementDto.setArrangementId(null);
        arrangementDto.setName(name);
        val copy = arrangementDto.toEntity();
        String copyId = copy.getArrangementId();
        copy.createBasic();

        val arrangementDetail= Arrangement.Detail.loadDraft(fromId);
        val detailDto = FastjsonUtil.convertObject(arrangementDetail, ArrangementDto.Detail.class);
        detailDto.setArrangementId(copyId);
        val copyDetail = detailDto.toEntity();
        val taskDefIdFromToCopy = taskDefService.copyList(fromId, copyId);
        copyDetail.getNodes().forEach(node->{
            if(StringUtils.isNotEmpty(node.getTaskDefId())){
                node.setTaskDefId(taskDefIdFromToCopy.get(node.getTaskDefId()));
            }
        });
        copyDetail.setLastUpdate(System.currentTimeMillis());
        copyDetail.setLastPublished(null);
        copyDetail.create();
        return copyId;
    }

    public Map<String, List<Arrangement.Error>> checkArrangementLegal(String arrangementId){
        val arrangement = Arrangement.Detail.loadDraft(arrangementId);

        val notSetTaskDefList = taskDefService.listNotSetTaskDefList(arrangementId)
                .stream().map(it->it.getTaskDefId()).collect(Collectors.toList());

        Map<String, List<Arrangement.Error>> nodeIdToError = Maps.map();
        int startCount = 0;
        int endCount = 0;
        for(Arrangement.Node node : arrangement.getNodes()){
            List<Arrangement.Error> errors = Lists.list();
            switch (node.getType()){
                case START: {
                    startCount++;
                    if(node.getNextNodeId().isEmpty()){
                        errors.add(Arrangement.Error.NODE_WITHOUT_OUTFLOW);
                    }
                    break;
                }
                case END: {
                    endCount++;
                    if(node.getPreviousNodeId().isEmpty()){
                        errors.add(Arrangement.Error.NODE_WITHOUT_INFLOW);
                    }
                    break;
                }
                case TASK:{
                    if(node.getPreviousNodeId().isEmpty()){
                        errors.add(Arrangement.Error.NODE_WITHOUT_INFLOW);
                    }
                    if(node.getNextNodeId().isEmpty()){
                        errors.add(Arrangement.Error.NODE_WITHOUT_OUTFLOW);
                    }
                    if(notSetTaskDefList.contains(node.getTaskDefId())){
                        errors.add(Arrangement.Error.NODE_NOT_SET);
                    }
                    break;
                }
                case CONDITION:{
                    if(node.getPreviousNodeId().isEmpty()){
                        errors.add(Arrangement.Error.NODE_WITHOUT_INFLOW);
                    }
                    if(node.getConditionToNextNodeId().size() < 1){
                        errors.add(Arrangement.Error.NODE_NOT_SET);
                    }
                    for(Arrangement.NodeCondition nodeCondition : node.getConditionToNextNodeId()){
                        if(nodeCondition.getNextNodeId().isEmpty()){
                            errors.add(Arrangement.Error.IF_ELSE_WITHOUT_OUTFLOW);
                            break;
                        }
                    }
                    break;
                }
            }
            if(!errors.isEmpty()){
                nodeIdToError.put(node.getId(), errors);
            }
        }
        if(!(startCount > 0)){
            nodeIdToError.put(Arrangement.NodeType.START.name(),
                    Lists.list(Arrangement.Error.MISSING_START_NODE));
        }
        if(!(endCount > 0)){
            nodeIdToError.put(Arrangement.NodeType.END.name(),
                    Lists.list(Arrangement.Error.MISSING_END_NODE));
        }
        return nodeIdToError;
    }

    public boolean checkNodeDeletable(String arrangementId, String taskDefId){
        val taskDefDetailList = TaskDef.Detail.listDraft(arrangementId);
        return !taskDefDetailList.stream().filter(it->taskDefId.equals(it.getDsTaskDefId())).findAny().isPresent();
    }

    @PaasTransactional
    public void delete(String arrangementId) {
        val arrangement = Arrangement.loadDraft(arrangementId);
        if(!Arrangement.Status.DRAFT.equals(arrangement.getStatus())){
            boolean referenced = ArrangementReferenced.referenced(arrangementId);
            if(referenced){
                throw new ServerException("arrangement referenced");
            }
        }
        arrangement.delete();
        Arrangement.Detail.loadDraft(arrangementId).delete();
        taskDefService.deleteByArrangement(arrangementId);
    }

    public List<ArrangementMatchConditionDto> availableConditions(ArrangementType type) {
        return Arrays.stream(EnvironmentContext.values()).filter(it->it.getType().equals(type)).map(it-> ArrangementMatchConditionDto.fromEnvironmentContext(it))
                .collect(Collectors.toList());
    }

    @PaasTransactional
    public void recover(String arrangementId) {
        val draft = Arrangement.loadDraft(arrangementId);
        if(Arrangement.Status.DRAFT.equals(draft.getStatus())){
            draft.delete();
        }else if(Arrangement.Status.PUBLISHED.equals(draft.getStatus())){
            //do nothing
        }else if(Arrangement.Status.RE_DRAFT.equals(draft.getStatus())){
            draft.delete();
            val published = Arrangement.loadPublished(arrangementId);
            published.setStatus(Arrangement.Status.PUBLISHED);
            published.update();
        }else{
            throw new ServerException();
        }

        val detailDraft = Arrangement.Detail.loadDraft(arrangementId);
        if(Arrangement.Status.DRAFT.equals(detailDraft.getStatus())){
            detailDraft.delete();
        }else if(Arrangement.Status.PUBLISHED.equals(detailDraft.getStatus())){
            //do nothing
        }else if(Arrangement.Status.RE_DRAFT.equals(detailDraft.getStatus())){
            detailDraft.delete();
            val published = Arrangement.Detail.loadPublished(arrangementId);
            published.setStatus(Arrangement.Status.PUBLISHED);
            published.update();
        }else{
            throw new ServerException();
        }
        taskDefService.recover(arrangementId);
    }

    public Arrangement loadPublished(String arrangementId) {
        return Arrangement.loadPublished(arrangementId);
    }

    public List<String> fetchPreviousNodes(String arrangementVid, String arrangementNodeId) {
        Arrangement.Detail arrangement = Arrangement.Detail.loadVersion(arrangementVid);
        return arrangement.getNodes().stream().filter(it->it.getId().equals(arrangementNodeId))
                .findFirst().get().getPreviousNodeId();
    }

    public List<String> fetchNextNodes(String arrangementVid, String arrangementNodeId) {
        Arrangement.Detail arrangement = Arrangement.Detail.loadVersion(arrangementVid);
        return arrangement.getNodes().stream().filter(it->it.getId().equals(arrangementNodeId))
                .findFirst().get().getNextNodeId();
    }

    public List<KeyValue> preLoadDataFunctions() {
        return PreLoadDataInterface.implementations.values().stream().map(it->it.textToValue()).collect(Collectors.toList());
    }

    public void reference(String arrangementId){
        new ArrangementReferenced(arrangementId).reference();
    }

    public Map<String, List<String>> fetchAllNodeNames(String arrangementVid) {
        val names = Arrangement.Detail.loadVersion(arrangementVid).getNodes()
                .stream().map(it-> Pair.pair(it.getId(), Lists.list(it.getTitle(), String.valueOf(it.getType())))).collect(Collectors.toList());
        return Maps.map(names);
    }

    public List<SimpleTree> fetchTaskRuleTreePublished(String arrangementId) {
        val tasks = Arrangement.Detail.loadPublished(arrangementId).getNodes().stream()
                .filter(it->StringUtils.isNotEmpty(it.getTaskDefId()))
                .map(it->new SimpleTree(it.getTaskDefId(), it.getTitle(), Lists.list())).collect(Collectors.toList());
        tasks.forEach(task->{
            task.setChildren(TaskDef.Rule.listPublished(task.getId()).stream()
                    .map(it->new SimpleTree(it.getRuleId(), it.getName(), Lists.list())).collect(Collectors.toList()));
        });
        return tasks;
    }

    public List<SimpleTree> fetchTaskRuleTreeDraft(String arrangementId) {
        val tasks = Arrangement.Detail.loadDraft(arrangementId).getNodes().stream()
                .filter(it->StringUtils.isNotEmpty(it.getTaskDefId()))
                .map(it->new SimpleTree(it.getTaskDefId(), it.getTitle(), Lists.list())).collect(Collectors.toList());
        tasks.forEach(task->{
            task.setChildren(TaskDef.Rule.listDraft(task.getId()).stream()
                    .map(it->new SimpleTree(it.getRuleId(), it.getName(), Lists.list())).collect(Collectors.toList()));
        });
        return tasks;
    }

    public List<Arrangement.Detail> listDetailPublished(List<String> arrangementIds) {
        val detail = Arrangement.Detail.listPublished(arrangementIds);
        return detail;
    }

    public Arrangement.Detail loadDetailVersion(String arrangementVerId) {
        val detail = Arrangement.Detail.loadVersion(arrangementVerId);
        return detail;
    }
}

akka {
#   loglevel = debug

   actor {
      provider = cluster
      serializers {
                  remote-serializer = "com.caidaocloud.remote.akka.AkkaSerializer"
          }

          serialization-bindings {
                  "com.caidaocloud.compute.remote.framework.serialize.RemoteSerializable" = remote-serializer
          }
    }
  remote {
    artery {
      canonical.hostname = "127.0.0.1"
      canonical.port = 25252
    }
  }
#   cluster {
#     seed-nodes = ["akka://COMPUTE@127.0.0.1:25251"]
#     downing-provider-class = "akka.cluster.sbr.SplitBrainResolverProvider"
#   }
}


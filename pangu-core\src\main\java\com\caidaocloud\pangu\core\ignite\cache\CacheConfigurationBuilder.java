package com.caidaocloud.pangu.core.ignite.cache;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Types;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.cache.configuration.Factory;

import com.caidaocloud.hrpaas.metadata.sdk.util.SnakeCaseConvertor;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.ignite.DatasourceProperties;
import com.caidaocloud.pangu.core.ignite.PgsqlDbContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import com.caidaocloud.pangu.core.ignite.store.CustomBlobStoreFactory;
import com.caidaocloud.pangu.core.ignite.store.CustomPojoStoreFactory;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.annotations.QuerySqlField;
import org.apache.ignite.cache.store.CacheStore;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.configuration.CacheConfiguration;

/**
 *
 * <AUTHOR> Zhou
 * @date 2025/1/9
 */
@Data
@Accessors(chain = true)
public  class CacheConfigurationBuilder<K,V>  implements Serializable {
	private String identifier;

	private String cacheName;

	private Class<V> clazz;

	private Field key;

	private DatasourceProperties datasourceProperties;

	private DynamicPojoDefinition dynamicPojoDefinition;

public 	CacheConfigurationBuilder<K, V> dynamicPojoDefinition(DynamicPojoDefinition dynamicPojoDefinition) {
		this.dynamicPojoDefinition = dynamicPojoDefinition;
		return this;
	}

	public CacheConfigurationBuilder<K, V> identifier(String identifier) {
		this.identifier = identifier;
		return this;
	}

	public CacheConfigurationBuilder<K, V> cacheName(String cacheName) {
		this.cacheName = cacheName;
		return this;
	}

	public CacheConfigurationBuilder<K, V> key(Field key) {
		this.key = key;
		return this;
	}

	public CacheConfigurationBuilder<K, V> datasourceProperties(DatasourceProperties datasourceProperties) {
		this.datasourceProperties = datasourceProperties;
		return this;
	}

	public CacheConfigurationBuilder(Class<V> clazz) {
		this.clazz = clazz;
	}

	public CacheConfiguration build(){
		CacheConfiguration cacheConfiguration = new CacheConfiguration();
		if (clazz == null) {
			throw new IllegalArgumentException("缓存对象clazz为空");
		}
		if (key == null) {
			throw new IllegalArgumentException("缓存对象key为空");
		}
		if (StringUtils.isEmpty(cacheName)) {
			if (StringUtils.isEmpty(identifier)) {
				throw new IllegalArgumentException("缓存name为空");
			}
			String prefix = StringUtils.substringAfterLast(identifier, ".");
			cacheName = prefix + SnowUtil.nextId();
		}
		cacheConfiguration.setName(cacheName);
		if (StringUtils.isNotEmpty(identifier)) {
			dynamicPojoDefinition.setDatasourceProperties(datasourceProperties);
			cacheConfiguration.setCacheStoreFactory(buildJdbcFactory());
		}else{
			cacheConfiguration.setStoreKeepBinary(true);
			dynamicPojoDefinition.setTableName(clazz.getSimpleName());
			cacheConfiguration.setCacheStoreFactory(buildBlobFactory(cacheConfiguration));

		}
		cacheConfiguration.setQueryEntities(Lists.list(buildQueryEntity()));


		return cacheConfiguration;
	}

	private Factory<? extends CacheStore> buildBlobFactory(CacheConfiguration cacheConfiguration) {
		CustomBlobStoreFactory<Object, Object> factory = new CustomBlobStoreFactory<>();
		factory.setPojoDef(dynamicPojoDefinition);
		// factory.setDialect(new BasicJdbcDialect());
		// factory.setDataSourceFactory(dynamicPojoDefinition.dsf());

		PgsqlDbContext dbContext = new PgsqlDbContext(datasourceProperties.getJdbcUrl(), datasourceProperties.getUsername(), datasourceProperties.getPassword());
		factory.setDbContext(dbContext);
		factory.setCacheConfiguration(cacheConfiguration);

		// factory.setConnectionUrl(datasourceProperties.getJdbcUrl());
		// factory.setUser(datasourceProperties.getUsername());
		// factory.setPassword(datasourceProperties.getPassword());
		//
		//
		// factory.setLoadQuery(dbContext.generateLoadSql(cacheName));
		// factory.setDeleteQuery(dbContext.generateDeleteSql(cacheName));
		// factory.setInsertQuery(dbContext.generateInsertSql(cacheName));
		// factory.setUpdateQuery(dbContext.generateUpdateSql(cacheName));
		// factory.setCreateTableQuery(dbContext.generateCreateTableSql(cacheName));
		return factory;
	}

	private QueryEntity buildQueryEntity() {
		QueryEntity qryEntity = new QueryEntity();

		qryEntity.setKeyType(key.getType().getName());
		qryEntity.setValueType(clazz.getName());
		qryEntity.setKeyFieldName(key.getName());

		Set<String> keyFields = new HashSet<>();
		keyFields.add(key.getName());
		qryEntity.setKeyFields(keyFields);

		LinkedHashMap<String, String> fields = new LinkedHashMap<>();
		for (Field field : clazz.getDeclaredFields()) {
			fields.put(field.getName(), field.getType().getName());
		}
		qryEntity.setFields(fields);

		return qryEntity;
	}


	private CacheJdbcPojoStoreFactory<K, V> buildJdbcFactory() {
		CustomPojoStoreFactory<K, V> factory = new CustomPojoStoreFactory();
		factory.setPojoDef(dynamicPojoDefinition);

		factory.setDialect(new BasicJdbcDialect());
		factory.setDataSourceFactory(dynamicPojoDefinition.dsf());

		JdbcType jdbcType = new JdbcType();
		jdbcType.setCacheName(cacheName);
		jdbcType.setDatabaseTable(convertTableName());
		jdbcType.setKeyType(key.getType());
		jdbcType.setValueType(clazz);

		jdbcType.setKeyFields(field2JdbcTypeField(key));

		List<JdbcTypeField> jdbcTypeFieldList = Arrays.stream(dynamicPojoDefinition.getFields()).map(f -> new JdbcTypeField(convert2JdbcType(f.getType()), f.getDbName(), f.getType(), f.getName()))
				.collect(Collectors.toList());
		jdbcType.setValueFields(jdbcTypeFieldList.toArray(new JdbcTypeField[0]));
		factory.setTypes(jdbcType);
		return factory;
	}

	private JdbcTypeField field2JdbcTypeField(Field field) {
		return new JdbcTypeField(convert2JdbcType(field.getType()), SnakeCaseConvertor.toSnake(field.getName()), field.getType(), field.getName());
	}

	private String convertTableName() {
		return SnakeCaseConvertor.toSnake(identifier) + "_" + SecurityUserUtil.getSecurityUserInfo().getTenantId();
	}


	private int convert2JdbcType(Class type){
		switch (type.getName()) {
		case "java.lang.Integer":
			return Types.INTEGER;
		case "java.lang.Long":
			return Types.BIGINT;
		case "java.lang.Double":
			return Types.DOUBLE;
		case "java.lang.Float":
			return Types.FLOAT;
		case "java.lang.String":
			return Types.VARCHAR;
		case "java.lang.Boolean":
			return Types.BOOLEAN;
		case "java.math.BigDecimal":
			return Types.DECIMAL;
		default:
			throw new IllegalArgumentException("Unknown Java type: " + type.getName());
		}
	}
}

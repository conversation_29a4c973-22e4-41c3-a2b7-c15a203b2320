package com.caidaocloud.pangu.domain.entity;

import java.util.List;
import java.util.Objects;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.pangu.domain.enums.Version;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.Data;

@Data
public class ApproveConfigDo extends DataSimple {

    private String schemaId;

    private String name;

    private EnumSimple version;

    private Boolean enabled;

    private String formId;

    @DisplayAsArray
    private List<String> items;


    public static  final   String APPROVE_CONFIG_IDENTIFIER = "entity.bonus.BonusApproveConfig";

    public ApproveConfigDo() {
        setIdentifier(APPROVE_CONFIG_IDENTIFIER);
        setEnabled(false);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApproveConfigDo that = (ApproveConfigDo) o;
        return Objects.equals(formId, that.formId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(formId);
    }

    public void create() {
        this.setCreateTime(System.currentTimeMillis());
        this.setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        this.setUpdateBy(this.getCreateBy());
        this.setUpdateTime(this.getCreateTime());
        DataInsert.identifier("entity.bonus.BonusApproveConfig")
                .insert(this);
    }

    public void update() {
        this.setUpdateTime(System.currentTimeMillis());
        this.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        DataUpdate.identifier("entity.bonus.BonusApproveConfig")
                .update(this);
    }

    void disable() {
        this.version = Version.SUSPEND.toValue();
        DataUpdate.identifier("entity.bonus.BonusApproveConfig")
                .update(this);
    }
}

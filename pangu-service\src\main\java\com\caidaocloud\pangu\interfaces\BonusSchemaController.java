package com.caidaocloud.pangu.interfaces;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.application.dto.BonusSchemaDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusSchemaDto;
import com.caidaocloud.pangu.application.service.BonusSchemaService;
import com.caidaocloud.pangu.infrastructure.util.LangUtil;
import com.caidaocloud.pangu.interfaces.vo.BonusSchemaVo;
import com.caidaocloud.pangu.interfaces.vo.BonusStructItemVo;
import com.caidaocloud.pangu.interfaces.vo.BonusStructVo;
import com.caidaocloud.pangu.interfaces.vo.MatchConditionVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/pangu/v1/bonus/schema")
@Api(value ="/api/pangu/v1/bonus/schema", tags = "奖金方案")
public class BonusSchemaController {

    @Autowired
    private BonusSchemaService service;

    @GetMapping("/one")
    @ApiOperation("奖金方案查看")
    public Result<BonusSchemaVo> load(@RequestParam String bid){
        return Result.ok(service.load(bid));
    }

    @PostMapping("/page")
    @ApiOperation("奖金方案列表")
    public Result<PageResult<BonusSchemaVo>> page(
            @RequestBody BasePage basePage) {
        return Result.ok(service.page(basePage));
    }
    @GetMapping("list")
    @ApiOperation("奖金结构下拉列表")
    public Result<List<BonusSchemaVo>> list(){
        List<BonusSchemaVo> list = service.list();
        return Result.ok(list);
    }

    @PostMapping("/create")
    @ApiOperation("新增奖金方案")
    public Result<String> create(@RequestBody BonusSchemaDto schema){
        schema.setName(schema.getName().trim());
        return Result.ok(service.create(schema));
    }

    @PostMapping("/update")
    @ApiOperation("更新奖金方案")
    public Result<Boolean> update(@RequestBody BonusSchemaDto schema){
        schema.setName(schema.getName().trim());
        service.update(schema);
        return Result.ok();
    }

    @PostMapping("/delete")
    @ApiOperation("删除奖金方案")
    public Result<Boolean> delete(@RequestBody BonusSchemaDeleteDto delete){
        service.delete(delete.getBid());
        return Result.ok();
    }

    @GetMapping("/condition/list")
    @ApiOperation("方案适用人群可匹配条件")
    public Result<List<MatchConditionVo>> conditionList(){
        return Result.ok(service.listMatchCondition());
    }


    @GetMapping("approve/item")
    @ApiOperation("审批汇总项目")
    public Result<List<BonusStructItemVo>> approveItems(@RequestParam String structId) {
        return Result.ok(service.listApproveItems(structId));
    }
}

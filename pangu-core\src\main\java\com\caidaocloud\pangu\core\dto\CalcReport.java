package com.caidaocloud.pangu.core.dto;

import com.caidaocloud.dto.KeyValue;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CalcReport {

    private String execSeqId;
    private String panguNodeId;
    private String execNodeSeqId;
    private boolean skip;

    @Data
    public static class Start extends CalcReport{
        private List<String> nextNodeIds = Lists.newArrayList();
    }

    @Data
    public static class TaskDataLoaded extends CalcReport{
        private long total;
        private int step;
        private String cacheId;
        private String resultCacheId;
    }


    @Data
    public static class Condition extends CalcReport{
        private List<String> skipNextNodes = Lists.newArrayList();
        private List<String> notSkipNextNodes = Lists.newArrayList();
    }

    @Data
    public static class End extends CalcReport{
    }

    @Data
    public static class Fail extends CalcReport{
        private String execSeqId;
        private String detail;
    }

    @Data
    public static class SubTask extends CalcReport{
        private int from;
        private int to;
    }

    @Data
    public static class SubTaskValue {
        private String property;
        private List<KeyValue> keys = Lists.newArrayList();
        private Object value;
    }

    @Data
    public static class PreLoad extends CalcReport{
        private List<String> nextNodeIds = Lists.newArrayList();
    }
}

package com.caidaocloud.pangu.infrastructure.ignite.store;

import com.caidaocloud.security.util.SecurityUserUtil;

/**
 *
 * <AUTHOR>
 * @date 2023/7/28
 */
public class PgsqlDbContext implements DbContext {
	private final String CREATE_TABLE_SQL_TEMPLATE = "CREATE TABLE IF NOT EXISTS \"%s_%s\" (\"akey\" bytea PRIMARY KEY NOT NULL,\"val\" bytea);";
	private final String UPDATE_SQL_TEMPLATE = "update \"%s_%s\" set val=? where akey=?";
	private final String INSERT_SQL_TEMPLATE = "insert into \"%s_%s\" (akey, val) values (?, ?)";
	private final String DELETE_SQL_TEMPLATE = "delete from \"%s_%s\" where akey=?";
	private final String LOAD_SQL_TEMPLATE = "select * from \"%s_%s\" where akey=?";

	private final String connUrl;
	private final String user;
	private final String pwd;

	public PgsqlDbContext(String connUrl, String user, String pwd) {
		this.connUrl = connUrl;
		this.user = user;
		this.pwd = pwd;
	}

	@Override
	public String getConnUrl() {
		return connUrl;
	}

	@Override
	public String getUser() {
		return user;
	}

	@Override
	public String getPwd() {
		return pwd;
	}

	public String generateCreateTableSql(String cacheName) {
		return generateSql(CREATE_TABLE_SQL_TEMPLATE, cacheName);
	}

	public String generateUpdateSql(String cacheName) {
		return generateSql(UPDATE_SQL_TEMPLATE, cacheName);
	}

	public String generateInsertSql(String cacheName) {
		return generateSql(INSERT_SQL_TEMPLATE, cacheName);
	}

	public String generateDeleteSql(String cacheName) {
		return generateSql(DELETE_SQL_TEMPLATE, cacheName);
	}

	public String generateLoadSql(String cacheName) {
		return generateSql(LOAD_SQL_TEMPLATE, cacheName);
	}

	private String generateSql(String template, String cacheName) {
		return String.format(template, cacheName, SecurityUserUtil.getSecurityUserInfo().getTenantId());
	}

	
}

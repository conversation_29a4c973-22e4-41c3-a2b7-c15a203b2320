package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorDataFilter;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Map;

public class FilterLt extends AbstractFunction {
    @Override
    public String getName() {
        return "FILTER_LT";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val property = FunctionUtils.getStringValue(aviatorObject1, env);
        Object value = aviatorObject2.getValue(env);
        if(value instanceof LocalDateTime){
            value = ((LocalDateTime)value).toEpochSecond(OffsetDateTime.now().getOffset()) * 1000;
        }
        val filter = DataFilter.lt(property, String.valueOf(value));
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(property).append(",").append(value).append(")=").append(FastjsonUtil.toJson(filter)).toString());
        }
        return new AviatorDataFilter(filter);
    }
}
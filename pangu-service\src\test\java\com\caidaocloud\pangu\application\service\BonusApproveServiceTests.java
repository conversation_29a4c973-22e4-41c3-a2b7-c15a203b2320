package com.caidaocloud.pangu.application.service;

import java.util.HashMap;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.pangu.application.dto.ApproveConfigDto;
import com.caidaocloud.pangu.application.dto.BonusApproveDto;
import com.caidaocloud.pangu.application.dto.BonusApproveSummaryDto;
import com.caidaocloud.pangu.application.dto.BonusSchemaDto;
import com.caidaocloud.pangu.infrastructure.repository.EmpRepository;
import com.caidaocloud.pangu.interfaces.vo.BonusSchemaVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/2/18
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class BonusApproveServiceTests {

	// @Autowired
	// private BonusApproveService bonusApproveService;
	@SpyBean
	private EmpRepository empRepository;
	@Autowired
	private BonusSchemaService bonusSchemaService;
	@Autowired
	private IWfRegisterFeign wfRegisterFeign;

	@Before
	public void bf(){
		SecurityUserInfo info = new SecurityUserInfo();
		info.setTenantId("11");
		info.setEmpId(****************L);
		info.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(info);
	}
	@Test
	public void APPROVE_CONFIG_SAVE_TEST(){
		BonusSchemaDto bonusSchemaDto = BONUS_SCHEMA_TEST_DATA();
		ApproveConfigDto approveConfigDo = new ApproveConfigDto();
		approveConfigDo.setEnabled(true);
		approveConfigDo.setItems(Lists.list("1825921469323296","1820265262676980"));
		approveConfigDo.setFormId("1847043729751081");
		bonusSchemaDto.setApproveConfig(approveConfigDo);
		// String bid = bonusSchemaService.update(bonusSchemaDto);
		// BonusSchemaVo update = bonusSchemaService.load(bid);
		// Assert.assertNotNull(update.getApproveConfig());
		// Assert.assertEquals(approveConfigDo, update.getApproveConfig());
	}

	private BonusSchemaDto BONUS_SCHEMA_TEST_DATA() {
		String json = "{\"name\":\"奖金方案01\",\"bonusStructBid\":\"1740158822692865\",\"coverage\":{\"id\":\"1692609711671_187\",\"type\":\"group\",\"relation\":\"or\",\"children\":[{\"id\":\"1692696838391_875\",\"type\":\"single\",\"relation\":null,\"children\":null,\"condition\":{\"name\":\"entity.hr.EmpPrivateInfo#name\",\"componentType\":\"STRING_INPUT\",\"symbol\":\"EQ\",\"value\":\"卢芳格\",\"simpleValue\":\"卢芳格\"}}],\"identifierEmpIdKey\":{}},\"description\":null,\"bid\":\"1740379006539778\"}";
		return FastjsonUtil.toObject(json, BonusSchemaDto.class);
	}
    //
	// @Test
	// public void APPROVE_START_TEST(){
	// 	EmpSimple empSimple = new EmpSimple();
	// 	empSimple.setEmpId("****************");
	// 	empSimple.setName("卢芳格");
	// 	Mockito.doAnswer(invocation -> empSimple).when(empRepository).loadEmp(Mockito.anyString());
    //
	// 	BonusApproveSummaryDto summary = bonusApproveService.getSummary("****************");
	// 	BonusApproveDto dto = new BonusApproveDto();
	// 	dto.setAccountId("****************");
	// 	dto.setSummary(summary);
	// 	dto.setFormData(new HashMap<>());
	// 	bonusApproveService.start(dto);
	// }
}
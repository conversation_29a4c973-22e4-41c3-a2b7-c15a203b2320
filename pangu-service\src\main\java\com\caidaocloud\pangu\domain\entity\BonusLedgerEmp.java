package com.caidaocloud.pangu.domain.entity;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 奖金计算员工
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
@NoArgsConstructor
public class BonusLedgerEmp extends DataSimple {

	/**
	 * 账套id
	 */
	private String ledgerId;

	/**
	 * 员工id
	 */
	private String empId;

	/**
	 * 计算状态
	 */
	private EnumSimple status;

	public static final String BONUS_LEDGER_EMP_IDENTIFIER = "entity.bonus.BonusLedgerEmp";

	public BonusLedgerEmp(String ledgerId, String empId) {
		this.ledgerId = ledgerId;
		this.empId = empId;

		EnumSimple status = new EnumSimple();
		status.setValue(BonusLedgerEmpCalcStatus.INIT.code);
		this.status = status;
	}
	//
	// public static List<BonusLedgerEmp> loadByAccountBidAndEmpId(List<String> accountBids, String empId) {
	// 	return SpringUtil.getBean(IBonusAccountRepository.class).loadAccountEmpByAccountBidAndEmpId(accountBids, empId);
	// }
	//
	// @Override
	// public boolean equals(Object o) {
	// 	if (this == o) return true;
	// 	if (o == null || getClass() != o.getClass()) return false;
	// 	BonusLedgerEmp that = (BonusLedgerEmp) o;
	// 	return Objects.equals(accountId, that.accountId) && Objects.equals(empId, that.empId);
	// }
	//
	// @Override
	// public int hashCode() {
	// 	return Objects.hash(accountId, empId);
	// }

}

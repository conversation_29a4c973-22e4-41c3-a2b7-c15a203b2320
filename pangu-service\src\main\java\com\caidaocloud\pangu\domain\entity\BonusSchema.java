package com.caidaocloud.pangu.domain.entity;

import java.util.List;
import java.util.Optional;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.domain.enums.Version;
import com.caidaocloud.pangu.domain.repository.IBonusSchemaRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructRepository;
import com.caidaocloud.pangu.domain.repository.IConditionRepository;
import com.caidaocloud.pangu.interfaces.vo.BonusSchemaVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class BonusSchema extends DataSimple {

    private String name;

    private String structId;

    @DisplayAsObject
    private ConditionTree coverage;

    private EnumSimple month;

    private Integer day;

    private ApproveConfigDo approveConfig;

    // 选人周期
    private PeriodDate selection;

    // 特殊条件
    private Boolean special;

    // 入职日期
    private PeriodDate hireDate;

    // 离职日期
    private PeriodDate terminationDate;

    private String remark;

    public static final String BONUS_SCHEMA_IDENTIFIER = "entity.bonus.BonusSchema";

    public BonusSchema() {
        setIdentifier(BONUS_SCHEMA_IDENTIFIER);
    }

    public static BonusSchema load(String bid){
        BonusSchema schema = SpringUtil.getBean(IBonusSchemaRepository.class).selectById(bid,BONUS_SCHEMA_IDENTIFIER);
        schema.setApproveConfig(Optional.ofNullable(loadApproveConfig(bid)).orElse(new ApproveConfigDo()));
        return schema;
    }


    public static PageResult<BonusSchema> page(BasePage basePage) {
        return SpringUtil.getBean(IBonusSchemaRepository.class).selectPage(basePage);
    }

    public static List<BonusSchema> list() {
          return SpringUtil.getBean(IBonusSchemaRepository.class)
                .selectList(BONUS_SCHEMA_IDENTIFIER, SecurityUserUtil.getSecurityUserInfo()
                        .getTenantId());
    }

	public String create() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
        SpringUtil.getBean(IBonusSchemaRepository.class).insert(this);
        saveApproveConfig();
        return this.getBid();
    }

    public void update(){
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        SpringUtil.getBean(IBonusSchemaRepository.class).updateById(this);
        updateApproveConfig();
    }


    private void saveApproveConfig() {
        if (getApproveConfig() == null) {
            return;
        }
        ApproveConfigDo config = ObjectConverter.convert(getApproveConfig(), ApproveConfigDo.class);
        config.setSchemaId(getBid());
        config.setName(getName());
        config.setVersion(Version.ENABLE.toValue());
        config.create();
    }

    private void updateApproveConfig() {
        if (getApproveConfig() == null) {
            return;
        }
        ApproveConfigDo exist = loadApproveConfig(getBid());
        if (ObjectUtil.nullSafeEquals(getApproveConfig(), exist)) {
            BeanUtil.copyWithNoValue(getApproveConfig(), exist);
            exist.update();
        }
        else {
            if (exist != null) {
                exist.disable();
                String name =getName() + "_" + DateUtil.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
                if (name.length()>50) {
                    name = name.substring(0, 50);
                }
                setName(name);
            }
            saveApproveConfig();
        }
    }

    private static ApproveConfigDo loadApproveConfig(String bid) {
        return SpringUtil.getBean(IBonusSchemaRepository.class).loadApproveConfigBySchemaId(bid);
    }

    public  void delete(){
        SpringUtil.getBean(IBonusSchemaRepository.class).delete(this);
    }

    public List<EmpSimple> fetchEmpByCoverage() {
        return SpringUtil.getBean(IConditionRepository.class).fetchMatchedEmp(coverage);
    }
}

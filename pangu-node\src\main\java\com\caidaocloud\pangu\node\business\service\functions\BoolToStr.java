package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;

import java.util.Map;

public class BoolToStr extends AbstractFunction {
    @Override
    public String getName() {
        return "BOOL_TO_STR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1) {
        if(Boolean.TRUE == aviatorObject1.getValue(env)){
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(true).append(")=TRUE").toString());
            }
            return new AviatorString("TRUE");
        }else{
            if(FunctionLogTool.logEnabled()){
                FunctionLogTool.log(new StringBuilder(getName()).append("(").append(false).append(")=FALSE").toString());
            }
            return new AviatorString("FALSE");
        }
    }
}

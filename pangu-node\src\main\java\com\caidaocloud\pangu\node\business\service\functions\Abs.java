package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class Abs extends AbstractFunction {
    @Override
    public String getName() {
        return "ABS";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject) {
        BigDecimal bigDecimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
        val result = new AviatorDecimal(bigDecimal.abs());
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(bigDecimal).append(")=").append(result.getValue(env)).toString());
        }

        return result;
    }
}

package com.caidaocloud.compute.remote.framework.core;

import java.util.Optional;

import javax.annotation.Resource;

import com.caidaocloud.compute.remote.framework.actor.ActorFactory;
import com.caidaocloud.compute.remote.framework.actor.ActorInfo;
import com.caidaocloud.compute.remote.framework.core.InitializerConfig;
import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.compute.remote.framework.core.RequestHandlerGenerator;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Configuration
@EnableConfigurationProperties
public class RemoteHandlerProcessor implements  BeanPostProcessor {

	@Resource
	private RemoteInitializer remoteInitializer;

	@Bean
	@ConditionalOnMissingBean
	public InitializerConfig initializerConfig() {
		return new InitializerConfig();
	}

	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		// 检查 Bean 类是否带有 @ServiceApi 注解
		Optional<ActorInfo> actorInfo = ActorFactory.buildActor(bean);
		if (actorInfo.isPresent()) {
			// 注册 Bean 对应的 Actor 到 Akka
			remoteInitializer.initHandler(actorInfo.get());
		}
		return bean;
	}
}

package com.caidaocloud.compute.remote.framework.actor;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface RemoteRequest {
	/**
	 * handler path
	 * @return handler path
	 */
	String value() default "";
}

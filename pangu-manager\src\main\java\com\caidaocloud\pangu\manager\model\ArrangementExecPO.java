package com.caidaocloud.pangu.manager.model;

import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

@TableName("arrangement_exec")
@Data
public class ArrangementExecPO {

    @TableId
    private String id;

    public static final String identifier = "entity.pangu.ArrangementExecPO";

    private String arrangementId;

    private String arrangementVid;

    private String arrangementName;

    private String workNo;

    private String empName;

    private String execSeqId;

    private ArrangementExec.Status status;

    private String context;

    private long waitFrom;

    private long started;

    private long ended;

    private String actualTenantId;

    private ArrangementType type;

    private long createTime;

    // 转换方法：从 ArrangementExec 转换为 ArrangementExecPO
    public static ArrangementExecPO fromArrangementExec(ArrangementExec exec) {
        ArrangementExecPO po = ObjectConverter.convert(exec, ArrangementExecPO.class);
        po.setContext(FastjsonUtil.toJson(exec.getContext())); // 假设 context 是 String 类型
        return po;
    }

    // 转换方法：从 ArrangementExecPO 转换为 ArrangementExec
    public ArrangementExec toArrangementExec() {
        ArrangementExec exec = ObjectConverter.convert(this, ArrangementExec.class);
        exec.setContext(FastjsonUtil.toObject(getContext(), Map.class)); // 假设 context 是 String 类型
        return exec;
    }
} 
package pangu;

import com.caidaocloud.pangu.manager.model.ArrangementExec;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExec;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExecPO;
import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2025/2/28
 */
public class PojoTest {
	@Test
	public  void test(){
		ArrangementNodeExec exec = new ArrangementNodeExec();
		exec.setSkip(true);
		Assert.assertEquals(true, ArrangementNodeExecPO.fromArrangementNodeExec(exec).isSkip());
		exec.setSkip(false);
		Assert.assertEquals(false, ArrangementNodeExecPO.fromArrangementNodeExec(exec).isSkip());
	}
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.util.Map;

public class GetVar extends AbstractFunction {
    @Override
    public String getName() {
        return "GET_VAR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1) {
        val name = FunctionUtils.getStringValue(aviatorObject1, env);
        val value = ExpressionTool.getParam(name);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(name).append(")=").append(value.getValue(env)).toString());
        }
        return value;
    }
}
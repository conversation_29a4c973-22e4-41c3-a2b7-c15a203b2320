package com.caidaocloud.pangu.application.service;

import java.util.*;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.pangu.application.dto.*;
import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.feign.ComposeFeign;
import com.caidaocloud.pangu.domain.BonusStructFactory;
import com.caidaocloud.pangu.domain.entity.*;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.interfaces.vo.*;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import static com.caidaocloud.pangu.application.constant.BonusConstant.BONUS_INCR_REDIS_KEY;

@Service
public class BonusStructService {
    @Autowired
    private MetadataOperatorService metadataOperatorService;
    @Autowired
    private ComposeFeign composeFeign;
    @Autowired
    private RedisTemplate redisTemplate;

    public BonusStructVo load(String bid){
        return ObjectConverter.convert(BonusStruct.load(bid), BonusStructVo.class);
    }

    public PageResult<BonusStructVo> page(BonusStructQueryDto queryDto){
        PageResult<BonusStruct> page = BonusStruct.page(queryDto, queryDto.getKeyword());
        Map<String, List<BonusStructItem>> itemMap = Sequences.sequence(BonusStruct.listItems(Sequences.sequence(page.getItems()).map(BonusStruct::getBid).toList())).toMap(BonusStructItem::getStructId);
        List<BonusStructVo> list = Sequences.sequence(page.getItems()).map(i -> {
            BonusStructVo vo = ObjectConverter.convert(i, BonusStructVo.class);
            List<String> itemNames = Sequences.sequence(itemMap.getOrDefault(i.getBid(), Collections.emptyList())).map(BonusStructItem::getName).toList();
            vo.setBonusItemsOverview(StringUtils.join(itemNames, ","));
            return vo;
        }).toList();
        return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    @PaasTransactional
    public String create(BonusStructDto struct){
        BonusStruct bonusStruct = BonusStructFactory.create(struct.getI18nName());
        return bonusStruct.create();
    }

    @PaasTransactional
    public String copy(BonusStructDto struct){
        BonusStruct bonusStruct = BonusStruct.load(struct.getBid());
        List<BonusStructItem> itemList = bonusStruct.listItems();
        List<BonusStructReportItem> reportItemList = bonusStruct.listReportItems();
        List<BonusStructCompose> composeList = bonusStruct.listCompose();

        BonusStruct newStruct = BonusStructFactory.create(struct.getI18nName());
        newStruct.setExecutionType(bonusStruct.getExecutionType());
        String bid = newStruct.create();

        List<Pair<Integer, String>> newReportItemIds = new ArrayList<>();
        for (BonusStructItem item : itemList) {
            String originBid = item.getBid();
            item.setId(null);
            item.setBid(null);
            item.setStructId(bid);
            String newBid = newStruct.addItem(item);

            int originOrder = Sequences.sequence(reportItemList).find(reportItem -> reportItem.getItemId().equals(originBid)).map(BonusStructReportItem::getSort).getOrThrow(new ServerException("bonus item do not exist"));
            newReportItemIds.add(Pair.pair(originOrder, newBid));
        }
        newReportItemIds.sort(Comparator.comparingInt(Pair::first));
        newStruct.saveReportSettingOrder(bonusStruct.getBasic(), newReportItemIds.stream().map(Pair::second).collect(Collectors.toList()));

        for (BonusStructCompose compose : composeList) {
            compose.setId(null);
            compose.setBid(null);
            compose.setStructId(bid);
            compose.create();
        }
        return bid;
    }

    @PaasTransactional
    public void update(BonusStructDto struct){
        val existed = BonusStruct.load(struct.getBid());
        existed.setI18nName(struct.getI18nName());
        existed.setName(struct.getI18nName().get("default"));
        if (struct.getExecutionType() != null) {
            existed.setExecutionType(struct.getExecutionType().toValue());
        }
        existed.update();
    }

    @PaasTransactional
    public void delete(String bid){
        val referred = DataQuery.identifier(BonusSchema.BONUS_SCHEMA_IDENTIFIER)
                .count(DataFilter.eq("structId", bid),
                        System.currentTimeMillis()) > 0;
        if(referred){
            throw new ServerException("计件工资结构已被引用");
        }
        BonusStruct.load(bid).delete();
    }

    public PageResult<BonusStructItemVo> pageItems(BonusStructItemQueryDto queryDto){
        PageResult<BonusStructItem> page = BonusStruct.pageItems(queryDto, queryDto.getStructId(), queryDto.getKeyword());
        List<BonusStructItemVo> list = ObjectConverter.convertList(page.getItems(), BonusStructItemVo.class);
        return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    //
    @PaasTransactional
    public void addItem(BonusStructItemDto dto) {
        BonusStructItem item = BonusStructFactory.createItem(dto, redisTemplate.opsForValue().increment(BONUS_INCR_REDIS_KEY + dto.getStructId(), 1));
        BonusStruct.load(dto.getStructId()).addItem(item);
    }
    //
    @PaasTransactional
    public void updateItem(BonusStructItemDto item){
        val existed = BonusStruct.loadItem(item.getBid());
        BeanUtil.copyProperties(item, existed);
        existed.update();
    }
    //
    @PaasTransactional
    public void deleteItem(String structBid, String itemBid){
       BonusStruct.loadItem(itemBid).delete();
    }
    //
    public BonusStructItemVo loadItem(String itemBid){
        return ObjectConverter.convert(BonusStruct.loadItem(itemBid), BonusStructItemVo.class);
    }

    public BonusReportSettingVo loadReportSetting(String structId) {
        BonusStruct bonusStruct = BonusStruct.load(structId);
        MetadataVo workInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpWorkInfo");
        MetadataVo privateInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpPrivateInfo");
        Map<String, MetadataVo> map = Maps.map("entity.hr.EmpWorkInfo", workInfo, "entity.hr.EmpPrivateInfo", privateInfo);
        List<MetadataPropertyVo> basicProperties = Sequences.sequence(bonusStruct.getBasic()).map(b -> {
            String[] s = b.split("@");
            MetadataPropertyVo vo = Sequences.sequence(map.get(s[0]).fetchAllProperties()).find(p -> p.getProperty().equals(s[1])).getOrNull();
            if (vo != null){
                vo.setProperty(b);
            }
            return vo;
        }).filter(Objects::nonNull).toList();
        List<BonusStructItem> allItems = bonusStruct.listItems();
        List<BonusStructReportItem> reportItems = bonusStruct.listReportItems();
        List<BonusStructItemVo> voList = Sequences.sequence(reportItems).map(item -> Sequences.sequence(allItems).find(i -> i.getBid().equals(item.getItemId())).map(i -> ObjectConverter.convert(i, BonusStructItemVo.class)).getOrNull()).filter(Objects::nonNull).toList();
        return new BonusReportSettingVo(basicProperties, voList);
    }


    @PaasTransactional
    public void saveReportSetting(BonusStructReportSettingDto dto) {
        BonusStruct bonusStruct = BonusStruct.load(dto.getStructId());
        bonusStruct.saveReportSetting(dto.getBasic(), dto.getItem());
    }

    public BonusReportSettingAllVo loadALlReportSetting(String structId) {
        BonusStruct bonusStruct = BonusStruct.load(structId);
        MetadataVo workInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpWorkInfo");
        workInfo.setStandardProperties(workInfo.fetchAllProperties());
        for (MetadataPropertyVo propertyVo : workInfo.getStandardProperties()) {
            propertyVo.setProperty(workInfo.getIdentifier() + "@" + propertyVo.getProperty());
        }
        MetadataVo privateInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpPrivateInfo");
        privateInfo.setStandardProperties(privateInfo.fetchAllProperties());
        for (MetadataPropertyVo propertyVo : privateInfo.getStandardProperties()) {
            propertyVo.setProperty(privateInfo.getIdentifier() + "@" + propertyVo.getProperty());
        }

        List<BonusStructItem> allItems = bonusStruct.listItems();
        List<BonusStructItemVo> voList = Sequences.sequence(allItems).map(i -> ObjectConverter.convert(i, BonusStructItemVo.class)).toList();
        return new BonusReportSettingAllVo(ObjectConverter.convert(workInfo,BonusMetadataVo.class), ObjectConverter.convert(privateInfo,BonusMetadataVo.class), voList);
    }

    @PaasTransactional
    public void modifyExecutionType(String structId, ExecutionType executionType) {
        BonusStruct bonusStruct = BonusStruct.load(structId);
        BonusStructDto dto = ObjectConverter.convert(bonusStruct, BonusStructDto.class);
        dto.setExecutionType(executionType);
        update(dto);
    }

    @PaasTransactional
    public void saveReportSettingOrder(BonusStructReportSettingDto dto) {
        BonusStruct bonusStruct = BonusStruct.load(dto.getStructId());
        bonusStruct.saveReportSettingOrder(dto.getBasic(), dto.getItem());
    }

    public List<BonusStructComposeVo> listComposeItems(String structId) {
        List<BonusStructCompose> list = BonusStruct.listComposeItems(structId);
        List<ComposeDefDto> composeDefs =
                composeFeign.listComposeDef(Sequences.sequence(list)
                .map(BonusStructCompose::getBid).toList()).getData();
        return Sequences.sequence(list).map(i -> {
            BonusStructComposeVo vo = ObjectConverter.convert(i, BonusStructComposeVo.class);
            ComposeDefDto defDto = Sequences.sequence(composeDefs)
                    .find(def -> def.getArrangementId().equals(vo.getComposeId()))
                    .getOrElse(ComposeDefDto::new);
            BeanUtil.copyProperties(defDto, vo);
            return vo;
        }).toList();
    }

    @PaasTransactional
    public void saveComposeItem(BonusStructComposeDto dto) {
        BonusStruct struct = BonusStruct.load(dto.getStructId());
        struct.addCompose(dto.getComposeId());
        composeFeign.reference(dto.getComposeId());
    }


    @PaasTransactional
    public void saveComposeItemOrder(BonusStructComposeOrderDto dto) {
        BonusStruct.load(dto.getStructId()).saveComposeOrder(dto.getComposeItemId());
    }

    @PaasTransactional
    public void updateComposeItem(BonusStructComposeDto dto) {
        val existed = BonusStruct.loadCompose(dto.getBid());
        BeanUtil.copyProperties(dto, existed);
        existed.update();
    }


    @PaasTransactional
    public void deleteComposeItem(String composeId) {
        BonusStruct.loadCompose(composeId).delete();
    }

    public List<BonusStructVo> list() {
        return ObjectConverter.convertList(BonusStruct.list(), BonusStructVo.class);
    }
}

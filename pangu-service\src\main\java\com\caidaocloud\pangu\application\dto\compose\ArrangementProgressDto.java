package com.caidaocloud.pangu.application.dto.compose;

import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 编排执行进度DTO
 * 
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArrangementProgressDto {

    /**
     * 总节点数
     */
    private Integer totalNodes;
    
    /**
     * 已完成节点数
     */
    private Integer completedNodes;

    @ApiModelProperty("执行状态")
    private BonusLedgerStepStatus status;

    @ApiModelProperty("执行时间(毫秒)")
    private long cost;

    @ApiModelProperty("当前任务名称")
    private String name;
}
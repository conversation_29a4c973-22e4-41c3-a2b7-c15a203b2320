package com.caidaocloud.pangu.node.business.service.load;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataTruncate;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.feign.AttendanceWfmFeign;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class PreLoadWfmProcessData implements PreLoadDataInterface{

    private String identifier = "entity.pangu.WfmProcessData";

    @Autowired
    private AttendanceWfmFeign attendanceWfmFeign;

    @Override
    public KeyValue textToValue() {
        return new KeyValue("工序产出", "WFM_PROCESS");
    }

    @Override
    public void exec(Map<String, String> context) {
        int pageNo = 1;
        val accountId = context.get("$.env."+ EnvironmentContext.BONUS_ACCOUNT_ID);
        DataDelete.identifier(identifier).batchDelete(DataFilter.eq("accountId", accountId));
        //DataTruncate.identifier(identifier).truncate();
        while(true){
            log.info("PreLoadWfmProcessData call " +
                    Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_START)) + ","
                    +Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_END)) + ","+pageNo+","+accountId);
            val page = attendanceWfmFeign
                    .getArrangeMonthAnalyzeList(
                            Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_START)),
                            Long.valueOf(context.get("$.env."+ EnvironmentContext.BONUS_CALC_END)),
                            pageNo, 100,
                            accountId).getData();
            pageNo++;
            if(page.getItems().isEmpty()){
                return;
            }
            page.getItems().forEach(it-> {
                it.setIdentifier(identifier);
                it.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            });
            DataInsert.identifier(identifier).batchInsert(page.getItems());
            if(page.getPageNo() * page.getPageSize() >= page.getTotal()){
                return;
            }
        }

    }
}

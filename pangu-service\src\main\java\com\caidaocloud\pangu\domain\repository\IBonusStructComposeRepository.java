package com.caidaocloud.pangu.domain.repository;

import com.caidaocloud.pangu.domain.entity.BonusStructCompose;
import com.caidaocloud.pangu.domain.entity.BonusStructReportItem;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusStructComposeRepository extends BaseRepository<BonusStructCompose> {
    List<BonusStructCompose> selectList(String structId);
}

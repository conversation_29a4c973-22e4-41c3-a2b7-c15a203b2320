package com.caidaocloud.pangu.manager.service;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.core.dto.NodeSyncStatus;
import com.caidaocloud.pangu.core.dto.RegisterResult;
import com.caidaocloud.pangu.core.dto.ServerInstance;
import com.caidaocloud.pangu.manager.dto.InvalidNodeDto;
import com.caidaocloud.pangu.manager.model.Node;
import com.caidaocloud.pangu.manager.util.DispatchTool;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NodeManagerService {

    private List<Node> nodeList = Lists.list();

    private ApplicationContext context = null;

    private boolean init = false;

    @Autowired
    private DispatchTool dispatchTool;

    public void initNodeList(ApplicationContext context){
        nodeList.addAll(Node.fetchNodeList());
        val current =System.currentTimeMillis();
        nodeList.forEach(it->it.setLastHeartbeatTime(current));
        this.context = context;
        init = true;
        Executors.newScheduledThreadPool(1)
                .scheduleAtFixedRate(() -> {
                            freshNodeCache(context);
                        }, 10, 20, TimeUnit.SECONDS);
    }

    public RegisterResult register(ServerInstance serverInstance){
        if(!init){
            return RegisterResult.instance(null, NodeSyncStatus.INITIALIZING);
        }
        val node = FastjsonUtil.convertObject(serverInstance, Node.class);
        node.setNodeStatus(Node.NodeStatus.ALIVE);
        node.setLastHeartbeatTime(System.currentTimeMillis());
        node.setId(UUID.randomUUID().toString());
        node.setIdle(node.getProcessors());
        log.info("fresh nodelist to cache waiting for lock2");
        synchronized(nodeList){
            log.info("fresh nodelist to cache locked2");
//            nodeList.removeIf(existed->node.getHost().equals(existed.getHost()) &&
//                    node.getPort().equals(existed.getPort()));
            nodeList.removeIf(existed->{
                boolean remove = node.getHost().equals(existed.getHost()) &&
                        node.getPort().equals(existed.getPort());
                if(remove){
                    dispatchTool.getInvalidNodeContext().put(existed.getId(), new InvalidNodeDto(existed.getId()));
                }
                return remove;
            });
            nodeList.add(node);
            log.info("fresh nodelist to cache after register" + FastjsonUtil.toJson(nodeList));
            freshNodeCache(context);
        }
        return RegisterResult.instance(node.getId(), NodeSyncStatus.SUCCESS);
    }

    public NodeSyncStatus freshHeartbeat(ServerInstance serverInstance) {
        if(!init){
            return NodeSyncStatus.INITIALIZING;
        }
        synchronized(nodeList){
            for(Node existed : nodeList){
                if(serverInstance.getHost().equals(existed.getHost()) &&
                        String.valueOf(serverInstance.getPort()).equals(existed.getPort())){
                    //ids are impossible to be different
                    existed.setNodeStatus(Node.NodeStatus.ALIVE);
                    existed.setLastHeartbeatTime(System.currentTimeMillis());
                    existed.setIdle(serverInstance.getProcessors());
                    return NodeSyncStatus.SUCCESS;
                }
            }
        }
        return NodeSyncStatus.NOT_EXIST;
    }


    private void freshNodeCache(ApplicationContext context){
        try {
            long now = System.currentTimeMillis();
            log.info("fresh nodelist to cache waiting for lock");
            synchronized(nodeList){
                log.info("fresh nodelist to cache locked");
                //remove after 2 minutes
                nodeList.removeIf(it->{
                    boolean remove = now - it.getLastHeartbeatTime() > 2 * 60 * 1000;
                    if(remove){
                        dispatchTool.getInvalidNodeContext().put(it.getId(), new InvalidNodeDto(it.getId()));
                    }
                    return remove;
                });
                log.info("fresh nodelist to cache " + FastjsonUtil.toJson(nodeList));
                Node.cacheNodeList(nodeList);
            }
        } catch (Throwable e){
            log.error("node status sync error ", e);
            SpringApplication.exit(context);
            System.exit(0);
        }
    }

    @SneakyThrows
    public void applyUtilSuccess(Consumer<Node> consumer){
        var node = apply(consumer);
        while(node.isEmpty()){
            Thread.sleep(2000);
            node = apply(consumer);
        }
    }

    public void rtn(String nodeId){
        synchronized (nodeList) {
            nodeList.stream().filter(it->StringUtils.equals(it.getId(), nodeId))
                    .forEach(node-> node.setIdle(node.getIdle() + 1));
        }
    }

    private Option<Node> apply(Consumer<Node> consumer){
        synchronized (nodeList) {
            val node = nodeList.stream()
                    .max(Comparator.comparing(Node::getIdle))
                    .orElse(null);
            if(null == node){
                return Option.none();
            }
            if(node.getIdle() > 0){
                node.setIdle(node.getIdle() - 1);
                try{
                    consumer.accept(node);
                }catch(Throwable e) {
                    log.error("node apply error: ", e);
                    node.setIdle(node.getIdle() + 1);
                    return Option.none();
                }
                return Option.some(node);
            }else{
                return Option.none();
            }
        }
    }

}

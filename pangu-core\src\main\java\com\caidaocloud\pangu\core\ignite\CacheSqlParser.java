package com.caidaocloud.pangu.core.ignite;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.FilterOperator;
import com.caidaocloud.hrpaas.metadata.sdk.filter.MultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SimpleDataFilter;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * todo sql改为prepared statement
 * <AUTHOR> <PERSON>
 * @date 2025/1/9
 */
@Data
public class CacheSqlParser {
	private Map<String, String> dbNameMapping;

	private DynamicPojoDefinition dpd;

	public CacheSqlParser(DynamicPojoDefinition dynamicPojoDefinition) {
		this.dpd = dynamicPojoDefinition;
		this.dbNameMapping = dpd.dbNameMapping();
	}

	public String loadPropertySql(DataFilter dataFilter, List<String> properties, List<String> orderBys, int from, int limit) {
		// Escape each property with backticks
		List<String> escapedProperties = properties.stream()
				.map(prop -> "`" + prop + "`")
				.collect(Collectors.toList());

		String propertyList = String.join(",", escapedProperties);
		String sql = String.format("select `%s`,%s from `%s` where %s", dpd.getKeyField()
				.getDbName(), propertyList, dpd.getTableName(), genWherePredict(dataFilter));

		if (orderBys!=null &&!orderBys.isEmpty()) {
			// Escape order by fields with backticks
			List<String> escapedOrderBys = orderBys.stream()
					.map(orderBy -> {
						// Handle "field DESC" or "field ASC" cases
						if (orderBy.toLowerCase().contains(" desc") || orderBy.toLowerCase().contains(" asc")) {
							String[] parts = orderBy.split("\\s+", 2);
							return "`" + parts[0] + "` " + parts[1];
						}
						return "`" + orderBy + "`";
					})
					.collect(Collectors.toList());

			String orderByClause = String.join(",", escapedOrderBys);
			sql += String.format(" order by %s", orderByClause);
		}

		if(limit > 0){
			sql += String.format(" limit %d offset %d", limit, from);
		}
		return sql;
	}

	public String loadCacheSql(DataFilter dataFilter, long time) {
		String sql = String.format("select * from %s where %s", dpd.getTableName(), genWherePredict(dataFilter, "\"", true));
		if (dpd.getDefType() == DynamicPojoDefinition.DefType.PAAS) {
			if(time == 0l){
				time = System.currentTimeMillis();
			}
			sql = sql + " and data_start_time <= " + time + " and data_end_time >= " + time;
		}
		return sql;
	}


	public String functionSql(DataFilter dataFilter, String property, Function function, String... groupBy) {
		if (groupBy != null && groupBy.length > 0) {
			String gb = StringUtils.join(groupBy, ",");
			return String.format("select %s,%s from %s where %s group by %s", gb, function.apply(property), dpd.getTableName(), genWherePredict(dataFilter), gb);
		}

		return String.format("select %s from %s where %s", function.apply(property), dpd.getTableName(), genWherePredict(dataFilter));
	}
	private String genWherePredict(DataFilter dataFilter) {
		return genWherePredict(dataFilter, "`",false);
	}

	/**
	 *
	 * @param dataFilter
	 * @param quote
	 * @param flag true,查询数控
	 * @return
	 */
	@NotNull
	private String genWherePredict(DataFilter dataFilter, String quote, boolean flag) {
		if (dataFilter == null) {
			return "1=1";
		}
		StringBuilder sb = new StringBuilder();
		if (dataFilter instanceof SimpleDataFilter) {
			String property = ((SimpleDataFilter) dataFilter).getProperty();
			DynamicPojoDefinition.DynamicFieldDefinition definition = findFieldDefinition(property);
			sb.append(convertKey(property,flag,definition,quote))
					.append(convertOpt(((SimpleDataFilter) dataFilter).getOperator()))
					.append(formatValue(((SimpleDataFilter) dataFilter).getValue(), definition,((SimpleDataFilter) dataFilter).getOperator()));
		}
		else if (dataFilter instanceof MultiDataFilter) {
			sb.append("(");
			List<DataFilter> filters = ((MultiDataFilter) dataFilter).getFilters();
			for (int i = 0, filtersSize = filters.size(); i < filtersSize; i++) {
				DataFilter filter = filters.get(i);
				if (i > 0) {
					sb.append(convertOpt(((MultiDataFilter) dataFilter).getOperator()));
				}
				sb.append(" ");
				sb.append(genWherePredict(filter, quote, flag));
				sb.append(" ");
			}
			sb.append(")");
		}
		return sb.toString();
	}

	private DynamicPojoDefinition.DynamicFieldDefinition findFieldDefinition(String property) {

		return Arrays.stream(dpd.getFields())
				.filter(field -> field.fetchName().equals(property))
				.findFirst()
				.orElse(null);
	}

	private String formatValue(Object value, DynamicPojoDefinition.DynamicFieldDefinition fieldDefinition, FilterOperator operator) {
		if (fieldDefinition == null) {
			return value.toString();
		}
		if (fieldDefinition.getType().equals(String.class) || fieldDefinition.getType().isEnum()) {
			if (operator == FilterOperator.REGEX) {
				return "'%" + value + "%'";
			}
			return "'" + value + "'";
		}
		return value.toString();
	}

	private String convertOpt(FilterOperator opt) {
		switch (opt) {
		case EQ:
			return "=";
		case NE:
			return "!=";
		case LE:
			return "<=";
		case GE:
			return ">=";
		case LT:
			return "<";
		case GT:
			return ">";
		case AND:
			return "and";
		case OR:
			return "or";
		case REGEX:
			return "like";
		default:
			throw new IllegalArgumentException("Unknown operator: " + opt);
		}
	}


	/**
	 *
	 * @param key
	 * @param flag true,查询pg数据库;false,查询ignite h2数据库
	 * @param definition
	 * @param quote
	 * @return
	 */
	private String convertKey(String key, boolean flag, DynamicPojoDefinition.DynamicFieldDefinition definition, String quote) {
		if (flag) {
			key = dbNameMapping.getOrDefault(key, key);
			if (definition != null && definition.getType() != String.class ){
				return quote+key+quote + "::int8";
			}
			return quote + key + quote;
		}else {
			if (definition.getType().isEnum()) {
				return  "cast ("+quote+key+quote+" as varchar)";
			}
			return quote + key + quote;
		}
	}

}

package com.caidaocloud.pangu.application.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.msg.producer.MessageProducer;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.application.dto.*;
import com.caidaocloud.pangu.application.dto.compose.ArrangementDetailDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementNodeExecDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementProgressDto;
import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.dto.emp.EmpInfoDto;
import com.caidaocloud.pangu.application.event.EmployeeStatsMessageDTO;
import com.caidaocloud.pangu.application.event.EmployeeStatsPublisher;
import com.caidaocloud.pangu.application.feign.ComposeFeign;
import com.caidaocloud.pangu.application.feign.MasterdataFeign;
import com.caidaocloud.pangu.domain.entity.*;
import com.caidaocloud.pangu.domain.enums.ApproveStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.domain.repository.IEmpRepository;
import com.caidaocloud.pangu.infrastructure.util.LangUtil;
import com.caidaocloud.pangu.interfaces.vo.BonusComposeResult;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerComposeVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/24
 */
@Service
@Slf4j
public class BonusLedgerService {

	@Autowired
	private IEmpRepository empRepository;
	@Autowired
	private MasterdataFeign masterdataFeign;
	@Value("${bonus.calc.load.batch:100}")
	private int bonusCalcLoadBatch;
	@Autowired
	private MessageProducer messageProducer;
	@Autowired
	private StringRedisTemplate redisTemplate;
	@Resource
	private ComposeFeign composeFeign;
	@Autowired
	private ArrangementService arrangementService;


	@PaasTransactional
	public String create(BonusLedgerDto bonusLedgerDto) {
		BonusSchema schema = BonusSchema.load(bonusLedgerDto.getSchemaId());
		BonusStruct struct = BonusStruct.load(schema.getStructId());
		List<BonusStructCompose> compose = struct.listCompose();
		BonusLedger ledger = new BonusLedger(bonusLedgerDto.getI18nName(), bonusLedgerDto.getSchemaId(), bonusLedgerDto.getMonth(),
				bonusLedgerDto.getStartDate(), bonusLedgerDto.getEndDate(), struct.getExecutionType(),
				schema.getSpecial(), schema.getHireDate(), schema.getTerminationDate(), bonusLedgerDto.getSelectionStartDate(), bonusLedgerDto.getSelectionEndDate());
		return ledger.create(schema, compose);
	}


	@PaasTransactional
	public void update(BonusLedgerDto bonusLedgerDto) {
		BonusLedger bonusLedger = BonusLedger.load(bonusLedgerDto.getBid());
		if (bonusLedger == null) {
			throw new ServerException("Data not found");
		}
		if (BonusLedgerStatus.CLOSE.name().equals(bonusLedger.getStatus().getValue())) {
			throw new ServerException("Bonus ledger closed");
		}
		BonusSchema schema = BonusSchema.load(bonusLedgerDto.getSchemaId());
		BonusStruct struct = BonusStruct.load(schema.getStructId());
		List<BonusStructCompose> compose = struct.listCompose();

		// Call the updated update method with BonusLedgerDto
		bonusLedger.update(bonusLedgerDto, schema,struct, compose);
	}

	/**
	 * 账套关闭
	 * @param bid
	 */
	@PaasTransactional
	public void close(String bid) {
		BonusLedger bonusLedger = BonusLedger.load(bid);
		if (bonusLedger == null) {
			throw new ServerException("Data not found");
		}
		bonusLedger.close();
	}

	@PaasTransactional
	public void delete(String bid) {
		BonusLedger bonusLedger = BonusLedger.load(bid);
		if (bonusLedger == null) {
			throw new ServerException("Data not found");
		}
		bonusLedger.delete();
	}

	@PaasTransactional
	public void syncEmp(String bid) {
		BonusLedger bonusLedger = BonusLedger.load(bid);
		List<EmpSimple> matchedEmp = BonusSchema.load(bonusLedger.getSchemaId()).fetchEmpByCoverage();
		bonusLedger.syncEmp(matchedEmp);
		empStatsMsg(bid);
	}

	private void empStatsMsg(String bid) {
		SpringUtil.getBean(EmployeeStatsPublisher.class).publish(new EmployeeStatsMessageDTO(bid));
	}

	@PaasTransactional
	public void addEmp(BonusAccountEmpDto bonusAccountSyncEmpDto) {
		BonusLedger ledger = BonusLedger.load(bonusAccountSyncEmpDto.getBid());
		ledger.addEmp(Sequences.sequence(bonusAccountSyncEmpDto.getEmpId()).map(empId -> {
			EmpSimple empSimple = new EmpSimple();
			empSimple.setEmpId(empId);
			return empSimple;
		}).toList());
		empStatsMsg(bonusAccountSyncEmpDto.getBid());
	}

	@PaasTransactional
	public void removeEmp(String bid, List<String> empId) {
		BonusLedger account = BonusLedger.load(bid);
		account.removeEmpBatch(empId);
		empStatsMsg(bid);
	}

	/**
	 * 加载计薪员工
	 * @param dto
	 * @return
	 */
	public PageResult<BonusLedgerEmpVo> loadLedgerEmpPage(BonusLedgerEmpPageDto dto) {
		BonusLedger ledger = BonusLedger.load(dto.getBid());
		PageResult<BonusLedgerEmp> empPage = ledger.loadEmp(dto);
		if (empPage.getItems().isEmpty()) {
			return new PageResult<>(Collections.emptyList(), empPage.getPageNo(), empPage.getPageSize(), empPage.getTotal());
		}
		List<String> empIds = Sequences.sequence(empPage.getItems()).map(BonusLedgerEmp::getEmpId).toList();
		Map<String, EmpInfoDto> empInfoVoMap = masterdataFeign.loadEmpInfo(Maps.map("empIds", empIds)).getData()
				.stream()
				.collect(Collectors.toMap(EmpInfoDto::getEmpId, emp -> emp,(a, b) -> {
					log.warn("Duplicate key:{}", a);
					return a;
				}));
		List<BonusLedgerEmpVo> list = Sequences.sequence(empPage.getItems()).map(emp -> {
			BonusLedgerEmpVo empVo = ObjectConverter.convert(emp, BonusLedgerEmpVo.class);
			EmpInfoDto infoVo = empInfoVoMap.get(emp.getEmpId());
			if (infoVo != null) {
				BeanUtils.copyProperties(infoVo, empVo);
			}
			return empVo;
		}).toList();
		return new PageResult<>(list, empPage.getPageNo(), empPage.getPageSize(), empPage.getTotal());
	}

	// public PageResult<BonusLedgerEmpVo> loadEmp(BonusLedger account, BonusLedgerEmpPageDto dto) {
	// 	PageResult<BonusLedgerEmp> empPage = account.loadEmp(dto);
	// 	if (empPage.getItems().isEmpty()) {
	// 		return new PageResult<>(new ArrayList<>(), empPage.getPageNo(), empPage.getPageSize(), empPage.getTotal());
	// 	}
	// 	List<String> empIds = Sequences.sequence(empPage.getItems()).map(BonusLedgerEmp::getEmpId).toList();
	// 	Map<String, EmpInfoDto> empInfoVoMap = masterdataFeign.loadEmpInfo(Maps.map("empIds",empIds)).getData().stream()
	// 			.collect(Collectors.toMap(EmpInfoDto::getEmpId, emp -> emp));
	// 	List<BonusLedgerEmpVo> list = Sequences.sequence(empPage.getItems()).map(emp -> {
	// 		BonusLedgerEmpVo empVo = ObjectConverter.convert(emp, BonusLedgerEmpVo.class);
	// 		empVo.setMonth(account.getMonth());
	// 		// EmpInfoDto infoVo = empInfoVoMap.get(emp.getEmpId());
	// 		// if (infoVo != null) {
	// 		// 	BeanUtils.copyProperties(infoVo, empVo);
	// 		// }
	// 		return empVo;
	// 	}).toList();
	// 	return new PageResult<>(list, empPage.getPageNo(), empPage.getPageSize(), empPage.getTotal());
	// }

	public PageResult<BonusLedgerVo> page(BonusLedgerPageDto dto) {
		PageResult<BonusLedger> page = BonusLedger.page(dto.getName(), dto.getPageNo(), dto.getPageSize());
		List<BonusLedgerVo> list = Sequences.sequence(page.getItems())
				.map(account -> {
					BonusLedgerVo vo = ObjectConverter.convert(account, BonusLedgerVo.class);
					vo.setStatus(BonusLedgerStatus.valueOf(account.getStatus().getValue()));
					vo.setApproveStatus(account.getApprove_status() == null || StringUtils.isEmpty(account.getApprove_status()
							.getValue()) ? ApproveStatus.NOT_NEED_APPROVAL : ApproveStatus.valueOf(account.getApprove_status()
							.getValue()));
					vo.setProgress(String.format("%s/%s", account.getSucceed(), account.getTotal()));
					return vo;
				}).toList();
		return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
	}

	public List<BonusLedgerComposeVo> loadComposeList(String bid) {
		BonusLedger ledger = BonusLedger.load(bid);
		if (ledger == null) {
			throw new ServerException("Data not found");
		}

		List<BonusLedgerStep> steps = ledger.loadStep();
		List<ComposeDefDto> composeDef =
				composeFeign.listComposeDef(Sequences.sequence(steps)
				.map(BonusLedgerStep::getComposeId).toList()).getData();

		// 转换为VO对象
		return steps.stream()
				.map(step -> {
					BonusLedgerComposeVo vo = ObjectConverter.convert(step, BonusLedgerComposeVo.class);
					// 设置步骤状态
					if (step.getStatus() != null) {
						vo.setStatus(BonusLedgerStepStatus.fromValue(Integer.parseInt(step.getStatus().getValue())));
					}

					ComposeDefDto def = Sequences.sequence(composeDef)
							.find(compose -> compose.getArrangementId().equals(step.getComposeId()))
							.getOrNull();

					if (def != null) {
						BeanUtils.copyProperties(def, vo);
					}
					return vo;
				})
				.collect(Collectors.toList());
	}

	@PaasTransactional
	public void exec(BonusLedgerExecDto execDto) {
		BonusLedger ledger = BonusLedger.load(execDto.getLedgerId());
		if (ledger == null) {
			throw new ServerException("Data not found");
		}
		if (BonusLedgerStatus.CLOSE.name().equals(ledger.getStatus().getValue())) {
			throw new ServerException("无法执行已关闭的计件工资账套");
		}
		// ledger.executeStep(execDto.getStepId());
		ledger.executeStepBatch(execDto.getBatchStepId());
		empStatsMsg(execDto.getLedgerId());
	}

	@PaasTransactional
	public void execAll(BonusLedgerExecDto execDto) {
		BonusLedger ledger = BonusLedger.load(execDto.getLedgerId());
		if (ledger == null) {
			throw new ServerException("Data not found");
		}
		if (BonusLedgerStatus.CLOSE.name().equals(ledger.getStatus().getValue())) {
			throw new ServerException("无法执行已关闭的计件工资账套");
		}
		ledger.executeAll();
		empStatsMsg(execDto.getLedgerId());
	}

	public BonusComposeResult composeResult(String ledgerId) {
		BonusLedger ledger = BonusLedger.load(ledgerId);
		if (ledger == null) {
			throw new ServerException("Data not found");
		}

		// 查找 composeId 为 0 的编排步骤
		List<BonusLedgerStep> steps = ledger.loadStep();
		Optional<BonusLedgerStep> stepOptional = steps.stream()
				.filter(step -> step.getComposeId().equals(BonusConstant.BONUS_LEDGER_FINAL_STEP_ID))
				.findFirst();

		if (stepOptional.isPresent() && String.valueOf(BonusLedgerStepStatus.EXECUTED.getValue())
				.equals(stepOptional.get().getStatus().getValue())) {
			int total = ledger.getTotal();
			int succeed = ledger.getSucceed();
			return new BonusComposeResult(succeed,total-succeed,total);
		}
		else {
			return new BonusComposeResult();
		}
	}

	public List<BonusLedgerVo> list() {
		List<BonusLedger> list = BonusLedger.page(null, 1, -1).getItems();
		return ObjectConverter.convertList(list, BonusLedgerVo.class);
	}

	/**
	 * Find multiple BonusLedger entities by their IDs
	 * 
	 * @param ids List of ledger IDs to retrieve
	 * @return List of BonusLedgerDto objects
	 */
	public List<BonusLedgerDto> findByIds(List<String> ids) {
		if (ids == null || ids.isEmpty()) {
			return new ArrayList<>();
		}
		
		// Retrieve all BonusLedger entities by their IDs
		List<BonusLedger> ledgers = BonusLedger.findByIds(ids);
		
		// Convert entities to DTOs
		return ledgers.stream()
				.map(ledger -> {
					BonusLedgerDto dto = new BonusLedgerDto();
					dto.setBid(ledger.getBid());
					dto.setI18nName(ledger.getI18nName());
					dto.setSchemaId(ledger.getSchemaId());
					dto.setMonth(ledger.getMonth());
					dto.setStartDate(ledger.getStartDate());
					dto.setEndDate(ledger.getEndDate());
					return dto;
				})
				.collect(Collectors.toList());
	}

	public List<BonusLedgerEmpSimpleVo> getLedgerEmpList(String bid) {
		BonusLedger bonusLedger = BonusLedger.load(bid);
		if (bonusLedger == null) {
			throw new ServerException("奖金账套不存在");
		}

		// 获取该奖金账套下的所有员工
		List<BonusLedgerEmp> empList = bonusLedger.loadSimpleEmp();

		// 获取账套名称
		String ledgerName = LangUtil.getCurrentLangVal(bonusLedger.getI18nName());

		// 转换为VO对象并设置账套名称
		List<BonusLedgerEmpSimpleVo> voList = ObjectConverter.convertList(empList, BonusLedgerEmpSimpleVo.class);
		for (BonusLedgerEmpSimpleVo vo : voList) {
			vo.setLedgerName(ledgerName);
		}
		
		return voList;
	}

	public ArrangementProgressDto composeProgress(String ledgerId) {
		BonusLedger ledger = BonusLedger.load(ledgerId);
		if (ledger == null) {
			throw new ServerException("奖金账套不存在");
		}

		List<BonusLedgerStep> steps = ledger.loadStep();
		if (ExecutionType.SINGLE.getCode().equals(ledger.getExecutionType().getValue())){
			steps = Sequences.sequence(steps).filter(step -> ledger.getWaitingTask().contains(step.getBid())).toList();
		}
		steps = steps.stream().filter(step -> !"0".equals(step.getComposeId())).collect(Collectors.toList());
		if (Sequences.sequence(steps).stream().allMatch(step->String.valueOf(BonusLedgerStepStatus.NOT_EXECUTED.getValue())
				.equals(step.getStatus().getValue()))){
			return ArrangementProgressDto.builder()
					.totalNodes(0)
					.completedNodes(0)
					.status(BonusLedgerStepStatus.NOT_EXECUTED)
					.build();
		}
		ArrangementProgressDto executedProgress = loadExecutedProgress(steps);
		log.info("executedProgress={}", executedProgress);
		ArrangementProgressDto runningProgress = loadRunningProgress(steps);
		log.info("runningProgress={}", runningProgress);
		ArrangementProgressDto waitingProgress = loadWaitingProgress(steps);
		log.info("waitingProgress={}", waitingProgress);
		BonusLedgerStepStatus status=BonusLedgerStepStatus.EXECUTED;
		Option<BonusLedgerStep> os = Sequences.sequence(steps)
				.find(step -> String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue())
						.equals(step.getStatus().getValue()) || String.valueOf(BonusLedgerStepStatus.ERROR.getValue())
						.equals(step.getStatus().getValue()));
		String name = "";
		if (os.isDefined()) {
			status = BonusLedgerStepStatus.fromValue(Integer.parseInt(os.get().getStatus().getValue()));
			List<ArrangementNodeExecDto> nodeExecDtos = arrangementService.loadNodeExecList(os.get()
					.getExecId());
			name = composeFeign.loadPublished(os.get().getComposeId()).getData().getComposeName();
			name += Sequences.sequence(nodeExecDtos)
					.find(exec -> !Lists.list("CHECK", "SUCCESS", "SKIPPED").contains(exec.getStatus()))
					.map(exec->"-"+exec.getArrangementNodeName()).getOrElse("");
		}

		return ArrangementProgressDto.builder()
				.totalNodes(executedProgress.getTotalNodes() + runningProgress.getTotalNodes() + waitingProgress.getTotalNodes())
				.completedNodes(executedProgress.getCompletedNodes() + runningProgress.getCompletedNodes() + waitingProgress.getCompletedNodes())
				.status(status)
				.cost(System.currentTimeMillis() - steps.get(0).getExecTime())
				.name(name)
				.build();
	}

	private ArrangementProgressDto loadWaitingProgress(List<BonusLedgerStep> steps) {
		List<BonusLedgerStep> list = Sequences.sequence(steps)
				.filter(step -> String.valueOf(BonusLedgerStepStatus.NOT_EXECUTED.getValue())
						.equals(step.getStatus().getValue())).toList();
		if (list.isEmpty()) {
			return ArrangementProgressDto.builder().totalNodes(0).completedNodes(0).build();
		}
		List<String> arrangementIds = Sequences.sequence(list).map(BonusLedgerStep::getComposeId).toList();
		List<ArrangementDetailDto> dtoList = arrangementService.loadArrangementList(arrangementIds);
		int total = 0;
		for (ArrangementDetailDto dto : dtoList) {
			total += dto.getNodes().size();
		}
		return ArrangementProgressDto.builder().totalNodes(total).completedNodes(0).build();
	}

	private ArrangementProgressDto loadRunningProgress(List<BonusLedgerStep> steps) {
		List<BonusLedgerStep> list = Sequences.sequence(steps)
				.filter(step -> String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue())
						.equals(step.getStatus().getValue())).toList();
		if (CollectionUtils.isEmpty(list)) {
			return ArrangementProgressDto.builder().totalNodes(0).completedNodes(0).build();
		}
		BonusLedgerStep step = list.get(0);
		return arrangementService.getArrangementProgress(step.getExecId(), step.getComposeId());
	}

	private ArrangementProgressDto loadExecutedProgress(List<BonusLedgerStep> steps) {
		List<BonusLedgerStep> list = Sequences.sequence(steps)
				.filter(step -> String.valueOf(BonusLedgerStepStatus.EXECUTED.getValue())
						.equals(step.getStatus().getValue())||  String.valueOf(BonusLedgerStepStatus.ERROR.getValue())
						.equals(step.getStatus().getValue()) ).toList();
		int total=0;
		int succeed=0;
		for (BonusLedgerStep step : list) {
			total += step.getTotal();
			succeed += step.getSucceed();
		}
		return ArrangementProgressDto.builder().totalNodes(total).completedNodes(succeed).build();
	}

}

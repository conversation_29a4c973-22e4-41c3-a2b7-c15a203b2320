package com.caidaocloud.pangu.application.service;

import java.math.BigDecimal;
import java.util.List;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.application.dto.BonusApproveDto;
import com.caidaocloud.pangu.application.dto.BonusApproveSummaryDto;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskApproveDTO;
import com.caidaocloud.pangu.application.feign.WfOperateFeignClient;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusApproveRecord;
import com.caidaocloud.pangu.domain.entity.BonusApproveSummary;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.application.enums.WfTaskActionEnum;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.enums.ApproveStatus;
import com.caidaocloud.pangu.domain.repository.IEmpRepository;
import com.caidaocloud.pangu.domain.repository.IFormRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.caidaocloud.pangu.application.factory.BonusWorkflowFactory.createFunCode;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/2/1
 */
@Service
@Slf4j
public class BonusApproveService {

	@Autowired
	private BonusSchemaService bonusSchemaService;
	@Autowired
	private IFormRepository formRepository;
	@Autowired
	private IEmpRepository empRepository;
	@Autowired
	private IWfRegisterFeign wfRegisterFeign;
	@Autowired
	private WfOperateFeignClient wfOperateFeignClient;

	public BonusApproveSummaryDto getSummary(String ledgerId) {
		BonusLedger ledger = BonusLedger.load(ledgerId);
		BonusSchema schema = BonusSchema.load(ledger.getSchemaId());
		List<BonusStructItem> bonusItem = bonusSchemaService.loadBonusItem(ledger.getSchemaId());
		List<BonusReport> empList = findAccountEmp(ledger);
		return buildApproveSummary(ledger, schema, empList, bonusItem);
	}

	private BonusApproveSummaryDto buildApproveSummary(BonusLedger account, BonusSchema schema, List<BonusReport> empList, List<BonusStructItem> bonusItemList) {
		BonusApproveSummaryDto summary = new BonusApproveSummaryDto();
		summary.setName(account.getName());
		summary.setAccountId(account.getBid());
		summary.setFormId(schema.getApproveConfig().getFormId());
		summary.setSummary(account.getName());
		summary.setYearMonth(DateUtil.format(account.getMonth(), "yyyyMM"));
		summary.setStartDate(account.getStartDate());
		summary.setEndDate(account.getEndDate());

		summary.setCount(empList.size());
		StringBuilder sb = new StringBuilder();
		for (String itemId : schema.getApproveConfig().getItems()) {
			Option<BonusStructItem> itemOption = Sequences.sequence(bonusItemList)
					.find(item -> itemId.equals(item.getBid()));
			if (itemOption.isEmpty()) {
				continue;
			}
			sb.append(itemOption.get().getName()).append(":");
			BigDecimal value = BigDecimal.ZERO;
			for (BonusReport emp : empList) {
				Option<BonusReport.BonusItem> item = Sequences.sequence(emp.getItem())
						.find(item1 -> itemId.equals(item1.getBid()));
                if (!item.isEmpty()) {
					value = value.add(new BigDecimal(item.get().getValue()));
                }
			}
			sb.append(value.toPlainString()).append("\n");
		}
		summary.setSummary(sb.toString());
        return summary;
    }


	private List<BonusReport> findAccountEmp(BonusLedger ledger) {
		BonusReportDto dto = new BonusReportDto();
		dto.setPageSize(-1);
		PageResult<BonusReport> emp = BonusReport.loadReport(ledger.getBid(), dto);
		return emp.getItems();
	}

	public void start(BonusApproveDto dto) {
		BonusLedger account = BonusLedger.load(dto.getLedgerId());
		BonusSchema schema = BonusSchema.load(account.getSchemaId());
		checkApprove(account, schema);
		startApprove(account, schema, dto);
	}

	private void startApprove(BonusLedger ledger, BonusSchema schema, BonusApproveDto dto) {
		String formDataId = formRepository.saveFormData(schema.getApproveConfig().getFormId(), dto.getFormData());
		String businessId = beginWorkflow(schema.getBid());
		BonusApproveRecord receipt = createBonusApproveRecord(ledger, schema, dto, businessId, formDataId);
		receipt.create();
		ledger.getApprove_status().setValue(ApproveStatus.PENDING.name());
		ledger.update();
	}

	private BonusApproveRecord createBonusApproveRecord(BonusLedger account, BonusSchema schema, BonusApproveDto dto, String businessId, String formDataId) {
		BonusApproveRecord receipt = new BonusApproveRecord();
        receipt.setLedgerId(account.getBid());
        receipt.setFormId(schema.getApproveConfig().getFormId());
        receipt.setBusinessId(businessId);
        receipt.setFormDataId(formDataId);
		receipt.setSummary(ObjectConverter.convert(dto.getSummary(), BonusApproveSummary.class));
		return receipt;
	}

	private String beginWorkflow(String schemaId) {
		String funCode = createFunCode(schemaId);
		WfBeginWorkflowDto beginDto = new WfBeginWorkflowDto();
		beginDto.setFuncCode(funCode);
		beginDto.setBusinessId(SnowUtil.nextId());
		buildApplicantInfo(beginDto);
		beginDto.setEventTime(System.currentTimeMillis());
		Result result = wfRegisterFeign.begin(beginDto);
		if (!result.isSuccess() ) {
			throw new ServerException(result.getMsg());
		}
		return beginDto.getBusinessId();
	}

	private void buildApplicantInfo(WfBeginWorkflowDto beginDto) {
		beginDto.setApplicantId(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId()));
		EmpSimple emp = empRepository.loadEmp(beginDto.getApplicantId());
		beginDto.setApplicantName(emp.getName());
	}

	private void checkApprove(BonusLedger ledger, BonusSchema schema) {
		if (!schema.getApproveConfig().getEnabled()) {
			throw new ServerException("该计件工资账套不需要审批");
		}
		if (ApproveStatus.PENDING.name().equals(ledger.getApprove_status().getValue())) {
			throw new ServerException("审批已发起");
		}
	}

	public BonusApproveSummaryDto getSummaryByBusinessKey(String businessKey) {
		String businessId = StringUtils.substringBefore(businessKey, "_");
		BonusApproveRecord receipt = BonusLedger.loadApproveReceiptByBusinessId(businessId);
		BonusApproveSummaryDto dto = ObjectConverter.convert(receipt, BonusApproveSummaryDto.class);
		BeanUtils.copyProperties(receipt.getSummary(), dto);
		return dto;
	}

	public void approve(BonusApproveDto dto, WfTaskActionEnum choice) {
		String businessId = StringUtils.substringBefore(dto.getBusinessKey(), "_");
		BonusApproveRecord receipt = BonusLedger.loadApproveReceiptByBusinessId(businessId);
		BonusLedger ledger = BonusLedger.load(receipt.getLedgerId());
		BonusSchema schema = BonusSchema.load(ledger.getSchemaId());
		formRepository.updateFormData(schema.getApproveConfig().getFormId(),receipt.getFormDataId(), dto.getFormData());
		// 工作流审批
		doApprove(dto.getTaskId(), dto.getComment(), choice);
	}

	private void doApprove(String taskId, String comment, WfTaskActionEnum choice) {
		WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
		wfApproveTaskDto.setTaskId(taskId);
		wfApproveTaskDto.setChoice(choice);
		wfApproveTaskDto.setComment(comment);
		try {
			Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
			if (!result.isSuccess()) {
				PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
				throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new ServerException(e.getMessage());
		}
	}

	public void callback(String businessKey, String tenantId, WfCallbackTriggerOperationEnum callback) {
		String businessId = StringUtils.substringBefore(businessKey, "_");
		BonusApproveRecord receipt = BonusLedger.loadApproveReceiptByBusinessId(businessId);
		String accountId = receipt.getLedgerId();
		BonusLedger ledger = BonusLedger.load(accountId);
		if (ApproveStatus.NOT_NEED_APPROVAL.name().equals(ledger.getApprove_status().getValue())) {
			log.warn("账套审批配置已关闭，无需处理审批结果，ledgerId={},schemaId={}", accountId, ledger.getSchemaId());
			return;
		}
		ledger.getApprove_status().setValue(callback == WfCallbackTriggerOperationEnum.APPROVED ? ApproveStatus.APPROVED.name() : ApproveStatus.REJECTED.name());
		ledger.update();
	}
}

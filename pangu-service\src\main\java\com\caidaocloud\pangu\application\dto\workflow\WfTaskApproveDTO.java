package com.caidaocloud.pangu.application.dto.workflow;

import java.util.List;

import com.caidaocloud.pangu.application.enums.ThirdSysStatus;
import com.caidaocloud.pangu.application.enums.WfTaskActionEnum;
import lombok.Data;

/**
 * 任务审批
 */
@Data
public class WfTaskApproveDTO extends WfTaskParentDTO {

    /**
     * 审批动作,动作:APPROVED(同意)、REFUSED(拒绝)
     */
    private WfTaskActionEnum choice;

    /**
     * 评论
     */
    private String comment;

    /**
     * 加签审批人
     */
    private List<String> addApprovers;

    /**
     * 上传文件路径
     */
    private String filePath;

    /**
     * 上传文件名
     */
    private String fileName;

    /**
     * isAdmin
     */
    private Boolean isAdmin;

    /**
     * 第三方业务状态
     */
    private ThirdSysStatus thirdStatus;

}

package com.caidaocloud.pangu.domain.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusStructItemRepository extends BaseRepository<BonusStructItem> {
	PageResult<BonusStructItem> selectPage(BasePage page, String keywords, String structId);

	List<BonusStructItem> selectList(String structId);

    List<BonusStructItem> selectListByStructIds(List<String> idList);
}

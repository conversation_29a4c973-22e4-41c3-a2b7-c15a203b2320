package multicast;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;

public class MulticastSender {
    public static void main(String[] args) throws Exception {
        String multicastAddress = "*********"; // 组播地址
        int port = 5000; // 端口号
        InetAddress group = InetAddress.getByName(multicastAddress);

        try (MulticastSocket socket = new MulticastSocket()) {
            String message = "Hello from node!";
            DatagramPacket packet = new DatagramPacket(
                message.getBytes(), message.length(), group, port);
            
            socket.send(packet); // 发送组播消息
            System.out.println("Message sent: " + message);
        }
    }
}

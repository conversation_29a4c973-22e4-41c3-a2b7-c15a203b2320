package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.dto.compose.ComposeExecDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementNodeExecDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@FeignClient(value = "${feign.rename.caidaocloud-pangu-manager:caidaocloud-pangu-manager}", fallback = ComposeExecFeignFallback.class, configuration = FeignConfiguration.class)
public interface ComposeExecFeign {
	@PostMapping("/api/calc/v1/exec")
	Result<String> exec(@RequestBody ComposeExecDto dto);

	@GetMapping("/api/calc/v1/exec/nodes/logs")
	Result<List<ArrangementNodeExecDto>> execNodesLogs(@RequestParam String execSeqId);
}

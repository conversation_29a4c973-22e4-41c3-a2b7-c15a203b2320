package com.caidaocloud.pangu.node.common.feign;

import com.alibaba.nacos.api.naming.pojo.ListView;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.dto.NodeSyncStatus;
import com.caidaocloud.pangu.core.dto.RegisterResult;
import com.caidaocloud.pangu.core.dto.ServerInstance;
import com.caidaocloud.pangu.node.common.annotations.FeignThrow;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@FeignClient(value = "${feign.rename.caidaocloud-pangu-manager:caidaocloud-pangu-manager}", fallback = PanguManagerFeignFallback.class, configuration = FeignConfiguration.class)
public interface PanguManagerFeign {

	@PostMapping(value = "/api/calc/v1/manage/node/register")
	@FeignThrow
	Result<RegisterResult> register(ServerInstance serverInstance);

	@PostMapping(value = "/api/calc/v1/manage/node/heartbeat")
	@FeignThrow
	Result<NodeSyncStatus> heartBeat(ServerInstance serverInstance);

	@PostMapping(value = "/api/calc/v1/start/report")
	@FeignThrow
	Result<Boolean> report(@RequestBody CalcReport.Start report);

	@PostMapping(value = "/api/calc/v1/end/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.End report);

	@PostMapping(value = "/api/calc/v1/condition/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.Condition report);

	@PostMapping(value = "/api/calc/v1/loaded/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.TaskDataLoaded report);

	@PostMapping(value = "/api/calc/v1/subTask/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.SubTask report);

	@PostMapping(value = "/api/calc/v1/failed/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.Fail report);

	@PostMapping(value = "/api/calc/v1/data/pre/load/report")
	@FeignThrow
	Result<Boolean> report(CalcReport.PreLoad report);

	@GetMapping(value = "/api/calc/v1/exec/node/result/cache")
	@FeignThrow
	Result<String> getResultCacheId(@RequestParam String execSeqId, @RequestParam String nodeId);
}

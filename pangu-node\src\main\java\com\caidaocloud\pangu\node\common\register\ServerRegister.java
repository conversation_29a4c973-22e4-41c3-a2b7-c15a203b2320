package com.caidaocloud.pangu.node.common.register;

import java.net.InetAddress;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.core.dto.NodeSyncStatus;
import com.caidaocloud.pangu.core.dto.ServerInstance;
import com.caidaocloud.pangu.node.business.service.TaskProcessorConfiguration;
import com.caidaocloud.pangu.node.common.feign.PanguManagerFeign;
import lombok.extern.slf4j.Slf4j;

import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Service
@Slf4j
public class ServerRegister {
	@Resource
	private InetUtils inetUtils;
	@Resource
	private RemoteInitializer remoteInitializer;
	@Resource
	private PanguManagerFeign panguManagerFeign;

	public static String registerId = null;

	@Value("${processors:10}")
	private int processors;

	@Value("${slave.register.host:}")
	private String registerHost;
	@Value("${slave.register.port:0}")
	private int registerPort;

	public void register(ApplicationContext context){
		InetAddress address = inetUtils.findFirstNonLoopbackAddress();
		ServerInstance serverInstance = convert2ServerInstance(address);
		new Thread(()->{
			log.info("start register worker to master,worker info={}", serverInstance);
			while(true){
				try {
					val result = panguManagerFeign.register(serverInstance).getData();
					if(NodeSyncStatus.INITIALIZING.equals(result.getStatus())){
						throw new ServerException("manager initializing");
					}else if(NodeSyncStatus.SUCCESS.equals(result.getStatus())){
						serverInstance.setId(result.getId());
						registerId = result.getId();
					}else{
						throw new ServerException("unexpected register result");
					}
					break;
				} catch (Throwable e) {
					//todo testcode log.error("register client failed ", e);
				}
			}
			Executors.newScheduledThreadPool(1)
					.scheduleWithFixedDelay(()->{
						try {
							serverInstance.setProcessors(TaskProcessorConfiguration.availableProcessCount.get());
							val result = panguManagerFeign.heartBeat(serverInstance).getData();
							if(NodeSyncStatus.INITIALIZING.equals(result)){
								throw new ServerException("manager initializing");
							}else if(NodeSyncStatus.SUCCESS.equals(result)){
								return;
							}else if(NodeSyncStatus.NOT_EXIST.equals(result)){
								log.error("client not exist ");
								SpringApplication.exit(context);
								System.exit(0);
							}else{
								throw new ServerException("unexpected heartbeat result");
							}
						} catch (Throwable e) {
							log.error("client heartbeat failed ", e);
						}
					}, 10, 10, TimeUnit.SECONDS);
		}).start();
	}

	private ServerInstance convert2ServerInstance(InetAddress address) {
		ServerInstance instance = new ServerInstance();
		if(StringUtils.isEmpty(registerHost)){
			instance.setHost(address.getHostAddress());
			instance.setPort(remoteInitializer.getPort());
		}else{
			instance.setHost(registerHost);
			instance.setPort(registerPort);
		}
		instance.setProcessors(processors);
		return instance;
	}

}

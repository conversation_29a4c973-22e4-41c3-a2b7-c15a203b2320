package com.caidaocloud.pangu.domain.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusStructRepository extends BaseRepository<BonusStruct> {
	BonusStruct detail(String bid);

	 PageResult<BonusStruct> selectPage(BasePage page, String keyword);
}

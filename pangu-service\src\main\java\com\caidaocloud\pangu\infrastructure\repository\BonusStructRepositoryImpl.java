package com.caidaocloud.pangu.infrastructure.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.repository.IBonusStructRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepositoryImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
@Repository
public class BonusStructRepositoryImpl extends BaseRepositoryImpl<BonusStruct> implements IBonusStructRepository {
	@Override
	public BonusStruct detail(String bid) {
		return DataQuery.identifier(BonusStruct.BONUS_STRUCT_IDENTIFIER).oneOrNull(bid, BonusStruct.class);
	}

	@Override
	public PageResult<BonusStruct> selectPage(BasePage page, String keywords) {
		return DataQuery.identifier(BonusStruct.BONUS_STRUCT_IDENTIFIER)
				.limit(page.getPageSize(), page.getPageNo())
				.filter(DataFilter.ne("deleted", Boolean.TRUE.toString()).andRegexIf("name", keywords, () -> StringUtils.isNotEmpty(keywords)), BonusStruct.class);
	}
}

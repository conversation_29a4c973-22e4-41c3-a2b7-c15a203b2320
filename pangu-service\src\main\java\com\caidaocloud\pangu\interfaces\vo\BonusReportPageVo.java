package com.caidaocloud.pangu.interfaces.vo;

import java.util.List;
import java.util.Map;

import com.caidaocloud.dto.PageResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/8/3
 */
@Data
@ApiModel("奖金报表")
public class BonusReportPageVo extends PageResult<Map<String,Object>> {
	List<Header> headers;

	@Data
	public static class Header {
		private String dataIndex;
		private boolean sort = false;
		private String title;
		private String type = "ro";
		private String width = "80px";
		private boolean dateFormat= false;

		public Header(String dataIndex, String title) {
			this.dataIndex = dataIndex;
			this.title = title;
		}
	}
	public BonusReportPageVo(List<Map<String,Object>> items, int pageNo, int pageSize, int total) {
		super(items, pageNo, pageSize, total);
	}
}

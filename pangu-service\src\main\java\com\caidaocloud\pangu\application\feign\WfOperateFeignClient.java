package com.caidaocloud.pangu.application.feign;


import com.caidaocloud.pangu.application.dto.workflow.WfTaskApproveDTO;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskBackDTO;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskRevokeDTO;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工作流流程操作
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-workflow-service-v2:caidaocloud-workflow-service-v2}",
        fallback = WfOperateFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "wfOperateFeignClient"
)
public interface WfOperateFeignClient {
    /**
     * 任务审批
     *
     * @param wfApproveTaskDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/approve")
    Result<?> approveTask(@RequestBody WfTaskApproveDTO wfApproveTaskDTO);

    /**
     * 任务驳回
     *
     * @param wfTaskBackDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/back")
    Result backTask(@RequestBody WfTaskBackDTO wfTaskBackDTO);

    /**
     * 任务流程撤回
     *
     * @param wfTaskRevokeDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/revoke")
    Result<String> revokeProcessOfTask(@RequestBody WfTaskRevokeDTO wfTaskRevokeDTO);

//    /**
//     * 流程任务第三方业务状态更新
//     *
//     * @param wfTaskThirdStatusDTO
//     * @return
//     */
//    @PostMapping("api/workflow/v2/operate/task/third/status")
//    Result updateThirdStatus(@RequestBody WfTaskThirdStatusDTO wfTaskThirdStatusDTO);
//
//    /**
//     * 获取任务节点配置
//     *
//     * @param taskId 任务节点id
//     * @return
//     */
//    @GetMapping("api/workflow/v2/config/current/node")
//    Result<WfDefNodeUserTaskDTO> getConfigOfCurrentNodeByTaskId(@RequestParam("taskId") String taskId);
//
//    /**
//     * 获取流程发起人和申请人
//     *
//     * @param businessKey 流程业务key
//     * @return
//     */
//    @GetMapping("api/workflow/v2/business/getInitiatorAndApplicant")
//    Result<WfInitiatorAndApplicantVo> getInitiatorAndApplicant(@RequestParam("businessKey") String businessKey);
//
//    /**
//     * 检查流程是否已启用
//     *
//     * @param funCode
//     * @return
//     */
//    @GetMapping("api/workflow/v2/config/def/checkEnabled")
//    Result<Boolean> checkDefEnabled(@RequestParam("funCode") String funCode);
}

2025-06-25 16:52:20.745 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-06-25 16:52:20.765 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-06-25 16:52:21.200 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-06-25 16:52:21.231 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@bb6366b6, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@f37a2353, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@9eea39fc, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@c9236fa0, org.springframework.test.context.support.DirtiesContextTestExecutionListener@d6174875, org.springframework.test.context.transaction.TransactionalTestExecutionListener@b9ab036b, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@efbf53df, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@2ee088cf, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@2ac854b5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@9300126f, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@d9342a50, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@218ba1ec]

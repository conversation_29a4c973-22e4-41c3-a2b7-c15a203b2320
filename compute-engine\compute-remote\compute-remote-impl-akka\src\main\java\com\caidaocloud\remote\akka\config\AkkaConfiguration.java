package com.caidaocloud.remote.akka.config;

import com.caidaocloud.compute.remote.framework.core.InitializerConfig;
import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.compute.remote.framework.core.RequestHandlerGenerator;
import com.caidaocloud.remote.akka.constant.AkkaConstant;
import com.caidaocloud.remote.akka.core.AkkaHandlerGenerator;
import com.caidaocloud.remote.akka.core.AkkaInitializer;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Configuration
@ConditionalOnProperty(value = "caidaocloud.remote.communication.type",havingValue = "akka")
public class AkkaConfiguration {

	@Bean
	public RemoteInitializer remoteInitializer(InitializerConfig initializerConfig){
		if (StringUtils.isEmpty(initializerConfig.getServerName())) {
			initializerConfig.setServerName(AkkaConstant.DEFAULT_AKKA_NAME);
		}
		return new AkkaInitializer(initializerConfig);
	}

	@Bean
	public RequestHandlerGenerator requestHandlerGenerator(RemoteInitializer remoteInitializer){
		return new AkkaHandlerGenerator(remoteInitializer);
	}

}

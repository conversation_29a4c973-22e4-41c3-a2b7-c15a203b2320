package com.caidaocloud.pangu.manager.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.manager.mapper.ArrangementNodeExecMapper;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class ArrangementNodeExec {

    private String id;

    public static final String identifier = "entity.pangu.ArrangementNodeExec";

    private String arrangementId;

    private String arrangementVid;

    private String execSeqId;

    private String execNodeSeqId;

    private String arrangementNodeId;

    private String arrangementNodeName;

    private String arrangementNodeType;

    private String detail;

    private Status status;

    private boolean skip;

    @DisplayAsObject
    private Map<String, String> context = Maps.map();

    private long waitFrom;

    private long started;

    private long ended;

    private String panguNodeId;

    private String host;

    private int port;

    private String cacheId;

    private String resultCacheId;

    private long total;

    private int step;

    private String actualTenantId;

    public static List<ArrangementNodeExec> listByExecSeqIdAndArrangementNodeIdList(String execSeqId, List<String> nextNodeIds) {
        return mapper().selectList(new LambdaQueryWrapper<ArrangementNodeExecPO>()
            .eq(ArrangementNodeExecPO::getExecSeqId, execSeqId)
            .in(ArrangementNodeExecPO::getArrangementNodeId, nextNodeIds)).stream().map(ArrangementNodeExecPO::toArrangementNodeExec).collect(Collectors.toList());
    }

    public static Optional<ArrangementNodeExec> first(Status status) {
        return mapper().selectList(new LambdaQueryWrapper<ArrangementNodeExecPO>()
                .eq(ArrangementNodeExecPO::getStatus, status)
                .orderByAsc(ArrangementNodeExecPO::getWaitFrom)).stream().findFirst().map(ArrangementNodeExecPO::toArrangementNodeExec);
    }

    public static Optional<ArrangementNodeExec> loadByExecNodeSeqIdAndStatus(String execNodeSeqId, Status status) {
        return Optional.ofNullable(mapper().selectOne(new LambdaQueryWrapper<ArrangementNodeExecPO>()
            .eq(ArrangementNodeExecPO::getExecNodeSeqId, execNodeSeqId)
            .eq(ArrangementNodeExecPO::getStatus, status))).map(ArrangementNodeExecPO::toArrangementNodeExec);
    }

    public static Optional<ArrangementNodeExec> loadByExecNodeSeqId(String execNodeSeqId) {
        return Optional.ofNullable(mapper().selectOne(new LambdaQueryWrapper<ArrangementNodeExecPO>()
                .eq(ArrangementNodeExecPO::getExecNodeSeqId, execNodeSeqId))).map(ArrangementNodeExecPO::toArrangementNodeExec);
    }


    public static Optional<ArrangementNodeExec> loadByExecNodeSeqIdAndStatusList(String execNodeSeqId, List<Status> statusList) {
        return Optional.ofNullable(mapper().selectOne(new LambdaQueryWrapper<ArrangementNodeExecPO>()
                .eq(ArrangementNodeExecPO::getExecNodeSeqId, execNodeSeqId)
                .in(ArrangementNodeExecPO::getStatus, statusList))).map(ArrangementNodeExecPO::toArrangementNodeExec);
    }

    public static List<ArrangementNodeExec> listByStatus(Status status) {
        return mapper().selectList(new LambdaQueryWrapper<ArrangementNodeExecPO>()
                .eq(ArrangementNodeExecPO::getStatus, status)).stream()
                .map(ArrangementNodeExecPO::toArrangementNodeExec).collect(Collectors.toList());
    }

    public static List<ArrangementNodeExec> listByPanguNodeIdAndStatus(String panguNodeId, Status status) {
        return mapper().selectList(new LambdaQueryWrapper<ArrangementNodeExecPO>()
                .eq(ArrangementNodeExecPO::getPanguNodeId, panguNodeId)
                .eq(ArrangementNodeExecPO::getStatus, status)).stream()
                .map(ArrangementNodeExecPO::toArrangementNodeExec).collect(Collectors.toList());
    }


    public void update() {
        mapper().updateById(ArrangementNodeExecPO.fromArrangementNodeExec(this));
    }

    public void create() {
        id = SnowUtil.nextId();
        mapper().insert(ArrangementNodeExecPO.fromArrangementNodeExec(this));
    }

    private static ArrangementNodeExecMapper mapper() {
        return SpringUtil.getBean(ArrangementNodeExecMapper.class);
    }

    public static List<ArrangementNodeExec> listByExecSeqId(String execSeqId) {
        LambdaQueryWrapper<ArrangementNodeExecPO> wrapper = new LambdaQueryWrapper<ArrangementNodeExecPO>().eq(ArrangementNodeExecPO::getExecSeqId, execSeqId);
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if(StringUtils.isNotEmpty(SecurityUserUtil.getSecurityUserInfo().getTenantId())){
            wrapper = wrapper.eq(ArrangementNodeExecPO::getActualTenantId, tenantId);
        }
        return mapper().selectList(wrapper)
                .stream()
                .map(ArrangementNodeExecPO::toArrangementNodeExec)
                .collect(Collectors.toList());
    }

    public enum Status{

        CHECK, WAITING, STARTED, LOADED, DISPATCHED, SUCCESS, SKIPPED, ERROR


    }


}

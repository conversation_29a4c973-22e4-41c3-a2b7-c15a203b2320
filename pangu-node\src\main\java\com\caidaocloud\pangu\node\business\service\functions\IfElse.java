package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.util.Map;

public class If<PERSON>lse extends AbstractFunction {
    @Override
    public String getName() {
        return "IF_ELSE";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2, AviatorObject aviatorObject3) {
        val test = aviatorObject1.getValue(env);
        AviatorObject result;
        if(Boolean.TRUE == test){
            result = aviatorObject2;
        }else{
            result = aviatorObject3;
        }
        if(FunctionLogTool.logEnabled()){
            val value1 = aviatorObject2.getValue(env);
            val value2 = aviatorObject3.getValue(env);
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(test).append(",").append(value1).append(",").append(value2).append(")=").append(result.getValue(env)).toString());
        }
        return result;
    }
}

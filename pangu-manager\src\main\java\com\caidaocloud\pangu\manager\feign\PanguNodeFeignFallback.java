package com.caidaocloud.pangu.manager.feign;

import com.caidaocloud.pangu.manager.dto.ArrangementDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
public class PanguNodeFeignFallback implements PanguNodeFeign{
    @Override
    public Result<ArrangementDto> fetchArrangement(String arrangementId) {
        return Result.fail();
    }

    @Override
    public Result<List<String>> fetchPreviousNodes(String arrangementVid, String arrangementNodeId) {
        return Result.fail();
    }

    @Override
    public Result<List<String>> fetchNextNodes(String arrangementVid, String arrangementNodeId) {
        return Result.fail();
    }

    @Override
    public Result<Map<String, List<String>>> fetchAllNodeNames(String arrangementVid) {
        return Result.fail();
    }
}

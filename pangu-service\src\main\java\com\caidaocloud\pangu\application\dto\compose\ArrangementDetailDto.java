package com.caidaocloud.pangu.application.dto.compose;

import java.util.List;

import com.googlecode.totallylazy.Lists;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
public class ArrangementDetailDto {
	private String name;//return to frontend when necessary

	private String arrangementId;

	private String arrangementVid;

	private Status status;

	private List<Node> nodes;

	@Data
	public static class Node {
		private String id;
	}

	public enum Status {

		DRAFT, PUBLISHED, RE_DRAFT, UPDATED, REPLACED;

		public Status toDisplay(){
			switch (this){
			case DRAFT: return DRAFT;
			case PUBLISHED: return PUBLISHED;
			case RE_DRAFT:
			case UPDATED: return RE_DRAFT;
			case REPLACED: return REPLACED;
			default: return null;
			}
		}
	}

}

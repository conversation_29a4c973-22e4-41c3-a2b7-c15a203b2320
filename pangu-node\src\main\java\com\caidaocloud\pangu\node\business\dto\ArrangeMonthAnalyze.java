package com.caidaocloud.pangu.node.business.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ArrangeMonthAnalyze extends DataSimple {
    @ApiModelProperty("开始日期")
    private Long startDate;
    @ApiModelProperty("结束日期")
    private Long endDate;
    @ApiModelProperty("员工主键")
    private String empId;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("姓名")
    private String empName;
    @ApiModelProperty("产品主键")
    private String productId;
    @ApiModelProperty("产品")
    private String productName;
    @ApiModelProperty("计薪工序主键")
    private String processId;
    @ApiModelProperty("计薪工序code")
    private String processCode;
    @ApiModelProperty("计薪工序")
    private String processName;
    @ApiModelProperty("排班工时")
    private String workTime;//bigDecimal
    @ApiModelProperty("异常工时")
    private String abnormalWorkTime;//bigDecimal
    @ApiModelProperty("实际工时")
    private String actualWorkTime;//bigDecimal
    @ApiModelProperty("有效工时")
    private String effectWorkTime;//bigDecimal

    @ApiModelProperty("完成数量")
    private Integer completedQuantity;//TEST 20          1-3

    @ApiModelProperty("入库数量")
    private Integer inventoryQuantity;//TEST 20       1-3

    @ApiModelProperty("工段")
    private DictSimple workshopSection;

    private String accountId;

    private String accountName;

    private String abnormalWorkTimeWage;

    private String actualCompletedQuantity;
}

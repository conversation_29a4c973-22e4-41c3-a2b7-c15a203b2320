package com.caidaocloud.pangu.core.ignite;

import java.io.Serializable;

import com.caidaocloud.security.util.SecurityUserUtil;

public class PgsqlDbContext implements DbContext, Serializable {
	private final String CREATE_TABLE_SQL_TEMPLATE = "CREATE TABLE IF NOT EXISTS \"%s\" (\"akey\" bytea PRIMARY KEY NOT NULL,\"val\" bytea);";
	private final String UPDATE_SQL_TEMPLATE = "update \"%s\" set val=? where akey=?";
	private final String INSERT_SQL_TEMPLATE = "insert into \"%s\" (akey, val) values (?, ?)";
	private final String DELETE_SQL_TEMPLATE = "delete from \"%s\" where akey=?";
	private final String LOAD_SQL_TEMPLATE = "select * from \"%s\" where akey=?";

	private final String connUrl;
	private final String user;
	private final String pwd;

	public PgsqlDbContext(String connUrl, String user, String pwd) {
		this.connUrl = connUrl;
		this.user = user;
		this.pwd = pwd;
	}

	@Override
	public String getConnUrl() {
		return connUrl;
	}

	@Override
	public String getUser() {
		return user;
	}

	@Override
	public String getPwd() {
		return pwd;
	}

	public String generateCreateTableSql(String cacheName) {
		return generateSql(CREATE_TABLE_SQL_TEMPLATE, cacheName);
	}

	public String generateUpdateSql(String cacheName) {
		return generateSql(UPDATE_SQL_TEMPLATE, cacheName);
	}

	public String generateInsertSql(String cacheName) {
		return generateSql(INSERT_SQL_TEMPLATE, cacheName);
	}

	public String generateDeleteSql(String cacheName) {
		return generateSql(DELETE_SQL_TEMPLATE, cacheName);
	}

	public String generateLoadSql(String cacheName) {
		return generateSql(LOAD_SQL_TEMPLATE, cacheName);
	}

	private String generateSql(String template, String cacheName) {
		return String.format(template, cacheName);
	}

	@Override
	public String toString() {
		return "PgsqlDbContext{" +
				"connUrl='" + connUrl + '\'' +
				", user='" + user + '\'' +
				", pwd='" + pwd + '\'' +
				'}';
	}
}

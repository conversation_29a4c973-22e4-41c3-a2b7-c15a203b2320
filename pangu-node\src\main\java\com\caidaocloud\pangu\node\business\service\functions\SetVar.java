package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.util.Map;

public class SetVar extends AbstractFunction {
    @Override
    public String getName() {
        return "SET_VAR";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val name = FunctionUtils.getStringValue(aviatorObject1, env);
        ExpressionTool.addParam(name, aviatorObject2);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(name).append(",").append(aviatorObject2.getValue(env)).append(")=").toString());
        }
        return aviatorObject2;
    }
}
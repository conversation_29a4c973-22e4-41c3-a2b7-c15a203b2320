package com.caidaocloud.pangu.application.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
@ApiModel("奖金账套员工查询DTO")
public class BonusLedgerEmpPageDto extends BasePage {
	@ApiModelProperty("账套bid")
	private String bid;

	private String keyword;

	@ApiModelProperty("合同公司")
	private String company;
	@ApiModelProperty("任职组织")
	private String organize;
	@ApiModelProperty("员工类型")
	private String empType;
	@ApiModelProperty("工作地")
	private String workPlace;

	private BonusLedgerEmpCalcStatus calcStatus;
}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class FunctionLogTool {

    @Data
    public static class FunctionLog extends DataSimple {

        public final String identifier = "entity.pangu.FunctionLog";

        private String key;
        private String execSeqId;
        private String execSeqNodeId;
        private String ruleName;
        private String ruleExpression;
        @DisplayAsArray
        private List<String> contents = Lists.list();
    }

    private static ThreadLocal<FunctionLog> tLogs = new ThreadLocal<>();

    public static boolean logEnabled(){
        return SpringUtil.getContext().getEnvironment().getProperty("calc.log.enabled", "0").equals("true");
    }

    public static void init(String key, String execSeqId, String execSeqNodeId, TaskDef.Rule rule){
        if(logEnabled()){
            val logs = new FunctionLog();
            logs.setKey(key);
            logs.setRuleName(rule.getName());
            logs.setRuleExpression(rule.getExpression());
            logs.setExecSeqId(execSeqId);
            logs.setExecSeqNodeId(execSeqNodeId);
            tLogs.set(logs);
        }
    }

    public static void log(String content){
        val logs = tLogs.get();
        if(logEnabled() && null != logs){
            logs.getContents().add(content);
        }
    }

    public static void flush(){
        val logs = tLogs.get();
        if(logEnabled() && null != logs){
            asyncToPaas(logs);
        }
    }

    private static void printConsole(FunctionLog logs){
        StringBuilder sb = new StringBuilder("\n==================================\n");
        sb.append("execSeqId:"+logs.getExecSeqId());
        sb.append("\n");
        sb.append("execSeqNodeId:"+logs.getExecSeqNodeId());
        sb.append("\n");
        sb.append("key"+logs.getKey());
        sb.append("\n");
        sb.append("rule name:"+logs.getRuleName());
        sb.append("\n");
        sb.append("rule expression:"+logs.getRuleExpression());
        sb.append("\n");
        logs.getContents().forEach(content->{
            sb.append(content);
            sb.append("\n");
        });
        logs.getContents().clear();
        tLogs.remove();
        log.info(sb.toString());
    }

    private static void asyncToPaas(FunctionLog logs){
        pool.submit(()->{
            DataInsert.identifier(logs.identifier).insert(logs);
        });
    }

    private static ExecutorService pool = Executors.newFixedThreadPool(3);

}

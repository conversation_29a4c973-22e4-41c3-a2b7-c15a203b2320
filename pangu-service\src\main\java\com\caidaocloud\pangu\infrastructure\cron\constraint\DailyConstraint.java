package com.caidaocloud.pangu.infrastructure.cron.constraint;

import com.caidaocloud.excption.ServerException;
import com.cronutils.model.Cron;
import com.cronutils.model.definition.CronConstraint;
import com.cronutils.model.field.CronFieldName;
import com.cronutils.model.field.expression.On;

/**
 *
 * <AUTHOR>
 * @date 2023/7/17
 */
public class DailyConstraint extends CronConstraint {
	public DailyConstraint(){
		super("The execution frequency of cron tasks is at most once a day");
	}

	/**
	 * 校验 cron 表达式，每天执行次数最多为1次，
	 * 例：0 0 0 ? * * * , 每天0点0分0秒执行一次
	 * @param cron
	 * @return
	 */
	@Override
	public boolean validate(Cron cron) {
		boolean flag = cron.retrieve(CronFieldName.SECOND).getExpression() instanceof On
				&& cron.retrieve(CronFieldName.MINUTE).getExpression() instanceof On
				&& cron.retrieve(CronFieldName.HOUR).getExpression() instanceof On;
		if (!flag) {
			throw new ServerException("The scheduled task can not be executed more than once a day.");
		}
		return true;
	}
}

package com.caidaocloud.pangu.infrastructure.repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.*;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.application.dto.compose.ComposeExecDto;
import com.caidaocloud.pangu.application.dto.workflow.WfTaskRevokeDTO;
import com.caidaocloud.pangu.application.feign.ComposeExecFeign;
import com.caidaocloud.pangu.application.feign.WfOperateFeignClient;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerEmp;
import com.caidaocloud.pangu.domain.entity.BonusApproveRecord;
import com.caidaocloud.pangu.domain.entity.BonusLedgerStep;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;


/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/24
 */
@Repository
@Slf4j
public class BonusLedgerRepositoryImpl implements IBonusLedgerRepository {
	@Autowired
	private ComposeExecFeign composeExecFeign;
	@Autowired
	private WfOperateFeignClient wfOperateFeignClient;

	@Override
	public String insert(BonusLedger bonusLedger) {
		return DataInsert.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).insert(bonusLedger);
	}

	@Override
	public void update(BonusLedger bonusLedger) {
		PageResult<BonusLedger> exist = DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER)
				.filter(DataFilter.eq("name", bonusLedger.getName())
						.andNe("bid", bonusLedger.getBid()).andNe("deleted", Boolean.TRUE.toString()), BonusLedger.class);
		if (!exist.getItems().isEmpty()) {
			throw new ServerException("Bonus ledger name '" + bonusLedger.getName() + "' already exists");
		}
		DataUpdate.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).update(bonusLedger);
	}

	@Override
	public void delete(String bid) {
		DataDelete.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).softDelete(bid);
	}

	@Override
	public BonusLedger loadByBid(String bid) {
		return DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).queryInvisible().specifyLanguage().decrypt()
				.oneOrNull(bid, BonusLedger.class);
	}

	@Override
	public PageResult<BonusLedgerEmp> loadAllEmp(BonusLedgerEmpPageDto queryDto, String ledgerId, int pageNo, int pageSize) {
		DataFilter filter = DataFilter.ne("deleted", Boolean.TRUE.toString());
		if (StringUtils.isNotEmpty(queryDto.getKeyword())){
			filter=filter.and(DataFilter.regex("workno", queryDto.getKeyword()).orRegex("name", queryDto.getKeyword()));
		}
		filter = (DataFilter) queryDto.doDataFilter(queryDto.getFilters(), filter);
		DataJoin.ModelInfo workInfo = DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", Lists.list(), filter);

		filter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.andEq("ledgerId", ledgerId);
		DataJoin.ModelInfo ledgerEmp = DataJoin.ModelInfo.model(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER, Lists.list("bid", "ledger_id", "emp_id","status"), filter);

		DataJoin join = DataJoin.joinModels(ledgerEmp,workInfo,
				DataJoin.JoinInfo.joinInfo(Lists.list(DataJoin.JoinPropertyInfo.joinProperty(ledgerEmp.getIdentifier(), workInfo.getIdentifier(), "empId", "empId"))));
		join.limit(pageSize, pageNo);
		PageResult<Triple<BonusLedgerEmp, BonusLedgerEmp, BonusLedgerEmp>> result = join.join(BonusLedgerEmp.class, System.currentTimeMillis());
		return new PageResult<>(Sequences.sequence(result.getItems()).map(Triple::getLeft)
				.toList(), result.getPageNo(), result.getPageSize(), result.getTotal());
	}

	@Override
	public void syncEmp(String ledgerId, List<BonusLedgerEmp> emps) {
		List<String> empIds = Sequences.sequence(emps).map(BonusLedgerEmp::getEmpId).toList();
		DataDelete.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).batchDelete(DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
				.andNotInIf("empId", empIds,()->!empIds.isEmpty())
				.andEq("ledgerId", ledgerId));
		DataDelete.identifier(BonusReport.BONUS_REPORT_IDENTIFIER)
				.batchDelete(DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
						.andNotInIf("empId", empIds,()->!empIds.isEmpty())
						.andEq("ledgerId", ledgerId));
		Map<String, List<BonusLedgerEmp>> exist = Sequences.sequence(loadSimpleEmp(ledgerId, empIds)).toMap(BonusLedgerEmp::getEmpId);
		
		List<BonusLedgerEmp> newEmps = emps.stream()
			.filter(emp -> !exist.containsKey(emp.getEmpId()))
				.peek(emp-> emp.setDataEndTime(DateUtil.MAX_TIMESTAMP))
			.collect(Collectors.toList());

		DataInsert.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).batchInsert(newEmps);
	}

	@Override
	public void addEmp(String ledgerId, List<BonusLedgerEmp> emps) {
		Map<String, List<BonusLedgerEmp>> exist = Sequences.sequence(loadSimpleEmp(ledgerId, Sequences.sequence(emps).map(BonusLedgerEmp::getEmpId).toList())).toMap(BonusLedgerEmp::getEmpId);

		List<BonusLedgerEmp> newEmps = emps.stream()
				.filter(emp -> !exist.containsKey(emp.getEmpId()))
				.peek(emp-> emp.setDataEndTime(DateUtil.MAX_TIMESTAMP))
				.collect(Collectors.toList());

		DataInsert.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).batchInsert(newEmps);
	}

	@Override
	public void removeEmp(BonusLedgerEmp emp) {
		PageResult<BonusLedgerEmp> result = DataQuery.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.queryInvisible().decrypt().specifyLanguage()
				.filter(DataFilter.eq("ledgerId", emp.getLedgerId())
						.andEq("empId", emp.getEmpId()), BonusLedgerEmp.class);
		if (result.getItems().isEmpty()) {
			log.warn("Bouns ledger emp not exist");
			return;
		}
		BonusLedgerEmp accountEmp = result.getItems().get(0);
		DataDelete.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).delete(accountEmp.getBid());
	}

	@Override
	public PageResult<BonusLedger> page(String name, int pageNo, int pageSize) {
		return DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).queryInvisible().specifyLanguage().decrypt()
				.limit(pageSize, pageNo)
				.filter(DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
						.andNe("deleted", Boolean.TRUE.toString())
						.andRegexIf("name", name, () -> StringUtils.isNotEmpty(name)), BonusLedger.class);
	}

	@Override
	public List<BonusLedger> loadOpen() {
		DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString())
				.andEq("status", BonusLedgerStatus.OPEN.name());
		return DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).queryInvisible().decrypt().specifyLanguage()
				.limit(-1, 1)
				.filter(dataFilter, BonusLedger.class).getItems();
	}

	@Override
	public List<BonusLedgerEmp> loadSimpleEmp(String bid, List<String> empIds) {
		return DataQuery.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER)
				.queryInvisible().specifyLanguage().decrypt()
				.limit(-1, 1)
				.filter(DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
						.andInIf("empId", empIds, () -> !CollectionUtils.isEmpty(empIds))
						.andEq("ledgerId", bid), BonusLedgerEmp.class).getItems();
	}

	@Override
	public void updateEmp(BonusLedgerEmp emp) {
		DataUpdate.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).update(emp);
	}

	@Override
	public void saveApproveReceipt(BonusApproveRecord bonusApproveRecord) {
		DataInsert.identifier(BonusApproveRecord.RECEIPT_IDENTIFIER).insert(bonusApproveRecord);
	}

	@Override
	public BonusApproveRecord loadApproveReceiptByBusinessId(String businessId) {
		PageResult<BonusApproveRecord> result = DataQuery.identifier(BonusApproveRecord.RECEIPT_IDENTIFIER)
				.filter(DataFilter.eq("businessId", businessId), BonusApproveRecord.class);
		return result.getItems().isEmpty() ? null : result.getItems().get(0);
	}

	@Override
	public BonusApproveRecord loadApproveReceiptByLedgerId(String accountBid) {
		PageResult<BonusApproveRecord> result = DataQuery.identifier(BonusApproveRecord.RECEIPT_IDENTIFIER)
				.filter(DataFilter.eq("ledgerId", accountBid), BonusApproveRecord.class);
		return result.getItems().isEmpty() ? null : result.getItems().get(0);

	}

	@Override
	public List<BonusLedger> loadBySchemaId(String bid) {
		return DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).limit(-1, 1)
				.filter(DataFilter.eq("schemaId", bid)
						.andNe("deleted", Boolean.TRUE.toString()), BonusLedger.class).getItems();
	}

	@Override
	public List<BonusLedgerEmp> loadAccountEmpByAccountBidAndEmpId(List<String> accountBids, String empId) {
		return DataQuery.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER).limit(-1,1)
				.filter(DataFilter
						.ne("deleted", Boolean.TRUE.toString())
						.andIn("ledgerId", accountBids)
						.andEq("empId", empId)
						.andEq("calcStatus", BonusLedgerEmpCalcStatus.DONE.name()), BonusLedgerEmp.class)
				.getItems();
	}

	@Override
	public void saveStep(BonusLedgerStep step) {
		DataInsert.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER).insert(step);
	}

	@Override
	public List<BonusLedgerStep> loadStep(String ledgerId) {
		return DataQuery.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.limit(-1, 1)
				.filter(DataFilter.eq("ledgerId", ledgerId).andNe("deleted", Boolean.TRUE.toString()),
						BonusLedgerStep.class).getItems();
	}

	@Override
	public BonusLedgerStep loadStepById(String stepId) {
		return DataQuery.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.oneOrNull(stepId, BonusLedgerStep.class);
	}

	@Override
	public void updateStep(BonusLedgerStep step) {
		DataUpdate.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.update(step);
	}

	@Override
	public void exec(BonusLedgerStep currentStep, Map<String, String> context) {
		String exec = composeExecFeign.exec(new ComposeExecDto(currentStep.getComposeId(), context)).getData();
		currentStep.setExecId(exec);
		updateStep(currentStep);
	}

	@Override
	public void removeEmpBatch(String bid, List<String> empId) {
		DataDelete.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER)
				.batchDelete(DataFilter.eq("ledgerId", bid).andIn("empId", empId));
	}

	@Override
	public BonusLedgerStep loadStepByExecId(String execId) {
		List<BonusLedgerStep> list = DataQuery.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.filter(DataFilter.eq("execId", execId), BonusLedgerStep.class).getItems();
		return list.isEmpty() ? null : list.get(0);
	}

	@Override
	public void deleteStepByLedgerId(String bid) {
		DataDelete.identifier(BonusLedgerStep.BONUS_LEDGER_STEP_IDENTIFIER)
				.batchDelete(DataFilter.eq("ledgerId", bid));
	}

	@Override
	public void revoke(String businessKey) {
		WfTaskRevokeDTO wfTaskRevokeDTO = new WfTaskRevokeDTO();
		wfTaskRevokeDTO.setBusinessKey(businessKey);
		wfOperateFeignClient.revokeProcessOfTask(wfTaskRevokeDTO);
	}

	@Override
	public List<BonusLedger> findByIds(List<String> ids) {
		return DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER).limit(-1, 1)
				.filter(DataFilter.in("bid", ids)
						.andNe("deleted", Boolean.TRUE.toString()), BonusLedger.class).getItems();

	}

	@Override
	public int countEmpByLedgerId(String bid) {
		DataFilter filter = DataFilter.eq("ledgerId", bid)
				.andNe("deleted", Boolean.TRUE.toString());
		long count = DataQuery.identifier(BonusLedgerEmp.BONUS_LEDGER_EMP_IDENTIFIER)
				.count(filter, System.currentTimeMillis());
		return (int) count;
	}

}

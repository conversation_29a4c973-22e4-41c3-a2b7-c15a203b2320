import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.pangu.core.ignite.DatasourceProperties;
import com.caidaocloud.pangu.core.ignite.IgniteCacheContext;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import lombok.SneakyThrows;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
import org.apache.ignite.configuration.CacheConfiguration;
import org.apache.ignite.configuration.DeploymentMode;
import org.apache.ignite.configuration.IgniteConfiguration;
import org.junit.Assert;
import org.junit.Test;

public class IgniteClientTest {

    @SneakyThrows
    @Test
    public void it() {
            IgniteConfiguration configuration = new IgniteConfiguration();
            configuration.setPeerClassLoadingEnabled(true);
            configuration.setDeploymentMode(DeploymentMode.CONTINUOUS);
            configuration.setWorkDirectory("C:\\ignite_master");
        configuration.setClientMode(true);
            try (Ignite ignite = Ignition.start(configuration);) {

                DatasourceProperties properties = new DatasourceProperties();
                properties.setJdbcUrl("***************************************************************************");
                properties.setDriverClassName("org.postgresql.Driver");
                properties.setUsername("postgres");
                properties.setPassword("Caidao2022");

                IgniteUtil igniteUtil = new IgniteUtil(ignite, properties);
                // try( IgniteCacheContext blob = igniteUtil.keyValueCache("tmp_blob2", String.class, Model1.class)){
                CacheConfiguration<Object, Object> cacheConfiguration = new CacheConfiguration<>();
                cacheConfiguration.setName("tmp_blob4");
                cacheConfiguration.setWriteThrough(true);
                cacheConfiguration.setReadThrough(true);
                CacheJdbcBlobStoreFactory<Object, Object> blobStoreFactory = new CacheJdbcBlobStoreFactory<>();
                blobStoreFactory.setConnectionUrl(properties.getJdbcUrl());
                blobStoreFactory.setUser(properties.getUsername());
                blobStoreFactory.setPassword(properties.getPassword());
                cacheConfiguration.setCacheStoreFactory(blobStoreFactory);
                // try( IgniteCache blob = ignite.getOrCreateCache("tmp_blob3")){
                try (IgniteCache blob = ignite.getOrCreateCache(cacheConfiguration)) {
                    System.out.println(">>>>>>>>>>>>> cache start");
                    blob.put("1", new Model1("1", "test2"));
                    Model1 model1 = (Model1) blob.get("1");
                    Assert.assertEquals("test2", model1.getName());
                }
                finally {
                    ignite.destroyCache("tmp_blob2");
                }
            }

    }}



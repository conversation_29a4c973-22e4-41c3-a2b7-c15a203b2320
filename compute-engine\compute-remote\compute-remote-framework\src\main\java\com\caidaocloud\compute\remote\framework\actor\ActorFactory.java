package com.caidaocloud.compute.remote.framework.actor;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ActorFactory {

    public static Optional<ActorInfo>

    buildActor(Object bean) {
        RemoteHandler handler = bean.getClass().getAnnotation(RemoteHandler.class);
        if (handler == null) {
            return Optional.empty();
        }
        ActorInfo actorInfo = new ActorInfo(bean, handler);
        actorInfo.setHandlerInfos(loadHandlerInfos4Actor(actorInfo));
        return Optional.of(actorInfo);
    }

    private static List<HandlerInfo> loadHandlerInfos4Actor(ActorInfo actorInfo) {
        List<HandlerInfo> ret = Lists.newArrayList();

        RemoteHandler anno = actorInfo.getDefinition();
        String rootPath = anno.value();
        Object actor = actorInfo.getActor();

        findHandlerMethod(rootPath, actor.getClass(), ret);
        return ret;
    }

    private static void findHandlerMethod(String rootPath, Class<?> clz, List<HandlerInfo> result) {
        Method[] declaredMethods = clz.getDeclaredMethods();
        for (Method handlerMethod : declaredMethods) {
            RemoteHandler handlerMethodAnnotation = handlerMethod.getAnnotation(RemoteHandler.class);
            if (handlerMethodAnnotation == null) {
                continue;
            }

            HandlerInfo handlerInfo = new HandlerInfo()
                    .setDefinition(handlerMethodAnnotation)
                    .setMethod(handlerMethod);
            result.add(handlerInfo);
        }

        // 递归处理父类
        final Class<?> superclass = clz.getSuperclass();
        if (superclass != null) {
            findHandlerMethod(rootPath, superclass, result);
        }
    }

    static String suitPath(String path) {
        if (path.startsWith("/")) {
            return path.replaceFirst("/", "");
        }
        return path;
    }
}

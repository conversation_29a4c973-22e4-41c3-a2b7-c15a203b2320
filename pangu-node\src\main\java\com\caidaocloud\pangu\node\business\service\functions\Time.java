package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class Time extends AbstractFunction {
    @Override
    public String getName() {
        return "TIME";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val time = FunctionUtils.getStringValue(aviatorObject1, env);
        val pattern = FunctionUtils.getStringValue(aviatorObject2, env).replaceAll("T", "'T'");
        LocalDateTime result;
        if(pattern.contains("HH")){
            result = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(pattern));
        }else{
            result = LocalDate.parse(time, DateTimeFormatter.ofPattern(pattern)).atStartOfDay();
        }
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(time).append(",").append(pattern).append(")=").append(result).toString());
        }
        return new AviatorTimestamp(result);
    }

    public static void main(String[] args) {
        LocalDateTime.parse("20240302", DateTimeFormatter.ofPattern("yyyyMMdd"));
    }
}

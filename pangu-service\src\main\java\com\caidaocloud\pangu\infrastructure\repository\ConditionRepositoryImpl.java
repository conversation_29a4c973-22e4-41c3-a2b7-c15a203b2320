package com.caidaocloud.pangu.infrastructure.repository;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Deque;
import java.util.List;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.filter.SpecifiedMultiDataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.paas.match.ConditionExp;
import com.caidaocloud.hrpaas.paas.match.ConditionNode;
import com.caidaocloud.hrpaas.paas.match.ConditionNodeRelationEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionNodeTypeEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.pangu.domain.repository.IConditionRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/25
 */
@Repository
public class ConditionRepositoryImpl implements IConditionRepository {

	@Override
	public List<EmpSimple> fetchMatchedEmp(ConditionTree condition) {
		ConditionNode node = new ConditionNode();
		node.setRelation(condition.getRelation());
		node.setChildren(condition.getChildren());
		SpecifiedMultiDataFilter dataFilter = parseGroupNode(node);
		dataFilter = dataFilter.and(DataFilter.specifiedAnd("entity.hr.EmpWorkInfo")
				.andNe("deleted", Boolean.TRUE.toString()));
		return DataQuery.identifier("").loadByCondition(dataFilter);
	}

	private SpecifiedMultiDataFilter parseGroupNode(ConditionNode condition) {
		SpecifiedMultiDataFilter dataFilter = null;
		Deque<SpecifiedMultiDataFilter> dq = new ArrayDeque<>();
		ConditionNodeRelationEnum relation = condition.getRelation();
		for (ConditionNode child : condition.getChildren()) {
			if (child.getType() == ConditionNodeTypeEnum.group) {
				dq.add(parseGroupNode(child));
			}
			else {
				ConditionExp exp = child.getCondition();
				String identifier = StringUtils.substringBefore(exp.getName(), "#"),
						property = StringUtils.substringAfter(exp.getName(), "#");
				if (dataFilter == null) {
					if (relation == ConditionNodeRelationEnum.or) {
						dataFilter = DataFilter.specifiedOr(identifier);
					}
					else {
						dataFilter = DataFilter.specifiedAnd(identifier);
					}
					addToFilter(dataFilter, exp, property);
				}
				else if (!dataFilter.getIdentifier().identifier.equals(identifier)) {
					dq.add(parseSingleNode(child));
				}
				else {
					addToFilter(dataFilter, exp, property);
				}
			}
		}
		while (!dq.isEmpty()) {
			if (relation == ConditionNodeRelationEnum.or) {
				dataFilter = dataFilter == null ? dq.pop() : dataFilter.or(dq.pop());
			}
			else {
				dataFilter = dataFilter == null ? dq.pop() : dataFilter.and(dq.pop());
			}
		}
		return dataFilter;
	}

	private void addToFilter(SpecifiedMultiDataFilter dataFilter, ConditionExp exp, String property) {
		switch (exp.getSymbol()) {
		case EQ:
			dataFilter.andEq(property, exp.getSimpleValue());
			break;
		case NE:
			dataFilter.andNe(property, exp.getSimpleValue());
			break;
		case GE:
			dataFilter.andGe(property, exp.getSimpleValue());
			break;
		case GT:
			dataFilter.andGt(property, exp.getSimpleValue());
			break;
		case LE:
			dataFilter.andLe(property, exp.getSimpleValue());
			break;
		case LT:
			dataFilter.andLt(property, exp.getSimpleValue());
			break;
		case IN:
			dataFilter.andIn(property, Arrays.asList(exp.getSimpleValue().split(",")));
			break;
		case CONTAIN:
			dataFilter.andRegex(property, exp.getSimpleValue());
			break;
		case CONTAIN_CHILD:
			DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
					.and(DataFilter.eq("bid", exp.getSimpleValue()).orRegex("pid$path", exp.getSimpleValue()))
					.andEq("deleted", Boolean.FALSE.toString());
			PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hr.Org")
					.decrypt().specifyLanguage()
					.queryInvisible().limit(5000, 1)
					.filter(filter, DataSimple.class, System.currentTimeMillis());
			List<String> pidList = pageResult.getItems().stream()
					.map(AbstractData::getBid).collect(Collectors.toList());
			dataFilter.andIn(property, pidList);
			break;
		default:
			throw new ServerException("Exp not support");
		}
	}

	private SpecifiedMultiDataFilter parseSingleNode(ConditionNode node) {
		ConditionExp exp = node.getCondition();
		String identifier = StringUtils.substringBefore(exp.getName(), "#"),
				property = StringUtils.substringAfter(exp.getName(), "#");

		SpecifiedMultiDataFilter dataFilter = DataFilter.specifiedAnd(identifier);
		addToFilter(dataFilter, exp, property);
		return dataFilter;
	}

	// private PageResult<DataSimple> loadPreDataFromMasterdata(int pageNo) {
	// 	String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
	// 	PageResult<DataSimple> workInfos = MdDataQuery.identifier("entity.hr.EmpWorkInfo").specifyLanguage().decrypt()
	// 			.queryInvisible()
	// 			.limit(++pageNo, pageSize)
	// 			.filter(DataFilter.eq("tenantId", tenantId), DataSimple.class);
	// 	List<String> empIds = Sequences.sequence(workInfos.getItems())
	// 			.map(item -> ((SimplePropertyValue) item.getProperties().get("empId")).getValue()).toList();
	// 	Map<String, List<DataSimple>> workInfoMap = Sequences.sequence(workInfos.getItems())
	// 			.toMap(item -> ((SimplePropertyValue) item.getProperties().get("empId")).getValue());
	// 	for (String identifier : IDENTIFIERS) {
	// 		PageResult<DataSimple> extendDataPage = MdDataQuery.identifier(identifier).specifyLanguage().decrypt()
	// 				.queryInvisible()
	// 				.filter(DataFilter.in("empId", empIds), DataSimple.class);
	// 		for (DataSimple item : extendDataPage.getItems()) {
	// 			String empId = ((SimplePropertyValue) item.getProperties().get("empId")).getValue();
	// 			workInfoMap.get(empId).get(0).getProperties().putAll(item.getProperties());
	// 		}
	// 	}
	// 	return workInfos;
	// }
}

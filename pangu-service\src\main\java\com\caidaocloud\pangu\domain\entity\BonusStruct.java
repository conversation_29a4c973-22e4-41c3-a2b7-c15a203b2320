package com.caidaocloud.pangu.domain.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.domain.repository.IBonusStructComposeRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructItemRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructReportItemRepository;
import com.caidaocloud.pangu.domain.repository.IBonusStructRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.util.*;

/**
 * 奖金结构
 */
@Data
public class BonusStruct extends DataSimple {

    private String name;

    @DisplayAsObject
    private Map<String,String> i18nName;

    @DisplayAsArray
    private List<String> basic;

    private EnumSimple executionType;

    private List<BonusStructItem> bonusStructItems;

    private List<BonusStructReportItem> bonusStructReportItems;


    private List<BonusStructCompose> bonusStructComposes;


    public static final String BONUS_STRUCT_IDENTIFIER = "entity.bonus.BonusStruct";

    public BonusStruct() {
        setIdentifier(BONUS_STRUCT_IDENTIFIER);
    }

    public BonusStruct(Map<String,String> i18nName) {
        this();
        this.i18nName = i18nName;
        this.name = i18nName.get("default").trim();
        this.executionType = ExecutionType.ALL.toValue();
    }

    public static BonusStructItem loadItem(String itemBid) {
        return SpringUtil.getBean(IBonusStructItemRepository.class).selectById(itemBid,BonusStructItem.BONUS_STRUCT_ITEM_IDENTIFIER);
    }

    public static List<BonusStructCompose> listComposeItems(String structId) {
        return SpringUtil.getBean(IBonusStructComposeRepository.class).selectList(structId);
    }

    public static BonusStructCompose loadCompose(String bid) {
        return SpringUtil.getBean(IBonusStructComposeRepository.class)
                .selectById(bid, BonusStructCompose.BONUS_STRUCT_COMPOSE_IDENTIFIER);
    }

    public static List<BonusStructItem> listItems(String structId) {
        return SpringUtil.getBean(IBonusStructItemRepository.class).selectList(structId);
    }

    public String create() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        setCreateTime(getUpdateTime());
        setCreateBy(getUpdateBy());
        if(DataQuery.identifier(BONUS_STRUCT_IDENTIFIER)
                .count(DataFilter.eq("name", name).andNe("deleted", Boolean.TRUE.toString()), System.currentTimeMillis()) > 0){
            throw new ServerException("名称重复");
        }
        SpringUtil.getBean(IBonusStructRepository.class).insert(this);
        return this.getBid();
    }

    public void update(){
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        if (DataQuery.identifier(BONUS_STRUCT_IDENTIFIER)
                .count(DataFilter.eq("name", name).andNe("deleted", Boolean.TRUE.toString())
                        .andNe("bid", getBid()), System.currentTimeMillis()) > 0) {
            throw new ServerException("名称重复");
        }
        SpringUtil.getBean(IBonusStructRepository.class).updateById(this);
    }


    public String addItem(BonusStructItem item) {
        String itemBid = item.create();

        BonusStructReportItem reportItem = new BonusStructReportItem(getBid(), itemBid);

        reportItem.create();
        return itemBid;
    }

    public static BonusStruct load(String bid){
        BonusStruct struct = SpringUtil.getBean(IBonusStructRepository.class).selectById(bid, BONUS_STRUCT_IDENTIFIER);
        struct.defaultBasic();
        return struct;
    }

    public static PageResult<BonusStruct> page(BasePage page,String keyword){
        return SpringUtil.getBean(IBonusStructRepository.class).selectPage(page, keyword);
    }

    public static List<BonusStruct> list(){
        return SpringUtil.getBean(IBonusStructRepository.class)
                .selectList(BONUS_STRUCT_IDENTIFIER, SecurityUserUtil.getSecurityUserInfo()
                        .getTenantId());
    }

    public void delete() {
        setUpdateTime(System.currentTimeMillis());
        setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
        SpringUtil.getBean(IBonusStructRepository.class).delete(this);

    }

    public static PageResult<BonusStructItem> pageItems(BasePage page, String structId, String keywords) {
        return SpringUtil.getBean(IBonusStructItemRepository.class).selectPage(page, keywords, structId);
    }

    public List<MetadataPropertyVo> basicProperties() {
        MetadataVo workInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpWorkInfo");
        MetadataVo privateInfo = SpringUtil.getBean(MetadataOperatorService.class).load("entity.hr.EmpPrivateInfo");
        Map<String, MetadataVo> map = Maps.map("entity.hr.EmpWorkInfo", workInfo, "entity.hr.EmpPrivateInfo", privateInfo);
        return Sequences.sequence(getBasic()).map(b -> {
            String[] s = b.split("@");
            return Sequences.sequence(map.get(s[0]).fetchAllProperties()).find(p -> p.getProperty().equals(s[1])).getOrNull();
        }).filter(Objects::nonNull).toList();
    }

    public static List<BonusStructItem> listItems(List<String> idList) {
        if (idList.isEmpty()) {
            return Collections.emptyList();
        }
        return SpringUtil.getBean(IBonusStructItemRepository.class).selectListByStructIds(idList);
    }

    public  List<BonusStructItem> listItems() {
        return SpringUtil.getBean(IBonusStructItemRepository.class).selectList(getBid());
    }

    public List<BonusStructReportItem> listReportItems() {
        return SpringUtil.getBean(IBonusStructReportItemRepository.class).selectList(getBid());
    }

    public void saveReportSetting(List<String> basic, List<String> item) {
        this.basic = basic;
        // defaultBasic();
        update();

        List<BonusStructReportItem> items = Sequences.sequence(item).map(i -> new BonusStructReportItem(getBid(), i)).toList();
        SpringUtil.getBean(IBonusStructReportItemRepository.class).saveList(getBid(), items);
    }

    private void defaultBasic() {
        if (!basic.contains("entity.hr.EmpWorkInfo@name")) {
            basic.add(0, "entity.hr.EmpWorkInfo@name");
        }
        if (!basic.contains("entity.hr.EmpWorkInfo@workno")) {
            basic.add(0, "entity.hr.EmpWorkInfo@workno");
        }
    }

    public void saveReportSettingOrder(List<String> basic, List<String> item) {
        this.basic = basic;
        update();

        List<BonusStructReportItem> items = new ArrayList<>();
        for (int i = 0, itemSize = item.size(); i < itemSize; i++) {
            String itemId = item.get(i);
            items.add(new BonusStructReportItem(getBid(), itemId, i));
        }
        SpringUtil.getBean(IBonusStructReportItemRepository.class).saveList(getBid(), items);
    }

    public String addCompose(String composeId) {
        BonusStructCompose compose = new BonusStructCompose(getBid(), composeId);
        compose.create();
        return compose.getBid();
    }

    public void saveComposeOrder(List<String> composeItemId) {
        List<BonusStructCompose> exist = listComposeItems(getBid());
        for (int i = 0, composeItemIdSize = composeItemId.size(); i < composeItemIdSize; i++) {
            String bid = composeItemId.get(i);
            Option<BonusStructCompose> option = Sequences.sequence(exist).find(item -> item.getBid().equals(bid));
            if (option.isEmpty()) {
                throw new ServerException("Compose item do not exist");
            }
            BonusStructCompose compose = option.get();
            compose.setSort(i);
            compose.update();
        }
    }

    public List<BonusStructCompose> listCompose() {
        return listComposeItems(getBid());
    }
}

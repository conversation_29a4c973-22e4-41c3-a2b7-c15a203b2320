package com.caidaocloud.comput.server;

import com.caidaocloud.compute.remote.framework.scan.EnableRemote;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
@SpringBootApplication
@EnableRemote
@Slf4j
public class ServerApplication implements InitializingBean {

	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(ServerApplication.class, args);
//		context.getBean(ClientApi.class).hello(new Address("**************",25252));
//		System.out.println(context.getBean(ClientApi.class)
//				.helloMessage(new Address("**************", 25252), new Hello("test")));
		// System.out.println(context.getBean(ClientApi.class).dispatchTask(new Address("127.0.0.1", 55373), "123"));
		context.stop();
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		// clientApi.hello();
	}
}

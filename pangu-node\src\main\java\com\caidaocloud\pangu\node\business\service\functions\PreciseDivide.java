package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class PreciseDivide extends AbstractFunction {
    @Override
    public String getName() {
        return "DIVIDE_P";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject1, AviatorObject aviatorObject2, AviatorObject roundingMode, AviatorObject precise) {
        val first = FunctionUtils.getNumberValue(aviatorObject1, env);
        val second= FunctionUtils.getNumberValue(aviatorObject2, env);
        val rounding = RoundingMode.valueOf(FunctionUtils.getStringValue(roundingMode, env));
        val precision = FunctionUtils.getNumberValue(precise, env).intValue();
        BigDecimal result = new BigDecimal(first.toString())
                .divide(new BigDecimal(second.toString()), precision, rounding);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(first).append(",").append(second).append(",").append(rounding).append(",").append(precision).append(")=").append(result).toString());
        }
        return new AviatorDecimal(result);
    }

}

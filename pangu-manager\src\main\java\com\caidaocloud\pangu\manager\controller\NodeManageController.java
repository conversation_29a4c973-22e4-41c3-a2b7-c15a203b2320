package com.caidaocloud.pangu.manager.controller;

import com.caidaocloud.pangu.core.dto.NodeSyncStatus;
import com.caidaocloud.pangu.core.dto.RegisterResult;
import com.caidaocloud.pangu.core.dto.ServerInstance;
import com.caidaocloud.pangu.manager.service.NodeManagerService;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/calc/v1/manage/node")
@Slf4j
public class NodeManageController {

    @Autowired
    private NodeManagerService nodeManagerService;

    @PostMapping(value = "/register")
    public Result<RegisterResult> register(@RequestBody ServerInstance serverInstance){
        return Result.ok(nodeManagerService.register(serverInstance));
    }
    @PostMapping(value = "/heartbeat")
    public Result<NodeSyncStatus> freshHeartbeat(@RequestBody ServerInstance serverInstance){
        return Result.ok(nodeManagerService.freshHeartbeat(serverInstance));
    }

}

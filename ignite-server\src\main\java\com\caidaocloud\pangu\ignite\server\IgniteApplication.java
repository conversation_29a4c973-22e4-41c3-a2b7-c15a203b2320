package com.caidaocloud.pangu.ignite.server;

import javax.annotation.PostConstruct;

import org.apache.ignite.Ignite;
import org.apache.ignite.configuration.DataRegionConfiguration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 *
 * <AUTHOR>
 * @date 2025/3/3
 */
@SpringBootApplication
public class IgniteApplication {
	// @Autowired
	// private Ignite ignite;

	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(IgniteApplication.class, args);
		DataRegionConfiguration region = context.getBean(Ignite.class).configuration().getDataStorageConfiguration()
				.getDefaultDataRegionConfiguration();
		System.out.println("Data Region Name: " + region.getName());
		System.out.println("Max Size: " + region.getMaxSize());
		System.out.println("Persistence Enabled: " + region.isPersistenceEnabled());
	}
}


package com.caidaocloud.pangu.node.business.model;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ArrangementReferenced extends DataSimple {

    public static final String identifier = "entity.pangu.ArrangementReferenced";

    private String arrangementId;

    public static boolean referenced(String arrangementId) {
        return DataQuery.identifier(identifier).count(DataFilter.eq("arrangementId", arrangementId),
                System.currentTimeMillis()) > 0;
    }

    public void reference(){
        if(!referenced(arrangementId)){
            DataInsert.identifier(identifier).insert(this);
        }
    }
}

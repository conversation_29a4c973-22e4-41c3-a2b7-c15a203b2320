package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.pangu.application.dto.compose.ComposeDefDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementQueryDto;
import com.caidaocloud.pangu.application.dto.compose.ArrangementDetailDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@FeignClient(value = "${feign.rename.caidaocloud-pangu-node:caidaocloud-pangu-node}", fallback = ComposeFeignFallback.class, configuration = FeignConfiguration.class)
public interface ComposeFeign {
	@GetMapping("/api/arrangement/v1/arrangement/list/published?type=BONUS")
	Result<List<ComposeDefDto>> listComposeDef(@RequestBody List<String> composeIds);

	@PostMapping(value = "/api/arrangement/v1/arrangement/reference")
	Result<Boolean> reference(@RequestParam String arrangementId);

	@PostMapping(value = "/api/arrangement/v1/arrangement/detail/published/list")
	Result<List<ArrangementDetailDto>> loadDetailPublishedList(@RequestBody ArrangementQueryDto dto);

	@PostMapping(value = "/api/arrangement/v1/arrangement/detail/version")
	Result<ArrangementDetailDto> loadDetailVersion(@RequestBody ArrangementQueryDto dto);

	@GetMapping("/api/arrangement/v1/arrangement/load/published")
	Result<ComposeDefDto> loadPublished(@RequestParam(required = false) String arrangementId);

	@GetMapping(value = "/api/arrangement/v1/arrangement/detail/published")
	Result<ArrangementDetailDto> loadDetailPublished(@RequestParam String arrangementId);

}

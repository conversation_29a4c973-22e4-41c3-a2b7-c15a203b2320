package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

public class TimeDiff extends AbstractFunction {
    @Override
    public String getName() {
        return "TIME_DIFF";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2, AviatorObject aviatorObject3) {
        LocalDateTime time1 = AviatorTimestamp.transformAviatorValue(aviatorObject1,env);
        LocalDateTime time2 = AviatorTimestamp.transformAviatorValue(aviatorObject2,env);
        val unit = FunctionUtils.getStringValue(aviatorObject3, env);
        val range = ChronoUnit.valueOf(unit).between(time1, time2);

        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(time1).append(",").append(time2).append(",").append(unit).append(")=").append(range).toString());
        }

        return new AviatorDecimal(range);
    }
}

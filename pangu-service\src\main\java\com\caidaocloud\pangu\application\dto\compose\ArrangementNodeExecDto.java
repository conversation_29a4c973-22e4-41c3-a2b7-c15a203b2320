package com.caidaocloud.pangu.application.dto.compose;

import java.util.Date;
import java.util.Map;

import lombok.Data;

/**
 * 方案节点执行DTO
 * 
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
public class ArrangementNodeExecDto {
    /**
     * ID
     */
    private String id;
    
    /**
     * 执行序列ID
     */
    private String execSeqId;
    
    /**
     * 方案ID
     */
    private String arrangementId;
    
    /**
     * 方案版本ID
     */
    private String arrangementVid;

    /**
     * 节点名称
     */
    private String arrangementNodeName;

    
    /**
     * 执行状态
     */
    private String status;



}
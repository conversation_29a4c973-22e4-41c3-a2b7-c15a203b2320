package multicast;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;

public class MulticastReceiver {
    public static void main(String[] args) throws Exception {
        String multicastAddress = "*********"; // 组播地址
        int port = 5000; // 端口号
        InetAddress group = InetAddress.getByName(multicastAddress);

        try (MulticastSocket socket = new MulticastSocket(port)) {
            socket.joinGroup(group); // 加入组播组

            byte[] buffer = new byte[256];
            while (true) {
                DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                socket.receive(packet); // 接收组播消息

                String received = new String(packet.getData(), 0, packet.getLength());
                System.out.println("Received message: " + received);
            }
        }
    }
}

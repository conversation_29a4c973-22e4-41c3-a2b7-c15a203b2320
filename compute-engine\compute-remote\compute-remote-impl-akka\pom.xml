<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>compute-remote</artifactId>
		<groupId>com.caidaocloud</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>compute-remote-impl-akka</artifactId>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<akka.version>2.8.7</akka.version>
		<scala.binary.version>2.13</scala.binary.version>
	</properties>

	<repositories>
		<repository>
			<id>akka-repository</id>
			<name>Akka library repository</name>
			<url>https://repo.akka.io/maven</url>
		</repository>
	</repositories>
	<dependencyManagement>
		<dependencies>

			<dependency>
				<groupId>com.typesafe.akka</groupId>
				<artifactId>akka-bom_${scala.binary.version}</artifactId>
				<version>${akka.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>compute-remote-framework</artifactId>
			<version>${parent.version}</version>
		</dependency>
		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-actor-typed_${scala.binary.version}</artifactId>
		</dependency>
		<dependency>
			<groupId>com.typesafe.akka</groupId>
			<artifactId>akka-cluster-typed_${scala.binary.version}</artifactId>
		</dependency>
	</dependencies>
</project>
package com.caidaocloud.pangu.domain.entity;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.application.dto.BonusLedgerDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.domain.enums.ApproveStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import com.caidaocloud.pangu.domain.enums.ExecutionType;
import com.caidaocloud.pangu.domain.enums.MonthPeriod;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.pangu.domain.repository.IBonusReportRepository;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;

import org.springframework.beans.BeanUtils;

/**
 * 奖金账套
 * 
 * <AUTHOR> Zhou
 * @date 2023/7/24
 */
@Data
@Slf4j
@NoArgsConstructor
public class BonusLedger extends DataSimple {

	/**
	 * 账套名称
	 */
	private String name;

	@DisplayAsObject
	private Map<String, String> i18nName;

	/**
	 * 奖金方案id
	 */
	private String schemaId;

	/**
	 * 计算年月
	 */
	private Long month;

	/**
	 * 开始日期
	 */
	private Long startDate;

	/**
	 * 结束日期
	 */
	private Long endDate;

	private Integer succeed;

	private Integer total;

	private EnumSimple status;

	private String receiptId;

	private EnumSimple approve_status;

	private EnumSimple executionType;

	/**
	 * 开始日期
	 */
	private Long selectionStartDate;

	/**
	 * 结束日期
	 */
	private Long selectionEndDate;

	// 特殊条件
	private Boolean special;

	private MonthPeriod hireDateMonth;
	private Integer hireDateDay;

	private MonthPeriod terminationDateMonth;
	private Integer terminationDateDay;

	@DisplayAsArray
	private List<String> waitingTask;

	// /**
	// * 适用人员
	// */
	// private List<BonusLedgerEmp> applicableEmployees;

	public static final String BONUS_LEDGER_IDENTIFIER = "entity.bonus.BonusLedger";

	public BonusLedger(Map<String, String> i18nName, String schemaId, Long month, Long startDate, Long endDate,
			EnumSimple executionType, Boolean special, PeriodDate hireDate, PeriodDate terminationDate, Long selectionStartDate, Long selectionEndDate) {
		this.i18nName = i18nName;
		this.name = i18nName.get("default").trim();
		this.schemaId = schemaId;
		this.month = month;
		this.startDate = startDate;
		this.endDate = endDate;
		this.executionType = executionType;
		succeed = 0;
		total = 0;

		EnumSimple enumSimple = new EnumSimple();
		enumSimple.setValue(BonusLedgerStatus.OPEN.name());
		status = enumSimple;

		this.special = special;
		this.hireDateMonth = hireDate.getMonth();
		this.hireDateDay = hireDate.getDay();
		this.terminationDateMonth = terminationDate.getMonth();
		this.terminationDateDay = terminationDate.getDay();

		setCreateTime(System.currentTimeMillis());
		setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
		setIdentifier(BONUS_LEDGER_IDENTIFIER);

		this.setSelectionStartDate(selectionStartDate);
		this.setSelectionEndDate(selectionEndDate);
	}

	//
	public static PageResult<BonusLedger> page(String name, int pageNo, int pageSize) {
		return SpringUtil.getBean(IBonusLedgerRepository.class).page(name, pageNo, pageSize);
	}

	public static BonusApproveRecord loadApproveReceiptByBusinessId(String businessId) {
		return SpringUtil.getBean(IBonusLedgerRepository.class).loadApproveReceiptByBusinessId(businessId);
	}

	public static List<BonusLedger> findByIds(List<String> ids) {
		return SpringUtil.getBean(IBonusLedgerRepository.class).findByIds(ids);
	}

	//
	// public static List<BonusLedger> loadOpenAccount() {
	// return SpringUtil.getBean(IBonusLedgerRepository.class).loadOpen();
	// }
	//
	// public static List<BonusLedger> loadBySchemaId(String bid) {
	// return SpringUtil.getBean(IBonusLedgerRepository.class).loadBySchemaId(bid);
	// }
	//
	public String create(BonusSchema schema, List<BonusStructCompose> compose) {
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setUpdateTime(System.currentTimeMillis());
		if (DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER)
				.count(DataFilter.eq("name", getName()).andNe("deleted", Boolean.TRUE.toString()),
						System.currentTimeMillis()) > 0) {
			throw new ServerException("Bonus ledger name '" + getName() + "' already exists");
		}
		initApproveStatus(schema);

		String bid = SpringUtil.getBean(IBonusLedgerRepository.class).insert(this);
		createLedgerStep(compose);
		return bid;
	}

	private void initApproveStatus(BonusSchema schema) {
		setApprove_status(new EnumSimple());
		if (schema.getApproveConfig() != null && schema.getApproveConfig().getEnabled()) {
			getApprove_status().setValue(ApproveStatus.UNSUBMITTED.name());
		} else {
			getApprove_status().setValue(ApproveStatus.NOT_NEED_APPROVAL.name());
		}
	}

	private void createLedgerStep(List<BonusStructCompose> compose) {
		List<BonusLedgerStep> list = Sequences.sequence(compose)
				.zipWithIndex().map(pair -> {
					Number index = pair.first();
					BonusStructCompose c = pair.second();
					BonusLedgerStep step = new BonusLedgerStep(getBid(), c.getComposeId());
					step.setBid(SnowUtil.nextId());
					step.setSort(index.intValue());
					return step;
				}).toList();
		BonusLedgerStep finalStep = new BonusLedgerStep(getBid(), BonusConstant.BONUS_LEDGER_FINAL_STEP_ID);
		finalStep.setBid(SnowUtil.nextId());
		finalStep.setSort(list.size());
		list.add(finalStep);
		for (int i = 1; i < list.size(); i++) {
			BonusLedgerStep step = list.get(i - 1);
			BonusLedgerStep nextStep = list.get(i);
			step.setNextStepId(nextStep.getBid());
		}
		for (BonusLedgerStep step : list) {
			SpringUtil.getBean(IBonusLedgerRepository.class).saveStep(step);
		}
	}

	public void update() {
		setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		setUpdateTime(System.currentTimeMillis());

		SpringUtil.getBean(IBonusLedgerRepository.class).update(this);
	}

	public void delete() {
		clearReport();
		SpringUtil.getBean(IBonusLedgerRepository.class).delete(this.getBid());
	}

	public void close() {
		if (approve_status.getValue() != null && !(ApproveStatus.NOT_NEED_APPROVAL.name()
				.equals(approve_status.getValue())
				|| ApproveStatus.APPROVED.name()
						.equals(approve_status.getValue()))) {
			throw new ServerException("仅审批通过后可以关闭账套");
		}
		status.setValue(BonusLedgerStatus.CLOSE.name());
		update();
	}

	public static BonusLedger load(String bid) {
		return SpringUtil.getBean(IBonusLedgerRepository.class).loadByBid(bid);
	}
	// public void addEmp(EmpInfoDto emp) {
	// BonusLedgerEmp ledgerEmp = new BonusLedgerEmp(getBid(), emp.getEmpId());
	// BeanUtils.copyProperties(emp, ledgerEmp);
	// SpringUtil.getBean(IBonusLedgerRepository.class).addEmp(ledgerEmp);
	// }

	public void removeEmp(String empId) {
		SpringUtil.getBean(IBonusLedgerRepository.class).removeEmp(new BonusLedgerEmp(getBid(), empId));
	}

	public void syncEmp(List<EmpSimple> matchedEmp) {
		if (selectionStartDate != null && selectionEndDate != null) {
			SelectionDateRange range = calculateSelectionRange();
			matchedEmp = range.filter(matchedEmp);
		}
		SpringUtil.getBean(IBonusLedgerRepository.class).syncEmp(getBid(), Sequences.sequence(matchedEmp)
				.map(emp -> new BonusLedgerEmp(getBid(), emp.getEmpId())).toList());

	}

	public void addEmp(List<EmpSimple> matchedEmp) {
		// // 删除账套下所有员工并重新添加
		// List<BonusLedgerEmp> existEmp =
		// SpringUtil.getBean(IBonusLedgerRepository.class)
		// .loadAllEmp(new BonusAccountEmpPageDto(), getBid(),null, null, 1,
		// -1).getItems();
		// Map<BonusLedgerEmp, BonusLedgerEmp> existMap = existEmp.stream()
		// .collect(Collectors.toMap(obj -> obj, obj -> obj));
		//
		// for (EmpSimple empSimple : matchedEmp) {
		// BonusLedgerEmp accountEmp = new BonusLedgerEmp(getBid(),
		// empSimple.getEmpId());
		// BonusLedgerEmp tmp = existMap.computeIfAbsent(accountEmp, emp -> emp);
		// }
		SpringUtil.getBean(IBonusLedgerRepository.class).addEmp(getBid(), Sequences.sequence(matchedEmp)
				.map(emp -> new BonusLedgerEmp(getBid(), emp.getEmpId())).toList());

	}

	public PageResult<BonusLedgerEmp> loadEmp(BonusLedgerEmpPageDto dto) {
		return SpringUtil.getBean(IBonusLedgerRepository.class)
				.loadAllEmp(dto, getBid(), dto.getPageNo(), dto.getPageSize());
	}

	public List<BonusLedgerEmp> loadSimpleEmp(List<String> empIds) {
		return SpringUtil.getBean(IBonusLedgerRepository.class)
				.loadSimpleEmp(getBid(), empIds);
	}

	/**
	 * 查询所有员工
	 * 
	 * @return
	 */
	public List<BonusLedgerEmp> loadSimpleEmp() {
		return SpringUtil.getBean(IBonusLedgerRepository.class)
				.loadSimpleEmp(getBid(), Lists.list());
	}

	public List<BonusLedgerStep> loadStep() {
		List<BonusLedgerStep> steps = SpringUtil.getBean(IBonusLedgerRepository.class).loadStep(getBid());
		return Sequences.sequence(steps).map(s -> {
			if (s.getComposeId().equals(BonusConstant.BONUS_LEDGER_FINAL_STEP_ID)) {
				return ObjectConverter.convert(s, BonusLedgerFinalStep.class);
			}
			return s;
		}).sortBy(Comparator.comparingInt(BonusLedgerStep::getSort)).toList();
	}

	//
	// public void beforeCalc() {
	// SpringUtil.getBean(IBonusLedgerRepository.class).deleteRemovedEmp(getBid());
	// }
	//
	// public void saveCalcResult(BonusLedgerEmp emp, List<BonusLedgerEmp.BonusItem>
	// itemList) {
	// emp.getCalcStatus().setValue(BonusAccountEmpCalcStatus.DONE.name());
	// emp.setItems(itemList);
	// emp.setUpdateTime(System.currentTimeMillis());
	// SpringUtil.getBean(IBonusLedgerRepository.class).updateEmp(emp);
	// }
	//
	// public void saveFailedResult(BonusLedgerEmp emp) {
	// emp.getCalcStatus().setValue(BonusAccountEmpCalcStatus.FAILED.name());
	// emp.setItems(null);
	// SpringUtil.getBean(IBonusLedgerRepository.class).updateEmp(emp);
	// }
	//

	//
	// public boolean checkPending() {
	// return approveStatus != null &&
	// approveStatus.getValue().equals(ApproveStatus.PENDING.name());
	// }

	@Deprecated
	public void executeStep(String stepId) {
		// 校验执行类型
		if (ExecutionType.ALL.getCode().equals(executionType.getValue())) {
			throw new ServerException("Cannot execute single step when execution type is ALL");
		}

		List<BonusLedgerStep> allSteps = loadStep();
		BonusLedgerStep currentStep = allSteps.stream()
				.filter(s -> stepId.equals(s.getBid()))
				.findFirst()
				.orElseThrow(() -> new ServerException("Step not found"));
		exec(allSteps, currentStep);
	}

	public void executeAll() {
		// 校验执行类型
		if (ExecutionType.SINGLE.getCode().equals(executionType.getValue())) {
			throw new ServerException("Cannot execute all steps when execution type is SINGLE");
		}

		List<BonusLedgerStep> allSteps = loadStep();
		BonusLedgerStep currentStep = allSteps.get(0);
		exec(allSteps, currentStep);
	}

	private void exec(List<BonusLedgerStep> allSteps, BonusLedgerStep currentStep) {
		// 检查当前步骤是否正在执行中
		if (currentStep.getStatus() != null &&
				String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue())
						.equals(currentStep.getStatus().getValue())) {
			throw new ServerException("Current step is already executing");
		}

		// 检查前一步骤状态
		if (currentStep.getSort() > 0) {
			BonusLedgerStep previousStep = allSteps.stream()
					.filter(s -> s.getSort() == currentStep.getSort() - 1)
					.findFirst()
					.orElse(null);

			if (previousStep != null && (previousStep.getStatus() == null ||
					!String.valueOf(BonusLedgerStepStatus.EXECUTED.getValue())
							.equals(previousStep.getStatus().getValue()))) {
				throw new ServerException("Previous step must be completed before executing current step");
			}
		}
		// for (BonusLedgerEmp emp : loadSimpleEmp()) {
		// emp.getStatus().setValue(BonusLedgerEmpCalcStatus.INIT.code);
		// SpringUtil.getBean(IBonusLedgerRepository.class).updateEmp(emp);
		// }
		clearReport();

		exec(currentStep);

		// 重置所有后续步骤状态为未执行
		allSteps.stream()
				.filter(s -> s.getSort() > currentStep.getSort())
				.forEach(nextStep -> {
					EnumSimple notExecutedStatus = new EnumSimple();
					notExecutedStatus.setValue(String.valueOf(BonusLedgerStepStatus.NOT_EXECUTED.getValue()));
					nextStep.setStatus(notExecutedStatus);
					nextStep.setExecTime(null);
					SpringUtil.getBean(IBonusLedgerRepository.class).updateStep(nextStep);
				});

	}

	public void exec(BonusLedgerStep currentStep) {
		Map<String, String> context = Maps.map("BONUS_ACCOUNT_NAME", getName(),
				"BONUS_CALC_YEAR_MONTH", DateUtil.format(getMonth(), "yyyyMM"),
				"BONUS_CALC_START", String.valueOf(getStartDate()),
				"BONUS_CALC_END", String.valueOf(getEndDate()),
				"BONUS_ACCOUNT_ID", getBid());
		EnumSimple status = new EnumSimple();
		status.setValue(String.valueOf(BonusLedgerStepStatus.IN_PROGRESS.getValue()));
		currentStep.setStatus(status);
		currentStep.setExecTime(System.currentTimeMillis());
		currentStep.setUpdateTime(System.currentTimeMillis());
		currentStep.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		SpringUtil.getBean(IBonusLedgerRepository.class).updateStep(currentStep);
		currentStep.exec(context);
	}

	public boolean isClosed() {
		return BonusLedgerStatus.CLOSE.name().equals(status.getValue());
	}

	public void removeEmpBatch(List<String> empId) {
		SpringUtil.getBean(IBonusLedgerRepository.class).removeEmpBatch(getBid(), empId);
		SpringUtil.getBean(IBonusReportRepository.class).removeEmpBatch(getBid(), empId);
	}

	/**
	 * 修改奖金方案
	 * 
	 * @param schema
	 * @param compose
	 */
	public void update(BonusLedgerDto bonusLedgerDto, BonusSchema schema, BonusStruct struct,
			List<BonusStructCompose> compose) {
		if (!schema.getBid().equals(getSchemaId()) ||
				!bonusLedgerDto.getMonth().equals(getMonth()) ||
				!bonusLedgerDto.getStartDate().equals(getStartDate()) ||
				!bonusLedgerDto.getEndDate().equals(getEndDate())) {
			log.info("Updating BonusLedger from {} to {}, revoking data", this, bonusLedgerDto);

			SpringUtil.getBean(IBonusLedgerRepository.class).deleteStepByLedgerId(getBid());
			BonusApproveRecord approveRecord = SpringUtil.getBean(IBonusLedgerRepository.class)
					.loadApproveReceiptByLedgerId(getBid());
			if (approveRecord != null)
				approveRecord.revoke();
			initApproveStatus(schema);
			createLedgerStep(compose);
			clearReport();
		}

		// Update properties from BonusLedgerDto
		BeanUtils.copyProperties(bonusLedgerDto, this, "schemaId");
		this.setName(bonusLedgerDto.getI18nName().get("default"));
		this.setExecutionType(struct.getExecutionType());

		this.special = schema.getSpecial();
		this.hireDateMonth = schema.getHireDate().getMonth();
		this.hireDateDay = schema.getHireDate().getDay();
		this.terminationDateMonth = schema.getTerminationDate().getMonth();
		this.terminationDateDay = schema.getTerminationDate().getDay();
		this.selectionStartDate = bonusLedgerDto.getSelectionStartDate();
		this.selectionEndDate = bonusLedgerDto.getSelectionEndDate();

		// Set schemaId after the checks
		setSchemaId(schema.getBid());
		update();
	}

	private void clearReport() {
		SpringUtil.getBean(IBonusReportRepository.class).deleteByLedgerId(getBid());
	}

	public int countEmp() {
		return SpringUtil.getBean(IBonusLedgerRepository.class).countEmpByLedgerId(getBid());
	}

	public int countReport() {
		return SpringUtil.getBean(IBonusReportRepository.class).countByLedgerId(getBid());
	}

	/**
	 * 计算选人周期范围
	 * 
	 * @return 包含开始和结束日期的选人周期对象
	 */
	public SelectionDateRange calculateSelectionRange() {
		SelectionDateRange range = new SelectionDateRange();

		if (Boolean.TRUE.equals(special)) {
			LocalDateTime time
					= Instant.ofEpochMilli(month)
					.atZone(ZoneId.systemDefault())
					.toLocalDateTime().withHour(0).withMinute(0).withSecond(0);
			LocalDateTime hireDate,terminationDate;
			// 计算入职日期
			if (hireDateMonth == MonthPeriod.CURRENT_MONTH) {
				hireDate = adjustDayOfMonth(time, hireDateDay);
			} else {
				hireDate = adjustDayOfMonth(time.minusMonths(1), hireDateDay);
			}

			// 计算离职日期
			if (terminationDateMonth == MonthPeriod.CURRENT_MONTH) {
				terminationDate = adjustDayOfMonth(time, terminationDateDay);
			} else {
				terminationDate = adjustDayOfMonth(time.minusMonths(1), terminationDateDay);
			}
			// 使用已设置的选人周期范围
			range.setTerminationDate(terminationDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
			range.setHireDate(hireDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
		} else {
			// 使用已设置的选人周期范围
			range.setTerminationDate(selectionStartDate);
			range.setHireDate(selectionEndDate);
		}

		return range;
	}


	// 调整日期以兼容大小月和二月
	private static LocalDateTime adjustDayOfMonth(LocalDateTime dateTime, int dayOfMonth) {
		// 获取目标月份的最大天数
		int maxDayOfMonth = dateTime.toLocalDate().lengthOfMonth();
		// 如果指定的天数超过最大天数，则调整为该月的最后一天
		int adjustedDay = Math.min(dayOfMonth, maxDayOfMonth);
		return dateTime.withDayOfMonth(adjustedDay);
	}

	public void executeStepBatch(List<String> batchStepId) {
		// 校验执行类型
		if (ExecutionType.ALL.getCode().equals(executionType.getValue())) {
			throw new ServerException("Cannot execute single step when execution type is ALL");
		}

		List<BonusLedgerStep> allSteps = loadStep();
		List<BonusLedgerStep> batchStep = Sequences.sequence(allSteps)
				.filter(step -> batchStepId.contains(step.getBid())).toList();
		if (batchStep.size() != batchStepId.size() || batchStep.size() == 0) {
			throw new ServerException("Step not found");
		}
		for (int i = 1; i < batchStep.size(); i++) {
			if (!batchStep.get(i - 1).getNextStepId().equals(batchStep.get(i).getBid())) {
				throw new ServerException("batch error");
			}
		}
		setWaitingTask(Sequences.sequence(batchStep).map(AbstractData::getBid).toList());
		update();
		exec(allSteps, batchStep.get(0));
	}

	/**
	 * 选人周期日期范围包装类
	 */
	@Data
	public static class SelectionDateRange {
		/**
		 * 开始日期（入职日期）
		 */
		private Long hireDate;

		/**
		 * 结束日期（离职日期）
		 */
		private Long terminationDate;

	public 	List<EmpSimple> filter(List<EmpSimple> matchedEmp) {
		List<List<EmpSimple>> list = ListUtils.partition(matchedEmp, 1000);
		List<EmpSimple> result = new ArrayList<>();
		for (List<EmpSimple> empSimples : list) {
			List<String> empIds = Sequences.sequence(empSimples).map(EmpSimple::getEmpId).toList();
			PageResult<Map<String, String>> query = DataQuery.identifier("entity.hr.EmpWorkInfo")
					.limit(-1,1)
					.filterProperties(DataFilter.in("empId", empIds).andNe("deleted", Boolean.TRUE.toString())
							.andLe("hireDate", String.valueOf(hireDate)).and(DataFilter.eq("empStatus", "0")
									.orGe("leaveDate", String.valueOf(terminationDate))), Lists.list("emp_id"), System.currentTimeMillis());
			Set<String> querySet = query.getItems().stream().map(map -> map.get("empId")).collect(Collectors.toSet());
			result.addAll(Sequences.sequence(empSimples).filter(emp -> querySet.contains(emp.getEmpId())));
		}
		return result;
		}
	}
}

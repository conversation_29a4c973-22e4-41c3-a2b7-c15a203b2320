package com.caidaocloud.pangu.domain.repository;

import com.caidaocloud.pangu.domain.entity.BonusStructReportItem;
import com.caidaocloud.pangu.infrastructure.repository.common.BaseRepository;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/1/23
 */
public interface IBonusStructReportItemRepository  extends BaseRepository<BonusStructReportItem> {
    void saveList(String structId, List<BonusStructReportItem> items);

    List<BonusStructReportItem> selectList(String structId);
}

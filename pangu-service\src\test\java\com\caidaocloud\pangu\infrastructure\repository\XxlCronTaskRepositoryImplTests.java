package com.caidaocloud.pangu.infrastructure.repository;

import com.caidaocloud.pangu.PanguApplication;
import com.caidaocloud.pangu.application.dto.cron.CronTaskDto;
import com.caidaocloud.pangu.domain.repository.ICronTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguApplication.class)
@Slf4j
public class XxlCronTaskRepositoryImplTests {
	@Autowired
	private ICronTaskRepository cronTaskRepository;

	@Test
	public void getXxlCookie() {
	}

	@Test
	public void getExecutorId() {
	}

	@Test
	public void registerTask() {
		CronTaskDto dto = new CronTaskDto("1");
		dto.setTenantId("11");
		String taskId = cronTaskRepository.registerTask(dto, "123", "0 0 0 ? * * *");
		System.out.println(taskId);
	}

	@Test
	public void cancelTask() {
		cronTaskRepository.cancelTask("33");
	}
}
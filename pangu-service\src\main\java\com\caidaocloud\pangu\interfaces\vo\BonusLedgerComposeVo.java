package com.caidaocloud.pangu.interfaces.vo;

import com.caidaocloud.pangu.domain.enums.BonusLedgerStepStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/2/10
 */
@Data
public class BonusLedgerComposeVo {
	private String bid;

	@ApiModelProperty("编排ID")
	private String composeId;

	@ApiModelProperty("编排名称")
	private String composeName;

	@ApiModelProperty("描述")
	private String remark;

	@ApiModelProperty("执行状态")
	private BonusLedgerStepStatus status;

	@ApiModelProperty("执行时间")
	private Long execTime;

	@ApiModelProperty("执行id")
	private String execId;
}

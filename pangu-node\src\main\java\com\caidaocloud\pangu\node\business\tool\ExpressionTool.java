package com.caidaocloud.pangu.node.business.tool;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.node.business.enums.EnvironmentContext;
import com.caidaocloud.pangu.node.business.model.TaskDef;
import com.caidaocloud.pangu.node.business.service.functions.And;
import com.caidaocloud.pangu.node.business.service.functions.Divide;
import com.caidaocloud.pangu.node.business.service.functions.FunctionLogTool;
import com.caidaocloud.pangu.node.business.service.functions.Or;
import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.*;
import com.googlecode.aviator.lexer.token.OperatorType;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.val;
import org.reflections.Reflections;

import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ExpressionTool {

    private static List<String> registerFunctions = Lists.list();

    private static ThreadLocal<Map<String, AviatorObject>> params = new ThreadLocal<>();

    private static ThreadLocal<List<CalcReport.SubTaskValue>> groupResult = new ThreadLocal<>();

    private static List<String> envKeys = Arrays.stream(EnvironmentContext.values()).map(it->it.getProperty()).collect(Collectors.toList());



    private static AviatorEvaluatorInstance defaultIns = AviatorEvaluator.newInstance();

    private static AviatorEvaluatorInstance interpreterIns = AviatorEvaluator.newInstance(EvalMode.INTERPRETER);

    static {
        defaultIns.setOption(Options.ALWAYS_PARSE_FLOATING_POINT_NUMBER_INTO_DECIMAL, true);
        interpreterIns.setOption(Options.ALWAYS_PARSE_FLOATING_POINT_NUMBER_INTO_DECIMAL, true);
        val functionClasses = new Reflections("com.caidaocloud.pangu.node.business.service.functions")
                .getSubTypesOf(AviatorFunction.class).stream().filter(it-> !Modifier.isAbstract(it.getModifiers())).collect(Collectors.toList());
        for (Class<? extends AviatorFunction> it : functionClasses) {
            AviatorFunction function = null;
            try {
                function = it.newInstance();
            } catch (Exception e) {
                throw new ServerException();
            }
            interpreterIns.addFunction(function);
            defaultIns.addFunction(function);

            interpreterIns.addOpFunction(OperatorType.AND, new And());
            defaultIns.addOpFunction(OperatorType.AND, new And());

            interpreterIns.addOpFunction(OperatorType.OR, new Or());
            defaultIns.addOpFunction(OperatorType.OR, new Or());

            interpreterIns.addOpFunction(OperatorType.DIV, new Divide());
            defaultIns.addOpFunction(OperatorType.DIV, new Divide());

            registerFunctions.add(function.getName());
        }
    }



    public static void main(String[] args) {




//        validateExp("MODEL_DATA_TOP(\"entity.wfm.PerformanceEmp\", \"monthScore\", FILTER_EQ(\"performancePeriodId\", MODEL_DATA_TOP(\"entity.wfm.PerformancePeriod\", \"bid\", FILTER_EQ(\"period\", $.env.BONUS_CALC_YEAR_MONTH))) && FILTER_EQ(\"empId\", GET_EMP_ID($.source.workno)))");
        FunctionLogTool.init("1", "2", "3", null);
        val v = new AviatorTimestamp(System.currentTimeMillis());


        System.out.println(exec("a", "STR_BEFORE('TAKE(', '(')", Maps.map()));


//        System.out.println(exec("b", "IF_ELSE($.task.a==0, 0, $.task.b/$.task.a,0)",
//                Maps.map("$.task.a", 0,
//                        "$.task.b", 3
////                        , "$.source.InputNumberStringZBfpR_", "3"
//                        )));
        FunctionLogTool.flush();
//        System.out.println(exec("c", "BEFORE(NOW(), NOW())", Maps.map("$.env.bonus_time", 3)));
    }

    public static void publishExp(String cacheKey, String exp){
        defaultIns.compile(cacheKey, exp, true);
    }

    public static Object exec(String cacheKey, String exp, Map params){
        try {
            ExpressionTool.params.set(Maps.map());
            params.put("TRUE", true);
            params.put("FALSE", false);
            params.put("SECONDS", "SECONDS");
            params.put("MINUTES", "MINUTES");
            params.put("HOURS", "HOURS");
            params.put("HALF_DAYS", "HALF_DAYS");
            params.put("DAYS", "DAYS");
            params.put("MONTHS", "MONTHS");
            params.put("WEEKS", "WEEKS");
            params.put("YEARS", "YEARS");
            params.put("UP", "UP");
            params.put("DOWN", "DOWN");
            params.put("CEILING", "CEILING");
            params.put("FLOOR", "FLOOR");
            params.put("HALF_UP", "HALF_UP");
            params.put("HALF_DOWN", "HALF_DOWN");
            return defaultIns.execute(cacheKey, exp, params, true);
        } finally {
            ExpressionTool.params.remove();
        }
    }

    public static void addParam(String name, AviatorObject value){
        params.get().put(name, value);
    }

    public static AviatorObject getParam(String name){
        return params.get().get(name);
    }

    public static void validateExp(String exp){
        Expression compiled;
        try {
            compiled = interpreterIns.compile(exp);
        } catch (Exception e) {
            throw new ServerException(e.getMessage());
        }
        List<String> vars = compiled.getVariableFullNames();
        List<String> functions = compiled.getFunctionNames();
        functions.forEach(it -> {
            if(!registerFunctions.contains(it)){
                throw new ServerException();
            }
        });
        vars.forEach(it -> {
            if(it.startsWith("$.env.")){
                if(!envKeys.contains(it.replace("$.env.", ""))){
                    throw new ServerException();
                }
            }
            //todo validate
        });
    }


    public static void initGroup() {
        groupResult.set(Lists.list());
    }

    public static void removeGroup() {
        groupResult.remove();
    }

    public static void addGroupResult(CalcReport.SubTaskValue groupResult) {
        ExpressionTool.groupResult.get().add(groupResult);
    }

    public static List<CalcReport.SubTaskValue> getGroupResult() {
        return ExpressionTool.groupResult.get();
    }
}

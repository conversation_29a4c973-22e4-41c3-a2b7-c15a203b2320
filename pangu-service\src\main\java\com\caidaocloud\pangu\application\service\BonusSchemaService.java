package com.caidaocloud.pangu.application.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefMetadataDto;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.pangu.application.dto.ApproveConfigDto;
import com.caidaocloud.pangu.application.dto.BonusSchemaDto;
import com.caidaocloud.pangu.domain.BonusSchemaFactory;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusSchema;
import com.caidaocloud.pangu.domain.entity.BonusStruct;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.enums.ApproveStatus;
import com.caidaocloud.pangu.domain.enums.BonusLedgerStatus;
import com.caidaocloud.pangu.domain.repository.IFormRepository;
import com.caidaocloud.pangu.domain.repository.IBonusLedgerRepository;
import com.caidaocloud.pangu.infrastructure.util.LangUtil;
import com.caidaocloud.pangu.interfaces.vo.BonusSchemaVo;
import com.caidaocloud.pangu.interfaces.vo.BonusStructItemVo;
import com.caidaocloud.pangu.interfaces.vo.MatchConditionVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.val;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.caidaocloud.pangu.application.factory.BonusWorkflowFactory.createFunCode;

@Service
public class BonusSchemaService {

    @Resource
    private IConditionFeign conditionFeign;
    @Autowired
    private IFormRepository formRepository;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private IBonusLedgerRepository bonusLedgerRepository;
    @Resource
    private MetadataOperatorService metadataOperatorService;

    private String CONDITION_CODE = "BONUS_SCHEMA";
    private static final String CALLBACK_ADDRESS_TEMPLATE = "/api/pangu/v1/bonus/approve/callback/%s";
    public static final String[][] CALLBACK_APIS = {{"approved","同意"}, {"refused","拒绝"}};

    public static final List<WfMetaFunFormFieldDto> WF_META_FUN_FORM_FIELDS =
            Lists.list(
                    new WfMetaFunFormFieldDto("name", "账套名称", WfFieldDataTypeEnum.Text),
                    new WfMetaFunFormFieldDto("yearMonth", "计算年月", WfFieldDataTypeEnum.Text),
                    new WfMetaFunFormFieldDto("startDate", "开始日期", WfFieldDataTypeEnum.Timestamp),
                    new WfMetaFunFormFieldDto("endDate", "结束日期", WfFieldDataTypeEnum.Timestamp),
                    new WfMetaFunFormFieldDto("count", "计薪人数", WfFieldDataTypeEnum.Number),
                    new WfMetaFunFormFieldDto("summary", "计件工资项目金额汇总", WfFieldDataTypeEnum.Text)
            );

    @PaasTransactional
    public String create(BonusSchemaDto schema){
        String bid = BonusSchemaFactory.create(schema).create();
        schema.setBid(bid);
        if (schema.getApproveConfig() != null && schema.getApproveConfig().getFormId() != null) {
            registerWorkflow(schema);
        }
        return bid;
    }

    private void registerWorkflow(BonusSchemaDto schema) {
        FormDefDto formDefDto = formRepository.getFormDefById(schema.getApproveConfig().getFormId());
        WfMetaFunDto metafun = buildMetafun(schema, formDefDto);
        wfRegisterFeign.registerFunction(metafun);
        registerCallback(metafun);
    }

    private void registerCallback(WfMetaFunDto metafun) {
        for (String[] api : CALLBACK_APIS) {
            String code = metafun.getCode() + "-" + api[0];
            String address = String.format(CALLBACK_ADDRESS_TEMPLATE, api[0]);

            WfMetaCallbackDto dto = new WfMetaCallbackDto(String.format("%s-%s", metafun.getName(), api[1]),
                    code, Lists.list(metafun.getCode()),
                    SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                    address,
                    "caidaocloud-pangu-engine",
                    "",
                    WfCallbackTypeEnum.RELATIVE_PATH,
                    WfCallbackTimeTypeEnum.NOW);
            wfRegisterFeign.registerCallback(dto);
        }
    }

    private WfMetaFunDto buildMetafun(BonusSchemaDto schema, FormDefDto formDefDto) {
        String funCode = createFunCode(schema.getBid());
        List<WfMetaFunFormFieldDto> formFields = new ArrayList<>();
        for (FormDefMetadataDto property : formDefDto.getProperties()) {
            formFields.add(new WfMetaFunFormFieldDto(property.getProperty(), property.getName()));
        }
        return new WfMetaFunDto(schema.getName(), funCode,
                WfFunctionPageJumpType.RELATIVE_PATH, SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                "caidaocloud-pangu-engine",
                "", "", "", formFields);
    }



    @PaasTransactional
    public String update(BonusSchemaDto schema){
        BonusSchema exist = BonusSchema.load(schema.getBid());
        BonusSchema bonusSchema = BonusSchemaFactory.create(schema);
        BeanUtils.copyProperties(bonusSchema, exist,"id","bid","identifier","tenantId","createTime","createBy","updateTime","updateBy","dataStartTime","dataEndTime","deleted");
        exist.update();
        if (schema.getApproveConfig() != null ) {
            if (schema.getApproveConfig().getEnabled() != null && schema.getApproveConfig().getEnabled()) {
                registerWorkflow(schema);
            }
            else {
                resetApproveStatus(schema.getBid());
            }
        }
        return exist.getBid();
        // if (ObjectUtil.nullSafeEquals(schema.getApproveConfig(), exist.getApproveConfigDo())) {
        //      schema.toEntity(exist).update();
        //     return exist.getBid();
        // }
        // else {
        //     String name = schema.getName() + "_" + DateUtil.format(System.currentTimeMillis(), "yyyyMMddHHmmss");
        //     if (name.length()>50) {
        //         name = name.substring(0, 50);
        //     }
        //     schema.setName(name);
        //     String bid = schema.toEntity().create();
        //     schema.setBid(bid);
        //     if (schema.getApproveConfig() != null && schema.getApproveConfig().getEnabled()) {
        //         registerWorkflow(schema);
        //     }
        //     return bid;
        // }
    }

    private void resetApproveStatus(String bid) {
        List<BonusLedger> accounts = bonusLedgerRepository.loadBySchemaId(bid);
        List<BonusLedger> accountList = Sequences.sequence(accounts)
                .filter(account -> BonusLedgerStatus.OPEN.name()
                        .equals(account.getStatus().getValue()) && !ApproveStatus.APPROVED.name()
                        .equals(account.getApprove_status().getValue())).toList();
        for (BonusLedger account : accountList) {

            account.getApprove_status().setValue(ApproveStatus.NOT_NEED_APPROVAL.name());
            account.update();
        }

    }

    @PaasTransactional
    public void delete(String bid){
        val referred = DataQuery.identifier(BonusLedger.BONUS_LEDGER_IDENTIFIER)
                .count(DataFilter.eq("schemaId", bid),
                        System.currentTimeMillis()) > 0;
        if(referred){
            throw new ServerException("该方案被使用，无法删除！");
        }
        BonusSchema.load(bid).delete();
    }

    public PageResult<BonusSchemaVo> page(BasePage basePage) {
        val page = BonusSchema.page(basePage);
        List<BonusStruct> structs = BonusStruct.list();
        List<BonusSchemaVo> list = Sequences.sequence(page.getItems()).map(it -> {
            BonusSchemaVo vo = ObjectConverter.convert(it, BonusSchemaVo.class);
            vo.setBonusStructId(it.getStructId());
            return vo;
        }).toList();
        PageResult<BonusSchemaVo> pageResult = new PageResult(list,
                page.getPageNo(), page.getPageSize(), page.getTotal());
        for (BonusSchemaVo item : pageResult.getItems()) {
            Map<String, String> map = Sequences.sequence(structs)
                    .find(bs -> bs.getBid().equals(item.getBonusStructId()))
                    .map(BonusStruct::getI18nName).getOrElse(HashMap::new);
            item.setBonusStructName(LangUtil.getCurrentLangVal(map));
        }
        return pageResult;
    }


    public BonusSchemaVo load(String bid) {
        val schema = BonusSchema.load(bid);
        BonusSchemaVo schemaVo = ObjectConverter.convert(schema, BonusSchemaVo.class);
        schemaVo.setApproveConfig(ObjectConverter.convert(schema.getApproveConfig(), ApproveConfigDto.class));
        schemaVo.setMonth(Integer.valueOf(schema.getMonth().getValue()));
        BonusStruct struct = BonusStruct.load(schema.getStructId());
        schemaVo.setBonusStructName(LangUtil.getCurrentLangVal(struct.getI18nName()));
        schemaVo.setBonusStructId(struct.getBid());
        return schemaVo;
    }

    public List<MatchConditionVo> listMatchCondition() {
        List<ConditionDataVo> conditionDataList = conditionFeign.getConditionDataByCode(CONDITION_CODE, false).getData();
        return conditionDataList.stream().map(data -> {
            MatchConditionVo vo = FastjsonUtil.convertObject(data, MatchConditionVo.class);
            vo.setCode(data.getIdentifier() + "#" + data.getQueryProperty().replaceAll("\\.","\\$"));
            return vo;
        }).collect(Collectors.toList());
    }

    public List<BonusStructItem> loadBonusItem(String schemaId){
        BonusSchema schema = BonusSchema.load(schemaId);
        BasePage basePage = new BasePage();
        basePage.setPageSize(-1);
        return BonusStruct.pageItems(basePage, schema.getStructId(),
                null).getItems();
    }

    public List<WfMetaFunFormFieldDto> buildWfFields(FormDefDto formDefDto) {
        List<WfMetaFunFormFieldDto> formFields = new ArrayList<>();
        formFields.addAll(WF_META_FUN_FORM_FIELDS);
        for (FormDefMetadataDto property : formDefDto.getProperties()) {
            formFields.add(new WfMetaFunFormFieldDto(property.getProperty(), property.getName()));
        }
        return formFields;
    }

   public  List<BonusStructItemVo> listApproveItems(String structId) {
       List<BonusStructItem> structItemList = BonusStruct.load(structId).listItems();
       Map<String, List<BonusStructItem>> map = Sequences.sequence(structItemList)
               .toMap(BonusStructItem::getValueIdentifier);
       Set<BonusStructItem> digitItem = new HashSet<>();
       for (Map.Entry<String, List<BonusStructItem>> entry : map.entrySet()) {
           String key = entry.getKey();
           MetadataVo definition = metadataOperatorService.load(key);
           Set<String> digitSet = Sequences.sequence(definition.fetchAllProperties())
                   .filter(p -> p.getDataType() == PropertyDataType.Number || p.getDataType() == PropertyDataType.Integer)
                   .map(MetadataPropertyVo::getProperty).toSet();
           
           for (BonusStructItem item : entry.getValue()) {
               if (digitSet.contains(item.getValueProperty())) {
                   digitItem.add(item);
               }
           }
       }
       return structItemList.stream()
               .filter(digitItem::contains)
               .map(item -> ObjectConverter.convert(item, BonusStructItemVo.class))
               .collect(Collectors.toList());
    }

    public List<BonusSchemaVo> list() {
        return ObjectConverter.convertList(BonusSchema.list(), BonusSchemaVo.class);
    }

}

package com.caidaocloud.remote.vertx.core;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.compute.remote.framework.actor.ActorInfo;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import com.caidaocloud.compute.remote.framework.actor.HandlerInfo;
import com.caidaocloud.compute.remote.framework.core.DynamicPortInitializer;
import com.caidaocloud.compute.remote.framework.core.InitializerConfig;
import com.caidaocloud.compute.remote.framework.serialize.SerializerUtils;
import com.caidaocloud.security.util.SecurityUserUtil;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.http.HttpServer;
import io.vertx.core.http.HttpServerOptions;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.client.WebClientOptions;
import io.vertx.ext.web.handler.BodyHandler;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/12/30
 */
@Slf4j
public class VertxInitializer extends DynamicPortInitializer implements ApplicationListener<WebServerInitializedEvent> {

	private Vertx vertx;
	private HttpServer httpServer;
	private WebClient httpClient;

	private boolean flag = false;

	private List<ActorInfo> actorInfoList = new ArrayList<>();

	public VertxInitializer(InitializerConfig initializerConfig) {
		super(initializerConfig);
		this.init(initializerConfig);
	}

	@Override
	public void init(InitializerConfig config) {
		this.vertx = initVertx();
		this.httpServer = initHttpServer();
		this.httpClient = initHttpClient();
	}

	private WebClient initHttpClient() {
		WebClientOptions options = new WebClientOptions();
		return WebClient.create(vertx, options);
	}

	private HttpServer initHttpServer() {
		HttpServerOptions options = new HttpServerOptions();
		return vertx.createHttpServer(options);
	}

	private Vertx initVertx() {
		VertxOptions options = new VertxOptions();
		return Vertx.vertx(options);
	}

	@Override
	public void initHandler(ActorInfo actorInfo) {
		actorInfoList.add(actorInfo);
	}

	@Override
	public int getPort() {
		return httpServer.actualPort();
	}


	public HttpServer getHttpServer() {
		return httpServer;
	}

	public WebClient getHttpClient() {
		return httpClient;
	}

	@Override
	public void onApplicationEvent(WebServerInitializedEvent event) {
		if (flag) {
			return;
		}
		Router router = Router.router(vertx);
		router.route().handler(BodyHandler.create());
		for (ActorInfo actorInfo : actorInfoList) {
			for (HandlerInfo handlerInfo : actorInfo.getHandlerInfos()) {
				String requestPath = String.format("/%s/%s", actorInfo.getPath(), handlerInfo.getMethod().getName());
				router.route(HttpMethod.POST, requestPath).handler(ctx -> {
					ActorMessageWrapper messageWrapper = (ActorMessageWrapper) SerializerUtils.deSerialized(ctx.body().buffer().getBytes());
					if (log.isDebugEnabled()) {
						log.info("[Vertx] receive request，path:{},params:{}", requestPath, messageWrapper.getArgs());
					}
					try {
						SecurityUserUtil.setSecurityUserInfo(messageWrapper.getUserInfo());
						Object ret = handlerInfo.getMethod().invoke(actorInfo.getActor(), messageWrapper.getArgs());
						if (ret instanceof String) {
							ctx.end((String) ret);
						} else {
							ctx.json(ret);
						}
					}
					catch (Exception e) {
						log.error("[Vertx] process failed!", e);
						ctx.fail(HttpResponseStatus.INTERNAL_SERVER_ERROR.code(),e);
					}finally {
						SecurityUserUtil.removeSecurityUserInfo();
					}
				});
			}
		}
		httpServer.requestHandler(router);
		httpServer.listen(getInitializerConfig().getPort()).onComplete(server->{
			log.info("[Vertx] server初始化成功,端口:{}", httpServer.actualPort());
		});
		flag = true;
	}

}

package com.caidaocloud.pangu.infrastructure.util;

import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import com.caidaocloud.util.WebUtil;
import org.apache.commons.lang3.StringUtils;

public final class LangUtil {
    public static Locale getLocale() {
        HttpServletRequest request = WebUtil.getRequest();
        Locale locale = null;
        if (request != null) {
            String acceptLanguage = request.getHeader("Accept-Language");
            locale = Optional.ofNullable(acceptLanguage).map(o1 -> {
                if (o1.contains(",") || o1.contains(";")) {
                    List<Locale.LanguageRange> list = Locale.LanguageRange.parse(acceptLanguage);
                    Optional<Locale.LanguageRange> range = list.stream()
                            .filter(r -> !r.getRange().startsWith("*"))
                            .max(Comparator.comparingDouble(Locale.LanguageRange::getWeight));

                    return range.map(r -> {
                        String rangeValue = r.getRange();
                        String[] parts = rangeValue.split("-");
                        String language = parts[0];
                        String country = parts.length > 1 ? parts[1] : "";
                        return new Locale(language, country);
                    }).orElse(Locale.getDefault());
                } else {
                    return Locale.forLanguageTag(acceptLanguage);
                }
            }).orElse(Locale.CHINESE);
        }
        return locale;
    }
    public static String getCurrentLangVal(Map<String, String> i18nMap) {
        Locale locale = getLocale();
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        String country = locale.getCountry();
        if (StringUtils.isNotEmpty(country)) {
            langDetail = String.format("%s-%s", currentLang, country);
        }

        if (i18nMap.containsKey(currentLang)) {
            return i18nMap.get(currentLang);
        } else {
            return i18nMap.containsKey(langDetail) ? i18nMap.get(langDetail) : i18nMap.get("default");
        }
    }

}
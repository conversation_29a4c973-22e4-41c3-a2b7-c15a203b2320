package com.caidaocloud.pangu.manager.dto;

import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ArrangementDto {

    private String arrangementId;

    private String arrangementVid;

    private ArrangementType type;

    private String name;

    @Data
    public static class Exec{
        private String arrangementId;
        private Map<String, String> context;
    }

}

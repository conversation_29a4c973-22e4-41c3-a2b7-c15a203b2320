package com.caidaocloud.pangu.infrastructure.util;

import java.util.Map;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.SpringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
public class RestTemplateUtil {

    private static RestTemplate restTemplate;

    private static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            synchronized (RestTemplateUtil.class) {
                if (restTemplate == null) {
                    restTemplate = SpringUtil.getBean(RestTemplate.class);
                    if (restTemplate == null) {
                        restTemplate = new RestTemplate();
                    }
                }
            }
        }
        return restTemplate;
    }

    @SneakyThrows
    public static <T> T postFormData(String address, String api, String cookie, Map<String, Object> parameter, Class<T> clazz) {
        ResponseEntity<T> response = postFormDataResp(address, api, cookie, parameter, clazz);
        if (response.getStatusCode() != HttpStatus.OK) {
            log.error("Request failed,address={},api={}", address, api);
            throw new ServerException("Request failed");
        }
        return response.getBody();
    }
    @SneakyThrows
    public static <T> ResponseEntity<T> postFormDataResp(String address, String api, String cookie, Map<String, Object> parameter, Class<T> clazz) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(address + api);
        LinkedMultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        for (Map.Entry<String, Object> entry : parameter.entrySet()) {
            if (entry.getValue() != null) {
                map.add(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        HttpEntity httpEntity = new HttpEntity(map, header(cookie));

        ResponseEntity<T> response = getRestTemplate().exchange(uriComponentsBuilder.build()
                .toUriString(), HttpMethod.POST, httpEntity, clazz);
        return response;
    }

    private static HttpHeaders header(String cookie) {
        HttpHeaders headers = new HttpHeaders();
        if (cookie != null) {
            headers.add("Cookie", cookie);
        }
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return headers;
    }

}

package com.caidaocloud.pangu.manager.annotations;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class FeignThrowAspect {

    @Pointcut("@annotation(com.caidaocloud.pangu.manager.annotations.FeignThrow)")
    public void feignThrowsPointCut() {
    }

    @Around("feignThrowsPointCut()")
    public Object aroundFeignCall(ProceedingJoinPoint point) throws ServerException{
        try {
            Result result = (Result) point.proceed();
            if(!result.isSuccess()){
                throw new ServerException(result.getMsg());
            }
            return result;
        } catch (Throwable e) {
            log.info("feign 调用失败", e);
            throw new ServerException(e.getMessage());
        }
    }
}

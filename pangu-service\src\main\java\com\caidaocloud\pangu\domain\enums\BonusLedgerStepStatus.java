package com.caidaocloud.pangu.domain.enums;

/**
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
public enum BonusLedgerStepStatus {
	NOT_EXECUTED(0, "未执行"),
	IN_PROGRESS(1, "执行中"),
	EXECUTED(2, "已执行"),
	ERROR(3, "执行错误");

	private final int value;
	private final String display;

	// 构造方法
	BonusLedgerStepStatus(int value, String display) {
		this.value = value;
		this.display = display;
	}

	public int getValue() {
		return value;
	}

	public String getDisplay() {
		return display;
	}

	// 根据value获取枚举
	public static BonusLedgerStepStatus fromValue(int value) {
		for (BonusLedgerStepStatus status : values()) {
			if (status.value == value) {
				return status;
			}
		}
		throw new IllegalArgumentException("Unexpected value: " + value);
	}

	// 根据display获取枚举
	public static BonusLedgerStepStatus fromDisplay(String display) {
		for (BonusLedgerStepStatus status : values()) {
			if (status.display.equals(display)) {
				return status;
			}
		}
		throw new IllegalArgumentException("Unexpected display: " + display);
	}
}

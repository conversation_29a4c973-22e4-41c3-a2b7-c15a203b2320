package com.caidaocloud.pangu.node.business.service.functions;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Options;
import com.googlecode.aviator.lexer.token.OperatorType;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Maps;
import lombok.val;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class Sum extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "SUM";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        BigDecimal result = new BigDecimal(0);
        val log = new StringBuilder(getName()).append("(");
        for(AviatorObject aviatorObject : aviatorObjects){
            val decimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
            log.append(decimal).append(",");
            result = result.add(decimal);
        }
        if(FunctionLogTool.logEnabled()){
            log.setLength(log.length()-1);
            log.append(")=").append(result);
            FunctionLogTool.log(log.toString());
        }
        return new AviatorDecimal(result);
    }
}

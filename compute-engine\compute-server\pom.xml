<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>compute-engine</artifactId>
		<groupId>com.caidaocloud</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>compute-server</artifactId>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<netty.version>4.1.115.Final</netty.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-bom</artifactId>
				<version>${netty.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>compute-remote-impl-akka</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>compute-remote-impl-vertx</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.baomidou</groupId>-->
<!--			<artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--			<version>3.4.3.2</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>org.freemarker</groupId>-->
<!--					<artifactId>freemarker</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--			<scope>runtime</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.caidaocloud</groupId>-->
<!--			<artifactId>galaxy-service-security</artifactId>-->
<!--			<version>1.0.0-SNAPSHOT</version>-->
<!--			<scope>runtime</scope>-->
<!--		</dependency>-->
	</dependencies>
</project>
{"identifier": "entity.payroll.BonusIndicator${defBid}", "name": "指标数据${defName}", "owner": "payroll", "timelineEnabled": false, "standardProperties": [{"property": "indicatorBy", "name": "统计维度", "dataType": "String", "widgetType": "text"}, {"property": "calcPeriod", "name": "统计周期", "dataType": "String", "widgetType": "text"}, {"property": "indicator", "name": "统计值", "dataType": "String", "widgetType": "text"}]}
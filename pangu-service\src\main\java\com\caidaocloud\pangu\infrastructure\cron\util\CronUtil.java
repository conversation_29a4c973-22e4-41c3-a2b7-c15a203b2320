package com.caidaocloud.pangu.infrastructure.cron.util;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Optional;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.infrastructure.cron.constraint.DailyConstraint;
import com.cronutils.model.Cron;
import com.cronutils.model.definition.CronConstraintsFactory;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;

/**
 *
 * <AUTHOR>
 * @date 2023/7/18
 */
public class CronUtil {

	private static final CronParser cronParser;

	static {
		CronDefinition definition = quartzDef().withCronValidation(new DailyConstraint()).instance();
		cronParser = new CronParser(definition);
	}

	private static CronDefinitionBuilder quartzDef() {
		return CronDefinitionBuilder.defineCron()
				.withSeconds().withValidRange(0, 59).and()
				.withMinutes().withValidRange(0, 59).and()
				.withHours().withValidRange(0, 23).and()
				.withDayOfMonth().withValidRange(1, 31).supportsL().supportsW().supportsLW().supportsQuestionMark()
				.and()
				.withMonth().withValidRange(1, 12).and()
				.withDayOfWeek().withValidRange(1, 7).withMondayDoWValue(2).supportsHash().supportsL()
				.supportsQuestionMark().and()
				.withYear().withValidRange(1970, 2099).withStrictRange().optional().and()
				.withCronValidation(CronConstraintsFactory.ensureEitherDayOfWeekOrDayOfMonth());
	}

	public static Cron parse(String exp) {
		try {
			return cronParser.parse(exp);
		}
		catch (ServerException e) {
			throw e;
		}
		catch (IllegalArgumentException e) {
			throw new ServerException("Illegal cron expression", e);
		}
	}

	public static long nextExecTime(String exp) {
		ZonedDateTime now = ZonedDateTime.now();
		ExecutionTime time = ExecutionTime.forCron(parse(exp));
		Optional<ZonedDateTime> zonedDateTime = time.nextExecution(now);
		if (!zonedDateTime.isPresent()) {
			throw new ServerException("Parse next exec time occurs error");
		}
		return zonedDateTime.get().toInstant().toEpochMilli();
	}
}

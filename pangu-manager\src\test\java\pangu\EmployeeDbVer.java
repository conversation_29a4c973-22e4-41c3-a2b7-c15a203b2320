package pangu;/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


import java.io.Serializable;
import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ignite.cache.query.annotations.QuerySqlField;

/**
 * This class represents employee object.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeDbVer implements Serializable {
    @QuerySqlField(index = true)
    private Long empId;

    /** Name. */
    private String name;

    private String email;

    /** Salary. */
    private long salary;

    /** Address. */
    private Address addr;

    /** Departments. */
    private Collection<String> departments;

    /** {@inheritDoc} */
    @Override public String toString() {
        return "Employee [name=" + name +
            ", salary=" + salary +
            ", address=" + addr +
            ", departments=" + departments + ']';
    }
}


package com.caidaocloud.pangu.application.dto;

import javax.validation.constraints.NotBlank;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import lombok.Data;

/**
 * 奖金结构--奖金项配置
 */
@Data
public class BonusStructItemDto {
    private String bid;
    @NotBlank(message = "计件工资结果不能为空")
    private String structId;
    @NotBlank(message = "项目名称不能为空")
    private String name;
    private String type;
    @NotBlank(message = "取值模型不能为空")
    private String valueIdentifier;
    @NotBlank(message = "取值字段不能为空")
    private String valueProperty;
    @NotBlank(message = "员工工号对应字段不能为空")
    private String worknoProperty;
    @NotBlank(message = "计算年月对应字段不能为空")
    private String monthProperty;
    private String remark;

    private Integer sort;



}

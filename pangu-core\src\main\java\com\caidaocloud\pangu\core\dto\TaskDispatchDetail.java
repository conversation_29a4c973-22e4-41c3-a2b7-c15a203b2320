package com.caidaocloud.pangu.core.dto;

import java.io.Serializable;

import lombok.Data;

@Data
public class TaskDispatchDetail implements Serializable {
    private String id;

    private String execSeqId;

    private String execNodeSeqId;

    private String dispatchNodeId;

    private boolean subTask;

    private int from;

    private int to;

    private Status status;

    public enum Status implements Serializable{
        DISPATCHED, SUCCESS, FAILED
    }

}

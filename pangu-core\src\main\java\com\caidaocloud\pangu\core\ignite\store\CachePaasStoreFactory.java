package com.caidaocloud.pangu.core.ignite.store;

import javax.cache.configuration.Factory;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/1/14
 */
@Data
public class CachePaasStoreFactory<K,V>  implements Factory<CachePaasStore<K,V>>{
	private String identifier;
	private String tenantId;



	@Override
	public CachePaasStore<K, V> create() {
		CachePaasMapStore store = new CachePaasMapStore();
		store.setIdentifier(identifier);
		store.setTenantId(tenantId);
		return (CachePaasStore) store;
	}
}

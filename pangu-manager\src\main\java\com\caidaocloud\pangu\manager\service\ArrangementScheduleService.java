package com.caidaocloud.pangu.manager.service;

import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.dto.NodeExec;
import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.manager.feign.ArrangementClient;
import com.caidaocloud.pangu.manager.feign.PanguNodeFeign;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExec;
import com.caidaocloud.pangu.manager.util.DispatchTool;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ArrangementScheduleService {

    @Autowired
    private ArrangementExecService arrangementExecService;

    @Autowired
    private PanguNodeFeign panguNodeFeign;

    @Autowired
    private NodeManagerService nodeManagerService;

    @Autowired
    private IgniteUtil igniteUtil;

    @Autowired
    private DispatchTool dispatchTool;

    @Autowired
    private ArrangementClient arrangementClient;

    @Autowired
    private ArrangementScheduleService arrangementScheduleService;

    public void scheduleArrangementNode() {
        new Thread(()->{
            while (true) {
                try {
                    arrangementScheduleService.startWaiting();
                } catch (Throwable e) {
                    try {
                        Thread.sleep(500);
                    } catch (Exception exception) {
                    }
                    log.error("scheduling waiting arrangement error", e);
                }
            }
        }).start();
        new Thread(()->{
            while (true) {
                try {
                    List<ArrangementNodeExec> checkings = ArrangementNodeExec.listByStatus(ArrangementNodeExec.Status.CHECK);
                    if(checkings.isEmpty()){
                        Thread.sleep(500);
                    }
                    checkings.forEach(checking->{
                        try{
                            arrangementScheduleService.checkChecking(checking);
                        }finally {
                            SecurityUserUtil.removeSecurityUserInfo();
                        }
                    });
                } catch (Throwable e) {
                    try {
                        Thread.sleep(500);
                    } catch (Exception exception) {
                    }
                    log.error("scheduling checking arrangement error", e);
                }
            }
        }).start();

        new Thread(()->{
            while (true) {
                try {
                    dispatchSubTask();
                } catch (Throwable e) {
                    try {
                        Thread.sleep(500);
                    } catch (Exception exception) {
                    }
                    log.error("scheduling checking arrangement error", e);
                }
            }
        }).start();

        new Thread(()->{
            while (true) {
                try {
                    arrangementExecService.redispatch();
                } catch (Throwable e) {
                    try {
                        Thread.sleep(500);
                    } catch (Exception exception) {
                    }
                    log.error("scheduling checking arrangement error", e);
                }
            }
        }).start();
    }

    @SneakyThrows
    public void dispatchSubTask() {
        Optional<ArrangementNodeExec> loaded = ArrangementNodeExec.first(ArrangementNodeExec.Status.LOADED);
        if(!loaded.isPresent()){
            Thread.sleep(500);
        }else{
            val nodeExec = loaded.get();
            val total = nodeExec.getTotal();
            val step = nodeExec.getStep();
            val dispatchContext = dispatchTool.getDispatchContext();
            for(int i = 0; i < total; i = i + step){
                val start = i;
                val end = i + step - 1;
                val dispatched = dispatchContext.count(DataFilter.eq("execNodeSeqId", nodeExec.getExecSeqId())
                        .andEq("from", String.valueOf(start)).andEq("to", String.valueOf(end)), "execNodeSeqId");
                if(dispatched > 0){
                    continue;
                }
                nodeManagerService.applyUtilSuccess((node)->{
                    TaskDispatchDetail dispatch = new TaskDispatchDetail();
                    dispatch.setExecSeqId(nodeExec.getExecSeqId());
                    dispatch.setExecNodeSeqId(nodeExec.getExecNodeSeqId());
                    dispatch.setDispatchNodeId(node.getId());
                    dispatch.setSubTask(true);
                    dispatch.setStatus(TaskDispatchDetail.Status.DISPATCHED);
                    dispatch.setFrom(start);
                    dispatch.setTo(end);
                    dispatch.setId(SnowUtil.nextId());
                    val address = new Address();
                    address.setHost(node.getHost());
                    address.setPort(Integer.valueOf(node.getPort()));
                    val execDto = new NodeExec.SubNodeExec();
                    execDto.setArrangementNodeId(nodeExec.getArrangementNodeId());
                    execDto.setArrangementVid(nodeExec.getArrangementVid());
                    execDto.setExecSeqId(nodeExec.getExecSeqId());
                    execDto.setExecNodeSeqId(nodeExec.getExecNodeSeqId());
                    execDto.setContext(nodeExec.getContext());
                    execDto.setFrom(start);
                    execDto.setTo(end);
                    execDto.setCacheId(nodeExec.getCacheId());
                    execDto.setResultCacheId(nodeExec.getResultCacheId());
                    execDto.setTenantId(nodeExec.getActualTenantId());
                    arrangementClient.dispatchSubTask(address, execDto);
                    dispatchContext.put(dispatch.getExecNodeSeqId()+ "-" + start, dispatch);
                });
            }
            nodeExec.setStatus(ArrangementNodeExec.Status.DISPATCHED);
            nodeExec.update();
            if(total <= 0){
                arrangementScheduleService.autoEndDispatched(nodeExec);
            }
        }
    }

    @Transactional
    public void autoEndDispatched(ArrangementNodeExec nodeExec){
        try{
            val user = new SecurityUserInfo();
            user.setTenantId(nodeExec.getActualTenantId());
            SecurityUserUtil.setSecurityUserInfo(user);
            nodeExec.setStatus(ArrangementNodeExec.Status.SUCCESS);
            nodeExec.update();
            val nextList = panguNodeFeign.fetchNextNodes(nodeExec.getArrangementVid(), nodeExec.getArrangementNodeId()).getData();
            val existedTask = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(nodeExec.getExecSeqId(), nextList);
            nextList.forEach(next->{
                val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                if(matched.isPresent()){
                    ArrangementNodeExec matchedNodeExec = matched.get();
                    matchedNodeExec.setSkip(false);
                    matchedNodeExec.update();
                }else{
                    arrangementExecService.initNext(nodeExec, next, false, ArrangementNodeExec.Status.CHECK);
                }
            });
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    @SneakyThrows
    @Transactional
    public void startWaiting(){
        Optional<ArrangementNodeExec> waiting = ArrangementNodeExec.first(ArrangementNodeExec.Status.WAITING);
        log.info("waiting is present : " + waiting.isPresent());
        if (waiting.isPresent()) {
            waiting.ifPresent(it -> arrangementExecService.startNode(it));
        } else {
            Thread.sleep(500);
        }
    }

    @SneakyThrows
    @Transactional
    public void checkChecking(ArrangementNodeExec checking){

        val user = new SecurityUserInfo();
        user.setTenantId(checking.getActualTenantId());
        SecurityUserUtil.setSecurityUserInfo(user);
        List<String> previousIds = panguNodeFeign.fetchPreviousNodes(checking.getArrangementVid(),
                checking.getArrangementNodeId()).getData();

        val successPreviousCount = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(
                checking.getExecSeqId(), previousIds).stream().filter(it->Lists.list(ArrangementNodeExec.Status.SKIPPED,
                ArrangementNodeExec.Status.SUCCESS).contains(it.getStatus())).count();
        if(successPreviousCount < previousIds.size()){
            return;
        }else{
            checking = ArrangementNodeExec.loadByExecNodeSeqIdAndStatus(checking.getExecNodeSeqId(), ArrangementNodeExec.Status.CHECK).get();
            if (checking.isSkip()){
                arrangementExecService.skipNode(checking);
            }else{
                checking.setStatus(ArrangementNodeExec.Status.WAITING);
                checking.setWaitFrom(System.currentTimeMillis());
                checking.update();
            }
        }
    }
}

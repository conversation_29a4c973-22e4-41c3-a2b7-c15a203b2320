package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorNil;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.ignite.cache.query.QueryCursor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class GroupAvg extends AbstractVariadicFunction {

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        val property = FunctionUtils.getStringValue(aviatorObjects[0], env);
        val cacheId = (String)env.get("cacheId");
        val aviatorGroupByList = Lists.list(aviatorObjects);
        aviatorGroupByList.remove(0);
        val precision = 6;
        //aviatorGroupByList.remove(aviatorGroupByList.size() - 1);
        val roundingMode = RoundingMode.HALF_UP;
        //aviatorGroupByList.remove(aviatorGroupByList.size() - 1);
        val groupByList = aviatorGroupByList.stream().map(it->FunctionUtils.getStringValue(it, env)).collect(Collectors.toList());
        val igniteContext = SpringUtil.getBean(IgniteUtil.class).getContext(cacheId);
        QueryCursor<List<?>> sumResults = igniteContext.avg(null, property, precision, roundingMode, groupByList.toArray(new String[groupByList.size()]));
        sumResults.getAll().forEach(sumResult->{
            CalcReport.SubTaskValue groupResult = new CalcReport.SubTaskValue();
            groupResult.setProperty(property);
            for(int i = 0; i<groupByList.size(); i++){
                val key = groupByList.get(i);
                val value = sumResult.get(i);
                groupResult.getKeys().add(new KeyValue(key, null == value?null:String.valueOf(value)));
            }
            val count = sumResult.get(sumResult.size() - 2);
            val sum = sumResult.get(sumResult.size() - 1);
            String value = new BigDecimal(String.valueOf(sum)).divide(new BigDecimal(String.valueOf(count)), precision, roundingMode)
                    .toPlainString();
            groupResult.setValue(value);
            ExpressionTool.addGroupResult(groupResult);
        });
        return AviatorNil.NIL;
    }

    @Override
    public String getName() {
        return "GROUP_AVG";
    }
}

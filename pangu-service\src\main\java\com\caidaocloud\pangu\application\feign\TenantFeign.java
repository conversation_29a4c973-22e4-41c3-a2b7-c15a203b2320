package com.caidaocloud.pangu.application.feign;

import java.util.List;

import com.caidaocloud.pangu.application.dto.tenant.Tenant;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 * @date 2023/8/21
 */
@FeignClient(value = "${feign.rename.caidaocloud-maintenance-service:caidaocloud-maintenance-service}", fallback = TenantFeignFallback.class, configuration = FeignConfiguration.class, contextId = "tenantFeign")
public interface TenantFeign {
	@GetMapping("/api/maintenance/v1/tenant")
	Result<List<Tenant>> tenantList();
}

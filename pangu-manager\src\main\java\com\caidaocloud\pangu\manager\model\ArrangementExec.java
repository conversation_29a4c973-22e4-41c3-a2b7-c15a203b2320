package com.caidaocloud.pangu.manager.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.manager.mapper.ArrangementExecMapper;
import com.caidaocloud.pangu.manager.mapper.ArrangementNodeExecMapper;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Maps;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageImpl;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class ArrangementExec {

    private String id;

    public static final String identifier = "entity.pangu.ArrangementExec";

    private String arrangementId;

    private String arrangementVid;

    private String arrangementName;

    private String workNo;

    private String empName;

    private String execSeqId;

    private Status status;

    @DisplayAsObject
    private Map<String, String> context = Maps.map();

    private long waitFrom;

    private long started;

    private long ended;

    private String actualTenantId;

    private ArrangementType type;

    private long createTime;
    
    public static long countByArrangementAndStatus(String arrangementId, ArrangementExec.Status status) {
        return mapper().selectCount(new LambdaQueryWrapper<ArrangementExecPO>()
            .eq(ArrangementExecPO::getArrangementId, arrangementId)
            .eq(ArrangementExecPO::getStatus, status));
    }

    public static Optional<ArrangementExec> loadByExecSeqIdAndStatus(String execSeqId, Status status) {
        return Optional.ofNullable(mapper().selectOne(new LambdaQueryWrapper<ArrangementExecPO>()
            .eq(ArrangementExecPO::getExecSeqId, execSeqId)
            .eq(ArrangementExecPO::getStatus, status))).map(ArrangementExecPO::toArrangementExec);
    }

    public static PageResult<ArrangementExec> execLogs(String keywords, String arrangementId,
                                                       ArrangementType type, ArrangementExec.Status status,
                                                       Long start, Long end, int pageNo, int pageSize) {
        LambdaQueryWrapper<ArrangementExecPO> wrapper = new LambdaQueryWrapper<ArrangementExecPO>()
                .eq(ArrangementExecPO::getActualTenantId, SecurityUserUtil.getSecurityUserInfo().getTenantId());
        if(StringUtils.isNotEmpty(arrangementId)){
            wrapper.eq(ArrangementExecPO::getArrangementId, arrangementId);
        }
        if(null != type){
            wrapper.eq(ArrangementExecPO::getType, type);
        }
        if(null != status){
            wrapper.eq(ArrangementExecPO::getStatus, status);
        }
        if(null != start && start > 0){
            wrapper.ge(ArrangementExecPO::getWaitFrom, start);
        }
        if(null != end && end > 0){
            wrapper.le(ArrangementExecPO::getEnded, end);
        }
        if(StringUtils.isNotEmpty(keywords)){
            wrapper.and(it->
                it.like(ArrangementExecPO::getEmpName, keywords).or().like(ArrangementExecPO::getWorkNo, keywords)
            );
        }
        wrapper.orderByDesc(ArrangementExecPO::getStarted);
        val page = mapper().selectPage(new Page<>(pageNo - 1, pageSize), wrapper);
        return new PageResult<>(page.getRecords().stream().map(ArrangementExecPO::toArrangementExec).collect(Collectors.toList()),
                (int)page.getCurrent(), (int)page.getSize(), (int)page.getTotal());
    }

    public void update() {
        mapper().updateById(ArrangementExecPO.fromArrangementExec(this));
    }

    public void create() {
        id = SnowUtil.nextId();
        createTime = System.currentTimeMillis();
        mapper().insert(ArrangementExecPO.fromArrangementExec(this));
    }

    private static ArrangementExecMapper mapper() {
        return SpringUtil.getBean(ArrangementExecMapper.class);
    }
    public enum Status{
        STARTED, SUCCESS, ERROR
    }

}

package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.excption.ServerException;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.math.BigDecimal;
import java.util.Map;

public class Multi extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "MULTI";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        if(aviatorObjects.length < 1){
            throw new ServerException();
        }
        BigDecimal result = new BigDecimal(1);
        val log = new StringBuilder(getName()).append("(");
        for(AviatorObject aviatorObject : aviatorObjects){
            val decimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
            log.append(decimal).append(",");
            result = result.multiply(decimal);
        }
        if(FunctionLogTool.logEnabled()){
            log.setLength(log.length()-1);
            log.append(")=").append(result);
            FunctionLogTool.log(log.toString());
        }
        return new AviatorDecimal(result);
    }
}

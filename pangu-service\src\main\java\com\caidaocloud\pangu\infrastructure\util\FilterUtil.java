package com.caidaocloud.pangu.infrastructure.util;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.pangu.application.dto.BonusReportDto;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Slf4j
public class FilterUtil {
	/**
	 * Process filters from BasePage and set values to DTO properties
	 * @param basePage the DTO to process
	 */
	public static void processFilters(BasePage basePage) {
		if (basePage.getFilters() == null || basePage.getFilters().isEmpty()) {
			return;
		}

		for (FilterElement filter : basePage.getFilters()) {
			String prop = filter.getProp();
			Object value = filter.getValue();
			OpEnum op = filter.getOp();

			if (op == OpEnum.in) {
				if (!(value instanceof String)) {
					continue;
				}

				String strValue = (String) value;
				List<String> valueList = Arrays.asList(strValue.split(","));

				try {
					String setterName = "set" + prop.substring(0, 1).toUpperCase() + prop.substring(1);
					Method setter = basePage.getClass().getMethod(setterName, List.class);
					setter.invoke(basePage, valueList);
				} catch (Exception e) {
					log.warn("Failed to set property {} with value {} on DTO: {}", prop, valueList, e.getMessage());
				}
			} else {
				if (!(value instanceof String)) {
					continue;
				}

				String strValue = (String) value;

				try {
					String setterName = "set" + prop.substring(0, 1).toUpperCase() + prop.substring(1);
					Method setter = basePage.getClass().getMethod(setterName, String.class);
					setter.invoke(basePage, strValue);
				} catch (Exception e) {
					log.warn("Failed to set property {} with value {} on DTO: {}", prop, strValue, e.getMessage());
				}
			}
		}
	}
}

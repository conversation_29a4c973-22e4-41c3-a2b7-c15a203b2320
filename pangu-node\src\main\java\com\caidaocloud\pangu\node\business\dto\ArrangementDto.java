package com.caidaocloud.pangu.node.business.dto;

import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.pangu.core.enums.ArrangementType;
import com.caidaocloud.pangu.node.business.model.Arrangement;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("ArrangementDto")
public class ArrangementDto {

    private String arrangementId;

    private String name;

    private String description;

    private ArrangementType type;

    private Arrangement.TriggerType triggerType;

    private String cron;

    public Arrangement toEntity(){
        Arrangement arrangement = FastjsonUtil.convertObject(this, Arrangement.class);
        arrangement.setArrangementId(SnowUtil.nextId());
        arrangement.setStatus(Arrangement.Status.DRAFT);
        return arrangement;
    }

    public Arrangement toEntity(Arrangement existed, boolean updateVersion){
        if(updateVersion){
            Arrangement arrangement = FastjsonUtil.convertObject(this, Arrangement.class);
            arrangement.setStatus(Arrangement.Status.RE_DRAFT);
            existed.setStatus(Arrangement.Status.UPDATED);
            return arrangement;
        }else{
            BeanUtils.copyProperties(this, existed);
            return existed;
        }

    }

    @Data
    @ApiModel("ArrangementDto.Detail")
    public static class Detail {

        private String arrangementId;

        private List<Arrangement.Node> nodes = Lists.list();

        private List<Arrangement.Note> notes = Lists.list();

        private List<Map> edges = Lists.list();

        private Map viewport = Maps.map();

        public Arrangement.Detail toEntity(Arrangement.Detail existed, boolean updateVersion) {
            if(updateVersion){
                Arrangement.Detail detail = FastjsonUtil.convertObject(this, Arrangement.Detail.class);
                detail.setStatus(Arrangement.Status.RE_DRAFT);
                existed.setStatus(Arrangement.Status.UPDATED);
                return detail;
            }else{
                BeanUtils.copyProperties(this, existed);
                return existed;
            }
        }

        public Arrangement.Detail toEntity() {
            Arrangement.Detail detail = FastjsonUtil.convertObject(this, Arrangement.Detail.class);
            detail.setStatus(Arrangement.Status.DRAFT);
            return detail;
        }
    }

    @Data
    @ApiModel("ArrangementDto.Publish")
    public static class Publish{
        private String arrangementId;
    }

    @Data
    @ApiModel("ArrangementDto.PublishResult")
    public static class PublishResult{

        private boolean published = true;

        private Map<String, List<Arrangement.Error>> errors = Maps.map();
    }

    @Data
    @ApiModel("ArrangementDto.Copy")
    public static class Copy{
        private String arrangementId;
        private String name;
    }

    @Data
    @ApiModel("ArrangementDto.Delete")
    public static class Delete{
        private String arrangementId;
    }

    @Data
    @ApiModel("ArrangementDto.Exec")
    public static class Exec{
        private String arrangementId;
        private Map<String, String> context;
    }

    @Data
    @ApiModel("ArrangementDto.Recover")
    public static class Recover{
        private String arrangementId;
    }

}

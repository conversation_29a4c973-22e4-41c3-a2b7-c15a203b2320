package com.caidaocloud.pangu.domain.entity;

import java.util.Objects;

import com.caidaocloud.pangu.domain.enums.MonthPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("选人周期")
public class PeriodDate {
    @ApiModelProperty("月份")
    private MonthPeriod month;
    @ApiModelProperty("日")
    private Integer day; // 1-31

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PeriodDate that = (PeriodDate) o;
        return month == that.month && Objects.equals(day, that.day);
    }

    @Override
    public int hashCode() {
        return Objects.hash(month, day);
    }
}
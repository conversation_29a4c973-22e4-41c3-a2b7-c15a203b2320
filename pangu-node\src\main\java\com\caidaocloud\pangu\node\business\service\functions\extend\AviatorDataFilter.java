package com.caidaocloud.pangu.node.business.service.functions.extend;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorType;

import java.util.Map;

public class AviatorDataFilter extends AviatorObject {

    private DataFilter filter;

    public AviatorDataFilter(DataFilter filter){
        this.filter = filter;
    }

    @Override
    public int innerCompare(AviatorObject aviatorObject, Map<String, Object> map) {
        return 0;
    }

    @Override
    public AviatorType getAviatorType() {
        return AviatorType.JavaType;
    }

    @Override
    public Object getValue(Map<String, Object> map) {
        return filter;
    }
}

// package com.caidaocloud.pangu.infrastructure.ignite.store;
//
// import javax.cache.configuration.Factory;
//
// import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStore;
// import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
// import org.apache.ignite.configuration.CacheConfiguration;
//
// /**
//  *
//  * <AUTHOR>
//  * @date 2023/7/28
//  */
// public class IgniteBlobStoreConfigurationFactory implements IgniteDbStoreConfigurationFactory<CacheJdbcBlobStore> {
// 	// private final String SELECT_TEMPLATE = ;
//
// 	private DbContext dbContext;
//
// 	public IgniteBlobStoreConfigurationFactory(DbContext dbContext) {
// 		this.dbContext = dbContext;
// 	}
//
// 	@Override
// 	public Factory<CacheJdbcBlobStore> createFactory(String cacheName) {
// 		CacheJdbcBlobStoreFactory factory = new CacheJdbcBlobStoreFactory<>();
//
// 		factory.setConnectionUrl(dbContext.getConnUrl());
// 		factory.setUser(dbContext.getUser());
// 		factory.setPassword(dbContext.getPwd());
//
// 		factory.setLoadQuery(dbContext.generateLoadSql(cacheName));
// 		factory.setDeleteQuery(dbContext.generateDeleteSql(cacheName));
// 		factory.setInsertQuery(dbContext.generateInsertSql(cacheName));
// 		factory.setUpdateQuery(dbContext.generateUpdateSql(cacheName));
// 		factory.setCreateTableQuery(dbContext.generateCreateTableSql(cacheName));
// 		return factory;
// 	}
//
// 	@Override
// 	public CacheConfiguration createCacheConfiguration(String cacheName) {
// 		CacheConfiguration configuration = new CacheConfiguration<>(cacheName);
// 		configuration.setWriteThrough(true);
// 		configuration.setReadThrough(true);
// 		configuration.setCacheStoreFactory(createFactory(cacheName));
// 		return configuration;
// 	}
//
// }

package com.caidaocloud.pangu.application.event;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.domain.entity.BonusLedger;
import com.caidaocloud.pangu.domain.entity.BonusLedgerEmp;
import com.caidaocloud.pangu.domain.entity.BonusReport;
import com.caidaocloud.pangu.domain.entity.BonusStructItem;
import com.caidaocloud.pangu.domain.enums.BonusLedgerEmpCalcStatus;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EmployeeStatsSubscriber {

    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = BonusConstant.BONUS_EMP_STATS_QUEUE, durable = "true"),
                    exchange = @Exchange(value = BonusConstant.BONUS_DIRECT_EXCHANGE),
                    key = {BonusConstant.BONUS_EMP_STATS_ROUTINGKEY}
            )
    )
    @RabbitHandler
    public void receive(String msg) {
        EmployeeStatsMessageDTO event = FastjsonUtil.toObject(msg, EmployeeStatsMessageDTO.class);
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            doProcess(event.getLedgerId());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    private void doProcess(String ledgerId) {
        // Fetch ledger based on ledgerId
        BonusLedger ledger = BonusLedger.load(ledgerId);
        if (ledger.isDeleted()) {
            log.info("ledger is deleted,ledgerId={}", ledgerId);
            return;
        }

        // Use the new counting methods to get statistics
        int total = ledger.countEmp();
        int succeed = ledger.countReport();

        // Update ledger with statistics
        ledger.setTotal(total);
        ledger.setSucceed(succeed);
        ledger.update();
    }
}
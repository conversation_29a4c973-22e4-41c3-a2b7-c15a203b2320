import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.Ignition;
import org.apache.ignite.cache.QueryEntity;
import org.apache.ignite.cache.query.SqlFieldsQuery;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.configuration.CacheConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/7/6
 */

@Slf4j
public class SqlTests {

	static CacheConfiguration code_sql_cache_configuration = new CacheConfiguration();
	static String sql_cache_name = "myCodeBasedAccountV2";
	static String table_name = "MY_ACCOUNT";
	static Ignite ignite;
	static {
		code_sql_cache_configuration.setName(sql_cache_name);
		QueryEntity entity = new QueryEntity();
		entity.setTableName(table_name);
		entity.setKeyFieldName("ID");
		entity.setKeyType("java.lang.Long");
		entity.setValueType("java.lang.Object");
		entity.setFields(((LinkedHashMap) Maps.map("ID", "java.lang.Long", "age", Integer.class.getName(), "amount", Double.class.getName(),
				"updateDate", "java.util.Date")));
		code_sql_cache_configuration.setWriteThrough(true);
		code_sql_cache_configuration.setReadThrough(true);
		CacheJdbcPojoStoreFactory factory = new CacheJdbcPojoStoreFactory<>();
		factory.setDataSourceFactory(() -> {
			HikariDataSource driverManagerDataSource = new HikariDataSource();
			driverManagerDataSource.setDriverClassName("org.postgresql.Driver");
			driverManagerDataSource.setJdbcUrl("**********************************************************************");
			driverManagerDataSource.setUsername("postgres");
			driverManagerDataSource.setPassword("Caidao01");
			return driverManagerDataSource;
		});
		code_sql_cache_configuration.setCacheStoreFactory(factory);

		code_sql_cache_configuration.setQueryEntities(Lists.list(entity));

		ignite = Ignition.start();
	}


	@Test
	public void InsertAndQueryTest(){
		//This cache configured in `application.yml`.

		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		accounts.query(new SqlFieldsQuery(
				"CREATE TABLE MY_ACCOUNT (id BIGINT PRIMARY KEY, name VARCHAR) WITH \"template=replicated\"")).getAll();
		//SQL table configured via QueryEntity in `application.yml`
		String qry = "INSERT INTO MY_ACCOUNT(ID, age,amount, UPDATEDATE) VALUES(?, ?,?, ?)";

		accounts.query(new SqlFieldsQuery(qry).setArgs(1, 250, 100, new Date()).setLocal(true)).getAll();
		accounts.query(new SqlFieldsQuery(qry).setArgs(2, 255,150, new Date())).getAll();
		accounts.query(new SqlFieldsQuery(qry).setArgs(3, 1,200, new Date())).getAll();

		qry = "SELECT * FROM MY_ACCOUNT";

		List<List<?>> res = accounts.query(new SqlFieldsQuery(qry)).getAll();

		for (List<?> row : res)
			System.out.println("(" + row.get(0) + ", " + row.get(1) + ", " + row.get(2) + ")");
	}

	public void IntegerDataInit(){
		//This cache configured in `application.yml`.
		ignite.destroyCache(sql_cache_name);
		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		accounts.removeAll();
		//SQL table configured via QueryEntity in `application.yml`
		String qry = "INSERT INTO MY_ACCOUNT(ID, age,amount, UPDATEDATE) VALUES(?, ?,?, ?)";

		accounts.query(new SqlFieldsQuery(qry).setArgs(1, 10,100, new Date())).getAll();
		accounts.query(new SqlFieldsQuery(qry).setArgs(2, 255,200D, new Date())).getAll();
		accounts.query(new SqlFieldsQuery(qry).setArgs(3, ((int) (1e5 + 1)),300D, new Date())).getAll();
	}

	@Test
	public void SumTest(){
		IntegerDataInit();
		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		List<List<?>> all = accounts.query(new SqlFieldsQuery("select sum(age) from my_account")).getAll();
		log.info("sum result:{}", all.get(0).get(0));
		Assert.assertEquals(1, all.size());
		Assert.assertEquals( ((long) (10 + 255 + 1e5 + 1)), all.get(0).get(0));
	}

	@Test
	public void MaxTest(){
		IntegerDataInit();
		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		List<List<?>> all = accounts.query(new SqlFieldsQuery("select max(age) from my_account")).getAll();
		log.info("max result:{}", all.get(0).get(0));
		Assert.assertEquals(1, all.size());
		Assert.assertEquals(((int) (1e5 + 1)), all.get(0).get(0));
	}

	@Test
	public void MinTest(){
		IntegerDataInit();
		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		List<List<?>> all = accounts.query(new SqlFieldsQuery("select min(age) from my_account")).getAll();
		log.info("min result:{}", all.get(0).get(0));
		Assert.assertEquals(1, all.size());
		Assert.assertEquals(10, all.get(0).get(0));
	}

	@Test
	public void countTest(){
		IntegerDataInit();
		IgniteCache<Long, Object> accounts = ignite.getOrCreateCache(code_sql_cache_configuration);
		List<List<?>> all = accounts.query(new SqlFieldsQuery("select count(age) from my_account")).getAll();
		log.info("min result:{}", all.get(0).get(0));
		Assert.assertEquals(1, all.size());
		Assert.assertEquals(3L, all.get(0).get(0));
	}

}
package com.caidaocloud.pangu.manager.service;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.pangu.core.dto.CalcReport;
import com.caidaocloud.pangu.core.dto.TaskDispatchDetail;
import com.caidaocloud.pangu.core.ignite.IgniteUtil;
import com.caidaocloud.pangu.manager.feign.PanguNodeFeign;
import com.caidaocloud.pangu.manager.model.ArrangementExec;
import com.caidaocloud.pangu.manager.model.ArrangementNodeExec;
import com.caidaocloud.pangu.manager.util.DispatchTool;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.Resource;

@Service
@Slf4j
public class ReportService {

    @Autowired
    private ArrangementExecService arrangementExecService;

    @Autowired
    private DispatchTool dispatchTool;

    @Autowired
    private Locker locker;

    @Autowired
    private PanguNodeFeign panguNodeFeign;

    @Autowired
    private ResultNoticeService resultNoticeService;

    @Autowired
    private NodeManagerService nodeManagerService;
    @Resource
    private IgniteUtil igniteUtil;

    @Transactional
    public boolean report(CalcReport.Start report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatus(report.getExecNodeSeqId(), ArrangementNodeExec.Status.STARTED).ifPresent(it -> {
                if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                    return;
                }
                it.setStatus(ArrangementNodeExec.Status.SUCCESS);
                it.setEnded(System.currentTimeMillis());
                it.update();
                report.getNextNodeIds().forEach(next -> {
                    arrangementExecService.initNext(it, next, false, ArrangementNodeExec.Status.WAITING);
                });
            });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock.unlock();
        }

    }

    @Transactional
    public boolean report(CalcReport.End report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatus(report.getExecNodeSeqId(), ArrangementNodeExec.Status.STARTED)
                    .ifPresent(it -> {
                        if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                            return;
                        }
                        it.setStatus(ArrangementNodeExec.Status.SUCCESS);
                        it.setEnded(System.currentTimeMillis());
                        it.update();
                        ArrangementExec.loadByExecSeqIdAndStatus(it.getExecSeqId(), ArrangementExec.Status.STARTED)
                                .ifPresent(arrangementExec -> {
                                    arrangementExec.setEnded(System.currentTimeMillis());
                                    arrangementExec.setStatus(ArrangementExec.Status.SUCCESS);
                                    arrangementExec.update();
                                    resultNoticeService.success(arrangementExec);
                                    destroyCache(it.getExecSeqId());
                                });
                    });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock.unlock();
        }

    }

    private void destroyCache(String execSeqId) {
        log.info("Destroying cache for execSeqId: {}", execSeqId);
        
        // Get all ArrangementNodeExec instances for this execSeqId
        List<ArrangementNodeExec> nodeExecs = ArrangementNodeExec.listByExecSeqId(execSeqId);
        
        // Destroy caches for each node execution
        for (ArrangementNodeExec nodeExec : nodeExecs) {
            if (StringUtils.isNotBlank(nodeExec.getCacheId())) {
                try {
                    log.debug("Destroying cache with ID: {}", nodeExec.getCacheId());
                    igniteUtil.getContext(nodeExec.getCacheId()).destroy();
                } catch (Exception e) {
                    log.error("Failed to destroy cache: {} for execNodeSeqId: {}", 
                            nodeExec.getCacheId(), nodeExec.getExecNodeSeqId(), e);
                }
            }
            
            if (StringUtils.isNotBlank(nodeExec.getResultCacheId())) {
                try {
                    log.debug("Destroying result cache with ID: {}", nodeExec.getResultCacheId());
                    igniteUtil.getContext(nodeExec.getResultCacheId()).destroy();
                } catch (Exception e) {
                    log.error("Failed to destroy result cache: {} for execNodeSeqId: {}", 
                            nodeExec.getResultCacheId(), nodeExec.getExecNodeSeqId(), e);
                }
            }
        }
        
        log.info("Cache destruction completed for execSeqId: {}", execSeqId);
    }

    @Transactional
    public boolean report(CalcReport.Condition report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatus(report.getExecNodeSeqId(), ArrangementNodeExec.Status.STARTED)
                    .ifPresent(it -> {
                        if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                            return;
                        }
                        it.setStatus(ArrangementNodeExec.Status.SUCCESS);
                        it.setEnded(System.currentTimeMillis());
                        it.update();
                        val existedTask = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(it.getExecSeqId(),
                                Sequences.sequence(report.getSkipNextNodes())
                                        .join(report.getNotSkipNextNodes()).toList());
                        report.getSkipNextNodes().forEach(next->{
                            val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                            if(!matched.isPresent()){
                                arrangementExecService.initNext(it, next, true, ArrangementNodeExec.Status.CHECK);
                            }
                        });
                        report.getNotSkipNextNodes().forEach(next->{
                            val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                            if(matched.isPresent()){
                                ArrangementNodeExec nodeExec = matched.get();
                                nodeExec.setSkip(false);
                                nodeExec.update();
                            }else{
                                arrangementExecService.initNext(it, next, false, ArrangementNodeExec.Status.CHECK);
                            }
                        });
                    });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock.unlock();
        }

    }

    @Transactional
    public boolean report(CalcReport.TaskDataLoaded report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            val result = new AtomicBoolean(false);
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatus(report.getExecNodeSeqId(), ArrangementNodeExec.Status.STARTED).ifPresent(it -> {
                if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                    return;
                }
                result.set(true);
                it.setCacheId(report.getCacheId());
                it.setResultCacheId(report.getResultCacheId());
                it.setTotal(report.getTotal());
                it.setStep(report.getStep());
                it.setStatus(ArrangementNodeExec.Status.LOADED);
                it.update();
            });
            nodeManagerService.rtn(report.getPanguNodeId());
            return result.get();
        }finally {
            lock.unlock();
        }

    }



    @Transactional
    public boolean report(CalcReport.SubTask report) {
        val lock0 = locker.getLock("execSeq_"+report.getExecSeqId());
        lock0.lock();
        try{
            val from = report.getFrom();
            val execNodeSeqId = report.getExecNodeSeqId();
            val dispatchContext = dispatchTool.getDispatchContext();
            val dispatchDetail = dispatchContext.get(execNodeSeqId + "-" + from);
            if (!StringUtils.equals(dispatchDetail.getDispatchNodeId(), report.getPanguNodeId())) {
                nodeManagerService.rtn(report.getPanguNodeId());
                return false;
            }
            dispatchDetail.setStatus(TaskDispatchDetail.Status.SUCCESS);
            ArrangementNodeExec.loadByExecNodeSeqIdAndStatusList(execNodeSeqId,
                    Lists.list(ArrangementNodeExec.Status.DISPATCHED, ArrangementNodeExec.Status.LOADED))
                    .ifPresent(exec->{
                        val lock = locker.getLock("execNodeSeq_"+execNodeSeqId);
                        lock.lock();
                        try{
                            dispatchContext.put(execNodeSeqId + "-" + from, dispatchDetail);
                            val statusList = dispatchContext.loadProperties(DataFilter.eq("execNodeSeqId", execNodeSeqId), Lists.list("status"), Lists.list(), 0,0);
                            val done = statusList.stream().allMatch(it->it.get(1).getValue().toString().equals(TaskDispatchDetail.Status.SUCCESS.toString()));
                            if(done){
                                if(statusList.size() == (exec.getTotal() / exec.getStep() + ((exec.getTotal() % exec.getStep()) > 0 ? 1 : 0))){
                                    exec.setStatus(ArrangementNodeExec.Status.SUCCESS);
                                }
                            }
                        }finally {
                            lock.unlock();
                        }
                        if(ArrangementNodeExec.Status.SUCCESS.equals(exec.getStatus())){
                            try{
                                val user = new SecurityUserInfo();
                                user.setTenantId(exec.getActualTenantId());
                                SecurityUserUtil.setSecurityUserInfo(user);
                                exec.setEnded(System.currentTimeMillis());
                                exec.update();
                                val nextList = panguNodeFeign.fetchNextNodes(exec.getArrangementVid(), exec.getArrangementNodeId()).getData();
                                val existedTask = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(exec.getExecSeqId(), nextList);
                                nextList.forEach(next->{
                                    val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                                    if(matched.isPresent()){
                                        ArrangementNodeExec nodeExec = matched.get();
                                        nodeExec.setSkip(false);
                                        nodeExec.update();
                                    }else{
                                        arrangementExecService.initNext(exec, next, false, ArrangementNodeExec.Status.CHECK);
                                    }
                                });
                            }finally {
                                SecurityUserUtil.removeSecurityUserInfo();
                            }
                        }
                    });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock0.unlock();
        }

    }

    public boolean report(CalcReport.Fail report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatusList(report.getExecNodeSeqId(),
                            Lists.list(ArrangementNodeExec.Status.STARTED,
                                    ArrangementNodeExec.Status.DISPATCHED,
                                    ArrangementNodeExec.Status.LOADED))
                    .ifPresent(it -> {
                        if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                            return;
                        }
                        it.setStatus(ArrangementNodeExec.Status.ERROR);
                        if(StringUtils.isEmpty(it.getDetail())){
                            it.setDetail(report.getDetail());
                        }
                        it.setEnded(System.currentTimeMillis());
                        it.update();
                        ArrangementExec.loadByExecSeqIdAndStatus(it.getExecSeqId(), ArrangementExec.Status.STARTED)
                                .ifPresent(arrangementExec -> {
                                    arrangementExec.setEnded(System.currentTimeMillis());
                                    arrangementExec.setStatus(ArrangementExec.Status.ERROR);
                                    arrangementExec.update();
                                    resultNoticeService.failed(arrangementExec);
                                    destroyCache(it.getExecSeqId());
                                });
                    });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock.unlock();
        }
    }

    public boolean report(CalcReport.PreLoad report) {
        val lock = locker.getLock("execSeq_"+report.getExecSeqId());
        lock.lock();
        try{
            ArrangementNodeExec
                    .loadByExecNodeSeqIdAndStatus(report.getExecNodeSeqId(), ArrangementNodeExec.Status.STARTED).ifPresent(it -> {
                if (!StringUtils.equals(it.getPanguNodeId(), report.getPanguNodeId())) {
                    return;
                }
                it.setStatus(ArrangementNodeExec.Status.SUCCESS);
                it.setEnded(System.currentTimeMillis());
                it.update();
                try{
                    val user = new SecurityUserInfo();
                    user.setTenantId(it.getActualTenantId());
                    SecurityUserUtil.setSecurityUserInfo(user);
                    val nextList = panguNodeFeign.fetchNextNodes(it.getArrangementVid(), it.getArrangementNodeId()).getData();
                    val existedTask = ArrangementNodeExec.listByExecSeqIdAndArrangementNodeIdList(it.getExecSeqId(), nextList);
                    nextList.forEach(next->{
                        val matched = existedTask.stream().filter(existed->existed.getArrangementNodeId().equals(next)).findAny();
                        if(matched.isPresent()){
                            ArrangementNodeExec nodeExec = matched.get();
                            nodeExec.setSkip(false);
                            nodeExec.update();
                        }else{
                            arrangementExecService.initNext(it, next, false, ArrangementNodeExec.Status.CHECK);
                        }
                    });
                }finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            });
            nodeManagerService.rtn(report.getPanguNodeId());
            return true;
        }finally {
            lock.unlock();
        }
    }
}

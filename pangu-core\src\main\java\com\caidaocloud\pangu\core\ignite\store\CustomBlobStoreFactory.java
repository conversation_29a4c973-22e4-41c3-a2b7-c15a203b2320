package com.caidaocloud.pangu.core.ignite.store;

import java.io.Serializable;

import com.caidaocloud.pangu.core.ignite.DbContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStore;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.configuration.CacheConfiguration;

/**
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Slf4j
public class CustomBlobStoreFactory<K,V> extends CacheJdbcBlobStoreFactory<K,V>  implements CustomStoreFactory, Serializable {
	private static final long serialVersionUID = 0;

	private DynamicPojoDefinition pojoDef;
	private DbContext dbContext;
	private CacheConfiguration cacheConfiguration;

	@Override
	public CacheJdbcBlobStore<K, V> create() {
		log.info("Initializing cache jdbc blob store,dbContext:{}", dbContext);
		CacheJdbcBlobStore<K, V> store = super.create();
		store.setConnectionUrl(dbContext.getConnUrl());
		store.setUser(dbContext.getUser());
		store.setPassword(dbContext.getPwd());


		String cacheName = cacheConfiguration.getName();
		store.setLoadQuery(dbContext.generateLoadSql(cacheName));
		store.setDeleteQuery(dbContext.generateDeleteSql(cacheName));
		store.setInsertQuery(dbContext.generateInsertSql(cacheName));
		store.setUpdateQuery(dbContext.generateUpdateSql(cacheName));
		store.setCreateTableQuery(dbContext.generateCreateTableSql(cacheName));
		return store;
	}
}

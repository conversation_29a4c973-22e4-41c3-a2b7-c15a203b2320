package com.caidaocloud.pangu.domain.repository;

import java.util.Map;

import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;

/**
 *
 * <AUTHOR>
 * @date 2024/2/2
 */
public interface IFormRepository {
	FormDefDto getFormDefById(String formId);

	String saveFormData(String formId, Map<String, Object> formData);

	void updateFormData(String formId, String formDataId, Map<String, Object> formData);

	Map<String, Object> getFormDataById(String formId, String formDataId);
}

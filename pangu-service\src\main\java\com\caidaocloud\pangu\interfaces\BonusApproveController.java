package com.caidaocloud.pangu.interfaces;


import com.caidaocloud.pangu.application.dto.BonusApproveDto;
import com.caidaocloud.pangu.application.dto.BonusApproveSummaryDto;
import com.caidaocloud.pangu.application.service.BonusApproveService;
import com.caidaocloud.pangu.application.enums.WfTaskActionEnum;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2024/2/1
 */
@RestController
@RequestMapping("/api/pangu/v1/bonus/approve")
@Api(value = "/api/pangu/v1/bonus/approve", tags = "奖金账套审批结果")
public class BonusApproveController {
	@Autowired
	private BonusApproveService bonusApproveService;

	@GetMapping("summary")
	public Result<BonusApproveSummaryDto> getSummary(@RequestParam("ledgerId") String ledgerId) {
		return Result.ok(bonusApproveService.getSummary(ledgerId));
	}

	@PostMapping("start")
	public Result start(@RequestBody BonusApproveDto dto) {
		bonusApproveService.start(dto);
		return Result.ok();
	}

	@GetMapping("detail")
	public Result<BonusApproveSummaryDto> getSummaryDetail(String businessKey) {
		return Result.ok(bonusApproveService.getSummaryByBusinessKey(businessKey));
	}

	@PostMapping("approve")
	public Result approve(@RequestBody BonusApproveDto dto) {
		bonusApproveService.approve(dto, WfTaskActionEnum.APPROVE);
		return Result.ok();
	}

	@PostMapping("refuse")
	public Result refuse(@RequestBody BonusApproveDto dto) {
		bonusApproveService.approve(dto, WfTaskActionEnum.REFUSE);
		return Result.ok();
	}
}

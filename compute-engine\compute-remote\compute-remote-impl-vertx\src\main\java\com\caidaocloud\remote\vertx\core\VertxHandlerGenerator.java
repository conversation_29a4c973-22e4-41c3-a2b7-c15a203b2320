package com.caidaocloud.remote.vertx.core;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.compute.remote.framework.actor.RemoteRequest;
import com.caidaocloud.compute.remote.framework.core.AbsHandlerGenerator;
import com.caidaocloud.compute.remote.framework.core.AbsInvokeHandler;
import com.caidaocloud.compute.remote.framework.core.RemoteInitializer;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;
import com.caidaocloud.compute.remote.framework.serialize.SerializerUtils;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.vertx.core.Future;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.buffer.impl.BufferImpl;
import io.vertx.core.http.HttpResponseExpectation;
import io.vertx.core.http.HttpServer;
import io.vertx.ext.web.client.HttpResponse;
import io.vertx.ext.web.client.WebClient;
import io.vertx.ext.web.codec.BodyCodec;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/12/30
 */
@Slf4j
public class VertxHandlerGenerator extends AbsHandlerGenerator {

	public VertxHandlerGenerator(RemoteInitializer initializer) {
		super(initializer);
	}

	@Override
	public <T> T createProxy(Class<T> serviceInterface, Map<String, MethodMetadata> methodMetadataMap) {
		RemoteRequest request = serviceInterface.getAnnotation(RemoteRequest.class);
		String path = request.value();
		return (T) Proxy.newProxyInstance(
				serviceInterface.getClassLoader(),
				new Class<?>[] {serviceInterface},
				new VertxInvokeHandler(path, methodMetadataMap, (VertxInitializer) getInitializer()));

	}

	private class VertxInvokeHandler extends AbsInvokeHandler {
		private WebClient client;

		public VertxInvokeHandler(String path, Map<String, MethodMetadata> methodMetadataMap, VertxInitializer initializer) {
			super(path,methodMetadataMap);
			this.client = initializer.getHttpClient();
		}


		@Override
		protected Object doInvoke(String path, Method method, Object[] args) {
			String requestPath = String.format("/%s/%s", path, method.getName());
			String methodName = MethodMetadata.parseMethodName(method);
			MethodMetadata methodMetadata = getMethodMetadataMap().get(methodName);
			Address address = ((Address) args[methodMetadata.getAddressIndex()]);
			Object[] realArgs = removeAddressIndex(args, methodMetadata.getAddressIndex());
			ActorMessageWrapper request = new ActorMessageWrapper(method.getName()).setArgs(realArgs)
					.setUserInfo(SecurityUserUtil.getSecurityUserInfo());
			Future<? extends HttpResponse<?>> future = client.post(address.getPort(), address.getHost(), requestPath)
					.sendBuffer(Buffer.buffer(SerializerUtils.serialize(request)))
					.expecting(HttpResponseExpectation.SC_SUCCESS);
			try {
				Object body = future.toCompletionStage().toCompletableFuture().get(5000, TimeUnit.MILLISECONDS).body();
				return FastjsonUtil.toObject(body.toString(), method.getReturnType());
			}
			catch (Exception e) {
				log.error("[Vertx] async remote request occurs error,{}", e.getMessage());
				throw new ServerException("[Vertx] async remote request occurs error", e);
			}
		}

	}
}

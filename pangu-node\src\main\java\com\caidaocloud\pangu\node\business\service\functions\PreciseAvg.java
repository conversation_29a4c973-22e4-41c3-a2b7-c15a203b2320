package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.pangu.node.business.tool.ExpressionTool;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class PreciseAvg extends AbstractVariadicFunction {
    @Override
    public String getName() {
        return "AVG_P";
    }

    @Override
    public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... aviatorObjects) {
        List<AviatorObject> params = Lists.list(aviatorObjects);
        val precision = FunctionUtils.getNumberValue(params.stream().reduce((a, b)->b).get(), env).intValue();
        params.remove(params.size() - 1);
        val roundingMode = RoundingMode.valueOf(FunctionUtils.getStringValue(params.stream().reduce((a, b)->b).get(), env));
        params.remove(params.size() - 1);

        BigDecimal result = new BigDecimal(0);
        int count = 0;
        val log = new StringBuilder(getName()).append("(");
        for(AviatorObject aviatorObject : params){
            val decimal = new BigDecimal(FunctionUtils.getNumberValue(aviatorObject, env).toString());
            log.append(decimal).append(",");
            result = result.add(decimal);
            count++;
        }
        result = result.divide(new BigDecimal(count), precision, roundingMode);
        if(FunctionLogTool.logEnabled()){
            log.append(precision).append(",").append(roundingMode).append(")=").append(result);
            FunctionLogTool.log(log.toString());
        }
        return new AviatorDecimal(result);
    }

}

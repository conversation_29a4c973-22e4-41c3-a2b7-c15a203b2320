package pangu;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

import javax.cache.Cache;
import javax.cache.configuration.Factory;
import javax.sql.DataSource;

import com.caidaocloud.pangu.manager.PanguManagerApplication;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.ignite.Ignite;
import org.apache.ignite.IgniteCache;
import org.apache.ignite.cache.store.jdbc.CacheJdbcBlobStoreFactory;
import org.apache.ignite.cache.store.jdbc.CacheJdbcPojoStoreFactory;
import org.apache.ignite.cache.store.jdbc.JdbcType;
import org.apache.ignite.cache.store.jdbc.JdbcTypeField;
import org.apache.ignite.cache.store.jdbc.dialect.BasicJdbcDialect;
import org.apache.ignite.configuration.CacheConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/7/20
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanguManagerApplication.class)
@Slf4j
public class PgsqlPojoDbTest {

	@Autowired
	private Ignite ignite;

	static CacheConfiguration empCacheCfg = new CacheConfiguration();
	static String cache_name = "empCacheWithPgsqlv3";
	static String table_name = "employees";
	static {
		empCacheCfg.setName(cache_name);
		CacheJdbcPojoStoreFactory<Integer, EmployeeDbVer> factory = new CacheJdbcPojoStoreFactory<>();
		factory.setDialect(new BasicJdbcDialect());
		factory.setDataSourceBean("pgDataSource");
		JdbcType employeeType = getJdbcType();
		factory.setTypes(employeeType);
		empCacheCfg.setCacheStoreFactory(factory);
		empCacheCfg.setReadThrough(true);
	}


	private static JdbcType getJdbcType() {
		JdbcType employeeType = new JdbcType();
		employeeType.setCacheName(cache_name);
		employeeType.setDatabaseTable(table_name);
		employeeType.setKeyType(Integer.class);
		employeeType.setKeyFields(new JdbcTypeField(Types.INTEGER, "id", Integer.class, "id"));
		employeeType.setValueFields(
				new JdbcTypeField(Types.INTEGER, "id", Integer.class, "id"),
				new JdbcTypeField(Types.VARCHAR, "name", String.class, "name"),
				new JdbcTypeField(Types.VARCHAR, "email", String.class, "email"),
				new JdbcTypeField(Types.INTEGER, "salary", Integer.class, "salary"),
				new JdbcTypeField(Types.VARCHAR, "addr", String.class, "addr")
		);

		employeeType.setValueType(EmployeeDbVer.class);

		return employeeType;
	}

	@Test
	public void readThroughTest(){

		IgniteCache<Integer, EmployeeDbVer> cache = ignite.getOrCreateCache(empCacheCfg);
		log.info("get cache succeed");
		int cnt = 0;
		Map<Integer, EmployeeDbVer> map = new HashMap<>();
		for (Cache.Entry<Integer, EmployeeDbVer> integerIntegerEntry : cache) {
			map.put(integerIntegerEntry.getKey(), integerIntegerEntry.getValue());
			log.info("employee >>>>>>>>." + integerIntegerEntry.getValue());
		}
		// 需要手动向数据库插入数据
		log.info("fetch a employee from cache by read through,{}", cache.get(4));
		Assert.assertTrue("read through failed", !map.containsKey(4));
	}

	@Test
	public void cacheTest(){
		IgniteCache<Integer, EmployeeDbVer> cache = ignite.getOrCreateCache(empCacheCfg);
		log.info("get cache succeed");

		EmployeeDbVer dbVer = new EmployeeDbVer(6L, "emp06", "<EMAIL>", 1000L, new Address("1545 Jackson Street", 94612), Lists.list("dept01"));
		cache.put(dbVer.getEmpId().intValue(), dbVer);
		log.info("saved data,{}", cache.get(6));
	}
}

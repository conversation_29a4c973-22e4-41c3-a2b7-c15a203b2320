package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.googlecode.totallylazy.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

public class GetEmpId extends AbstractFunction {
    @Override
    public String getName() {
        return "GET_EMP_ID";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject aviatorObject) {
        String workNo = FunctionUtils.getStringValue(aviatorObject, env);
        val empId = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .filterProperties(
                        DataFilter.eq("workno", workNo).andNe("deleted", "true"),
                        Lists.list("empId"), System.currentTimeMillis())
                .getItems().stream().findFirst().map(it-> StringUtils.trimToEmpty(it.get("empId"))).orElse("");
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(workNo).append(")=").append(empId).toString());
        }
        return new AviatorString(empId);
    }
}

package com.caidaocloud.pangu.interfaces;

import com.caidaocloud.pangu.application.dto.BonusLedgerDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerExecDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerPageDto;
import com.caidaocloud.pangu.application.dto.BonusAccountEmpDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpDeleteDto;
import com.caidaocloud.pangu.application.dto.BonusLedgerEmpPageDto;
import com.caidaocloud.pangu.application.service.BonusLedgerService;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerComposeVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerVo;
import com.caidaocloud.pangu.interfaces.vo.BonusLedgerEmpVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.web.Result;
import com.caidaocloud.pangu.application.constant.BonusConstant;
import com.caidaocloud.lock.Locker;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.Lock;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringRunner.class)
@WebMvcTest(BonusLedgerController.class)
public class BonusLedgerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BonusLedgerService bonusLedgerService;

    @MockBean
    private Locker lock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreate() throws Exception {
        BonusLedgerDto schemeDto = new BonusLedgerDto();
        when(bonusLedgerService.create(schemeDto)).thenReturn("Success");

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for schemeDto
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value("Success"));
    }

    @Test
    public void testPage() throws Exception {
        BonusLedgerPageDto dto = new BonusLedgerPageDto();
        PageResult<BonusLedgerVo> pageResult = new PageResult<>();
        when(bonusLedgerService.page(dto)).thenReturn(pageResult);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for dto
                .andExpect(status().isOk());
    }

    @Test
    public void testUpdate() throws Exception {
        BonusLedgerDto schemeDto = new BonusLedgerDto();

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for schemeDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).update(schemeDto);
    }

    @Test
    public void testDelete() throws Exception {
        String bid = "testBid";

        mockMvc.perform(delete("/api/pangu/v1/bonus/ledger/delete")
                .param("bid", bid))
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).delete(bid);
    }

    @Test
    public void testClose() throws Exception {
        BonusLedgerDto schemeDto = new BonusLedgerDto();

        mockMvc.perform(put("/api/pangu/v1/bonus/ledger/close")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for schemeDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).close(schemeDto.getBid());
    }

    @Test
    public void testLoadEmp() throws Exception {
        BonusLedgerEmpPageDto dto = new BonusLedgerEmpPageDto();
        PageResult<BonusLedgerEmpVo> pageResult = new PageResult<>();
        when(bonusLedgerService.loadLedgerEmpPage(dto)).thenReturn(pageResult);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for dto
                .andExpect(status().isOk());
    }

    @Test
    public void testSyncEmp() throws Exception {
        BonusAccountEmpDto bonusAccountSyncEmpDto = new BonusAccountEmpDto();
        bonusAccountSyncEmpDto.setBid("testBid");

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp/sync")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"bid\":\"testBid\"}")) // Add JSON content for bonusAccountSyncEmpDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).syncEmp("testBid");
    }

    @Test
    public void testAddEmp() throws Exception {
        BonusAccountEmpDto bonusAccountSyncEmpDto = new BonusAccountEmpDto();

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/emp")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}")) // Add JSON content for bonusAccountSyncEmpDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).addEmp(bonusAccountSyncEmpDto);
    }

    @Test
    public void testRemoveEmp() throws Exception {
        BonusLedgerEmpDeleteDto dto = new BonusLedgerEmpDeleteDto();
        dto.setBid("testBid");
        dto.setEmpIds(Collections.singletonList("empId"));

        mockMvc.perform(delete("/api/pangu/v1/bonus/ledger/emp")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"bid\":\"testBid\", \"empIds\":[\"empId\"]}")) // Add JSON content for dto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).removeEmp("testBid", Collections.singletonList("empId"));
    }

    @Test
    public void testLoadComposeList() throws Exception {
        String bid = "testBid";
        List<BonusLedgerComposeVo> composeList = Collections.emptyList();
        when(bonusLedgerService.loadComposeList(bid)).thenReturn(composeList);

        mockMvc.perform(get("/api/pangu/v1/bonus/ledger/compose")
                .param("bid", bid))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    public void testExec() throws Exception {
        BonusLedgerExecDto execDto = new BonusLedgerExecDto();
        execDto.setLedgerId("testLedgerId");

        Lock mockLock = mock(Lock.class);
        when(lock.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId())).thenReturn(mockLock);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/compose/exec")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"ledgerId\":\"testLedgerId\"}")) // Add JSON content for execDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).exec(execDto);
        verify(mockLock, times(1)).lock();
        verify(mockLock, times(1)).unlock();
    }

    @Test
    public void testExecAll() throws Exception {
        BonusLedgerExecDto execDto = new BonusLedgerExecDto();
        execDto.setLedgerId("testLedgerId");

        Lock mockLock = mock(Lock.class);
        when(lock.getLock(BonusConstant.BONUS_EXEC_KEY + execDto.getLedgerId())).thenReturn(mockLock);

        mockMvc.perform(post("/api/pangu/v1/bonus/ledger/compose/exec/all")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"ledgerId\":\"testLedgerId\"}")) // Add JSON content for execDto
                .andExpect(status().isOk());

        verify(bonusLedgerService, times(1)).execAll(execDto);
        verify(mockLock, times(1)).lock();
        verify(mockLock, times(1)).unlock();
    }
} 
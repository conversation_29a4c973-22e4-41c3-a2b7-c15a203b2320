package com.caidaocloud.remote.akka.core;

import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import com.caidaocloud.compute.remote.framework.actor.Address;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;
import com.caidaocloud.compute.remote.framework.actor.ActorMessageWrapper;
import com.caidaocloud.remote.akka.constant.AkkaConstant;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@AllArgsConstructor
public abstract class AkkaMethodHandler {
	private ActorSystem actorSystem;
	private MethodMetadata methodMetadata;

	abstract Object doExecute(ActorSelection actorSelection, ActorMessageWrapper request);

	public Object execute(Address address, String path,Object[] args) {
		ActorSelection actorSelection = actorSystem
				.actorSelection(String.format(AkkaConstant.AKKA_REMOTE_PATH, actorSystem.name(), address.toString(), path));
		ActorMessageWrapper request = new ActorMessageWrapper(methodMetadata.getMethod().getName()).setArgs(args);
		return doExecute(actorSelection, request);
	}
}

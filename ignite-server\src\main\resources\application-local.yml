server:
 port: 8081


ignite:
 metricsLogFrequency: 0
 peerClassLoadingEnabled: true
 deploymentMode: CONTINUOUS
 #  igniteInstanceName: bonus-calc
 workDirectory: C:\ignite

spring:
 cloud:
  service-registry:
   auto-registration:
    enabled: false
 application:
  name: caidaocloud-ignite-server

nacos:
 config:
  type: yaml
  server-addr: 192.168.120.202:8848
  data-id: caidaocloud-ignite-server-config
  auto-refresh: true
  group: DEFAULT_GROUP
  namespace: cd2pg
  bootstrap:
   enable: true
   log:
    enable: true




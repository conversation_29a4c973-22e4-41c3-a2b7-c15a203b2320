package com.caidaocloud.pangu.node.business.service.functions;

import com.caidaocloud.pangu.node.business.service.functions.extend.AviatorTimestamp;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import lombok.val;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class Before extends AbstractFunction {
    @Override
    public String getName() {
        return "BEFORE";
    }

    @Override
    public AviatorObject call(Map<String, Object> env,AviatorObject aviatorObject1, AviatorObject aviatorObject2) {
        val first = AviatorTimestamp.transformAviatorValue(aviatorObject1, env);
        val second = AviatorTimestamp.transformAviatorValue(aviatorObject2, env);
        val before = first.isBefore(second);
        if(FunctionLogTool.logEnabled()){
            FunctionLogTool.log(new StringBuilder(getName()).append("(").append(first).append(",").append(second).append(")=").append(before).toString());
        }
        return FunctionUtils.wrapReturn(before);
    }
}

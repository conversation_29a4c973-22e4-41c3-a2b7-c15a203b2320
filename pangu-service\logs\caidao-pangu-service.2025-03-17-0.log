2025-03-17 15:47:52.763 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-17 15:47:52.865 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-17 15:47:53.397 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-17 15:47:53.451 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@da8bd7ac, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@53d15e17, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@9fe928f7, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@daa72191, org.springframework.test.context.support.DirtiesContextTestExecutionListener@879473dc, org.springframework.test.context.transaction.TransactionalTestExecutionListener@f61f79d, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5ebc958a, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@22881129, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@c4d5ce0f, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@79d16228, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@8dafba39, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@7b7eb7cb]
2025-03-17 15:47:55.356 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-17 15:47:55.361 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-17 15:47:56.554 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f21c377b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:47:58.019 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-17 15:47:58.810 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-17 15:47:58.846 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-17 15:48:04.322 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-17 15:48:05.007 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-17 15:48:05.014 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-17 15:48:05.172 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 65ms. Found 0 repository interfaces.
2025-03-17 15:48:05.330 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-17 15:48:05.361 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-17 15:48:05.868 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-17 15:48:06.466 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$3dc84eaa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:06.518 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:06.542 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$eb1bf2c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:06.954 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.175 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.185 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.210 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.211 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$84062d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.250 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$9c6df2ac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:07.501 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$b432e29] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:08.113 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$11464266] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:08.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:09.993 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-17 15:48:10.009 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-17 15:48:12.031 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.583 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.735 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.802 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:12.809 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:13.028 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:13.033 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:13.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f21c377b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:48:15.352 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-17 15:48:15.352 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-17 15:48:15.386 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@fe51d4f2
2025-03-17 15:48:17.016 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-17 15:48:17.427 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#e03d1f2:0/SimpleConnection@1c100a97 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 52508]
2025-03-17 15:48:27.289 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-17 15:48:27.642 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-17 15:48:27.774 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-17 15:48:30.716 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-17 15:48:30.720 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-17 15:49:01.947 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-17 15:49:01.961 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-17 15:49:02.364 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-17 15:49:02.407 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6c4b0220, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@81936ffa, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@d6e1b042, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@fa746652, org.springframework.test.context.support.DirtiesContextTestExecutionListener@27f5883, org.springframework.test.context.transaction.TransactionalTestExecutionListener@971cdfd4, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@c873d805, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@3e0deb6d, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@89fee8a4, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@e1ffcae9, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@5805eae2, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@63e677db]
2025-03-17 15:49:03.631 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-17 15:49:03.635 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-17 15:49:04.768 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b7c8e744] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:05.680 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-17 15:49:06.340 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-17 15:49:06.373 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-17 15:49:10.842 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-17 15:49:11.599 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-17 15:49:11.604 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-17 15:49:11.688 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 64ms. Found 0 repository interfaces.
2025-03-17 15:49:11.799 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-17 15:49:11.825 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-17 15:49:12.258 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-17 15:49:12.671 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$374fe73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:12.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:12.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$b0c8a291] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:12.959 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.112 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.123 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.140 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.141 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$49b2dcd5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.167 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$621aa275] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.345 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$d0efddf2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.570 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$d6f2f22f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.597 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:13.967 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-17 15:49:13.972 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-17 15:49:14.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.493 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.529 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.551 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.562 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.563 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.569 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.571 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:14.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b7c8e744] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:49:15.190 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-17 15:49:15.190 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-17 15:49:15.217 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@206d595a
2025-03-17 15:49:15.959 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-17 15:49:16.062 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#6c5d885b:0/SimpleConnection@78cfa63 [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 52685]
2025-03-17 15:49:19.166 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-17 15:49:19.380 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-17 15:49:19.563 [redisson-netty-5-18] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-17 15:49:21.668 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-17 15:49:22.529 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-17 15:49:22.545 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-17 15:49:22.639 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-17 15:49:22.929 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-17 15:49:22.929 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-17 15:49:23.797 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-17 15:49:26.566 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-17 15:49:26.617 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-17 15:49:26.622 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-17 15:49:26.656 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-17 15:49:26.770 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-17 15:49:27.363 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-17 15:49:27.415 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-17 15:49:27.452 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-17 15:49:27.456 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-17 15:49:27.486 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-17 15:49:27.491 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-17 15:49:27.513 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-17 15:49:27.520 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-17 15:49:27.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-17 15:49:27.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-17 15:49:27.816 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-17 15:49:27.879 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 25.358 seconds (JVM running for 30.495)
2025-03-17 15:49:28.056 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-17 15:49:28.057 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-17 15:49:28.057 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-17 15:49:28.568 [main] INFO  org.reflections.Reflections - Reflections took 187 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-17 15:49:28.846 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:49:28.953 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-17 15:49:28.966 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-17 15:49:29.085 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:49:29.088 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@3149b42
2025-03-17 15:49:29.980 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:49:30.440 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:49:30.447 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-17 15:49:30.449 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-17 15:49:30.476 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:49:30.477 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[***************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@8275dac5
2025-03-17 15:50:29.113 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:51:34.665 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-17 15:51:34.667 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-17 15:51:34.666 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-17 15:51:34.671 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-17 15:51:34.679 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-17 15:51:35.259 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-17 15:51:35.724 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-17 15:51:35.744 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-17 15:51:36.638 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-17 15:51:36.744 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-17 15:51:36.770 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-17 15:51:36.772 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-17 15:51:36.783 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-17 15:51:36.787 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-17 15:51:36.802 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-17 15:51:36.819 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-17 15:51:36.826 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-17 15:51:37.164 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-17 15:51:37.165 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-17 15:51:37.246 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-17 15:51:37.635 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@a862dae8: tags=[[amq.ctag-2T31whZhtQ8elQgGEqAXnA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@6f81ccbe Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-17 15:51:37.650 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@b396d76: tags=[[amq.ctag-oJnAep2o4UH7bBhC25N9BA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@6f81ccbe Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-17 15:51:38.598 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-17 15:51:38.600 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-17 15:51:38.601 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-03-17 15:51:49.039 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.caidaocloud.pangu.BonusLedgerControllerTest], using SpringBootContextLoader
2025-03-17 15:51:49.049 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.caidaocloud.pangu.BonusLedgerControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-03-17 15:51:49.553 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
2025-03-17 15:51:49.590 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@5a4a417c, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@977907a9, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@dcefb755, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4a185c6c, org.springframework.test.context.support.DirtiesContextTestExecutionListener@e2949848, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1aea5bbe, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@20aab41f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@853e3329, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@10ca5653, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@9100e240, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@f16fc4ae, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@74fec334]
2025-03-17 15:51:50.572 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-03-17 15:51:50.578 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-03-17 15:51:51.447 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$41cd6635] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:52.144 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='***************:8848', contextPath='null', encode='null', endpoint='null', namespace='cd2pg', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=true, dataId='caidaocloud-pangu-engine-config', dataIds='null', group='CORE_HR_GROUP', type=YAML, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=true, logEnable=false}}
2025-03-17 15:51:52.646 [main] INFO  c.a.b.n.c.util.NacosConfigLoader - load config from nacos, data-id is : caidaocloud-pangu-engine-config, group is : CORE_HR_GROUP
2025-03-17 15:51:52.658 [main] INFO  c.c.pangu.BonusLedgerControllerTest - The following profiles are active: local
2025-03-17 15:51:54.714 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-03-17 15:51:55.172 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-03-17 15:51:55.177 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-03-17 15:51:55.250 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52ms. Found 0 repository interfaces.
2025-03-17 15:51:55.358 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-03-17 15:51:55.384 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-03-17 15:51:55.801 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c19089b-c2bc-3e0f-a98f-638529057194
2025-03-17 15:51:56.253 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$8d797d64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.301 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.321 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$3acd2182] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.669 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.861 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.869 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.890 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.891 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$d3b75bc6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:56.916 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$ec1f2166] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:57.060 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$5af45ce3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:57.284 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$60f77120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:57.310 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:57.656 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-03-17 15:51:57.661 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-03-17 15:51:58.154 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.213 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.230 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.235 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.241 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.246 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.248 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:58.296 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$41cd6635] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-03-17 15:51:59.011 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-17 15:51:59.011 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-17 15:51:59.041 [main] INFO  c.n.config.DynamicPropertyFactory - DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c67016f7
2025-03-17 15:51:59.720 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Attempting to connect to: [***************:5672]
2025-03-17 15:51:59.817 [main] INFO  o.s.a.r.c.CachingConnectionFactory - Created new connection: rabbitConnectionFactory#15eb5e53:0/SimpleConnection@ba9e6b4b [delegate=amqp://admin@***************:5672//caidaocloud2, localPort= 52998]
2025-03-17 15:52:02.547 [main] INFO  org.redisson.Version - Redisson 3.17.4
2025-03-17 15:52:02.782 [redisson-netty-5-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for ***************/***************:6379
2025-03-17 15:52:02.963 [redisson-netty-5-19] INFO  o.r.c.pool.MasterConnectionPool - 24 connections initialized for ***************/***************:6379
2025-03-17 15:52:04.995 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-03-17 15:52:05.741 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-03-17 15:52:05.757 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2025-03-17 15:52:05.841 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2025-03-17 15:52:06.082 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-03-17 15:52:06.082 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-03-17 15:52:07.009 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-03-17 15:52:09.074 [main] WARN  c.x.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-03-17 15:52:09.113 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-03-17 15:52:09.118 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2025-03-17 15:52:09.149 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-03-17 15:52:09.246 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-03-17 15:52:09.775 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2025-03-17 15:52:09.824 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-03-17 15:52:09.866 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-03-17 15:52:09.869 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-03-17 15:52:09.896 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-03-17 15:52:09.901 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-03-17 15:52:09.925 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: listUsingGET_1
2025-03-17 15:52:09.932 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: loadUsingGET_1
2025-03-17 15:52:09.954 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_2
2025-03-17 15:52:10.077 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_2
2025-03-17 15:52:10.252 [main] INFO  c.c.m.c.RabbitMqConsumerManager - Spring context refreshed,consumer start up
2025-03-17 15:52:10.315 [main] INFO  c.c.pangu.BonusLedgerControllerTest - Started BonusLedgerControllerTest in 20.616 seconds (JVM running for 23.197)
2025-03-17 15:52:10.461 [main] INFO  o.s.mock.web.MockServletContext - Initializing Spring TestDispatcherServlet ''
2025-03-17 15:52:10.461 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Initializing Servlet ''
2025-03-17 15:52:10.461 [main] INFO  o.s.t.w.s.TestDispatcherServlet - Completed initialization in 0 ms
2025-03-17 15:52:10.675 [main] INFO  org.reflections.Reflections - Reflections took 87 ms to scan 1 urls, producing 9 keys and 30 values 
2025-03-17 15:52:10.885 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:52:10.967 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-hr-paas-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-17 15:52:10.977 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-17 15:52:11.098 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:52:11.100 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-hr-paas-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-hr-paas-service,current list of Servers=[***************:10012],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:10012;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@ce5169ef
2025-03-17 15:52:11.993 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-hr-paas-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:52:12.231 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:52:12.238 [main] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: caidaocloud-masterdata-service instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-03-17 15:52:12.240 [main] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-03-17 15:52:12.268 [main] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:52:12.270 [main] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client caidaocloud-masterdata-service initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=caidaocloud-masterdata-service,current list of Servers=[***************:20008],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:***************:20008;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7e783ec
2025-03-17 15:52:14.377 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: caidaocloud-masterdata-service.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-03-17 15:53:17.316 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-03-17 15:53:17.317 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-03-17 15:53:17.316 [Thread-47] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-03-17 15:53:17.318 [Thread-38] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-03-17 15:53:17.320 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-03-17 15:53:17.400 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-17 15:53:17.515 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-17 15:53:17.554 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Waiting for workers to finish.
2025-03-17 15:53:18.542 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Successfully waited for workers to finish.
2025-03-17 15:53:18.727 [Thread-43] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server stop.
2025-03-17 15:53:18.735 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='caidaocloud-pangu-engine-local', registryValue='http://192.168.130.41:9999/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-03-17 15:53:18.736 [xxl-job, executor ExecutorRegistryThread] INFO  c.x.j.c.t.ExecutorRegistryThread - >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-03-17 15:53:18.736 [Thread-45] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server destroy success.
2025-03-17 15:53:18.736 [xxl-job, executor JobLogFileCleanThread] INFO  c.x.j.c.thread.JobLogFileCleanThread - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-03-17 15:53:18.737 [xxl-job, executor TriggerCallbackThread] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-03-17 15:53:18.737 [Thread-42] INFO  c.x.j.c.thread.TriggerCallbackThread - >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-03-17 15:53:18.738 [Thread-45] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2025-03-17 15:53:18.905 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-17 15:53:18.905 [Thread-45] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Shutdown ignored - container is not active already
2025-03-17 15:53:18.957 [Thread-45] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-03-17 15:53:19.510 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@c7e52aa3: tags=[[amq.ctag-kcX3XcSrRef-1tvhkKYqwQ]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,3), conn: Proxy@e6a280b4 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-17 15:53:19.569 [SimpleAsyncTaskExecutor-1] INFO  o.s.a.r.l.SimpleMessageListenerContainer - Restarting Consumer@badf89f7: tags=[[amq.ctag-bAe8s8yUdidPMaHU_B5BBA]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@***************:5672//caidaocloud2,4), conn: Proxy@e6a280b4 Shared Rabbit Connection: null, acknowledgeMode=MANUAL local queue size=0
2025-03-17 15:53:20.129 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-03-17 15:53:20.130 [Thread-45] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-03-17 15:53:20.131 [Thread-45] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!

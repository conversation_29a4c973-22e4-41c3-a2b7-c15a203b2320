package com.caidaocloud.pangu.core.ignite.cache;

import java.io.Serializable;
import java.lang.reflect.Field;

import javax.cache.configuration.Factory;

import com.caidaocloud.pangu.core.ignite.DatasourceProperties;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoContext;
import com.caidaocloud.pangu.core.ignite.dynamic.DynamicPojoDefinition;
import lombok.Data;
import lombok.SneakyThrows;
import org.apache.ignite.configuration.CacheConfiguration;

/**
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
public class DynamicPojoCacheCfgFactory<K, V> implements Factory<CacheConfiguration<K, V>> {
	private String cacheName;
	private String identifier;
	private DynamicPojoDefinition dynamicPojoDefinition;
	private DatasourceProperties datasourceProperties;


	public DynamicPojoCacheCfgFactory(String identifier, DatasourceProperties datasourceProperties) {
		this.identifier = identifier;
		this.datasourceProperties = datasourceProperties;
	}


	@SneakyThrows
	@Override
	public CacheConfiguration<K, V> create() {
		Class<?> cacheValue = DynamicPojoContext.loadClass(dynamicPojoDefinition);
		Field key = cacheValue.getDeclaredField(dynamicPojoDefinition.getKeyField().getName());
		CacheConfiguration configuration = new CacheConfigurationBuilder<>(cacheValue).identifier(identifier).key(key)
				.datasourceProperties(datasourceProperties)
				.dynamicPojoDefinition(dynamicPojoDefinition)
				.cacheName(cacheName)
				.build();
		configuration.setReadThrough(true);
		return configuration;
	}

	public DynamicPojoCacheCfgFactory<K, V> setDynamicPojoDefinition(DynamicPojoDefinition dynamicPojoDefinition) {
		this.dynamicPojoDefinition = dynamicPojoDefinition;
		return this;
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>caidao-pangu-service</artifactId>
		<groupId>com.caidaocloud</groupId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>pangu-service</artifactId>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
	</properties>
	<dependencies>
<!--		<dependency>-->
<!--			<groupId>org.apache.ignite</groupId>-->
<!--			<artifactId>ignite-slf4j</artifactId>-->
<!--			<version>${ignite.version}</version>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.ignite</groupId>-->
<!--			<artifactId>ignite-core</artifactId>-->
<!--			<version>${ignite.version}</version>-->
<!--			<scope>test</scope>-->

<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.ignite</groupId>-->
<!--			<artifactId>ignite-spring</artifactId>-->
<!--			<version>${ignite.version}</version>-->
<!--			<scope>test</scope>-->

<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.ignite</groupId>-->
<!--			<artifactId>ignite-spring-boot-autoconfigure-ext</artifactId>-->
<!--			<version>1.0.0</version>-->
<!--			<scope>test</scope>-->

<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.ignite</groupId>-->
<!--			<artifactId>ignite-indexing</artifactId>-->
<!--			<version>${ignite.version}</version>-->
<!--			<scope>test</scope>-->

<!--		</dependency>-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<version>2.1.2.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>galaxy-service-msg</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>galaxy-service-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>caidaocloud-commons</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>galaxy-service-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>galaxy-service-security</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.4</version>
		</dependency>
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<version>${kotlin.stdlib.version}</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.totallylazy</groupId>
			<artifactId>totallylazy</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>metadata-sdk</artifactId>
			<version>2.0.2-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>galaxy-service-xxljob</artifactId>
			<version>2.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.cronutils</groupId>
			<artifactId>cron-utils</artifactId>
			<version>9.2.0</version>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>paas-sdk</artifactId>
			<version>2.0.2-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud.masterdata</groupId>
			<artifactId>masterdata-core</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.zaxxer</groupId>-->
<!--			<artifactId>HikariCP</artifactId>-->
<!--			<version>3.2.0</version>-->
<!--						<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.postgresql</groupId>-->
<!--			<artifactId>postgresql</artifactId>-->
<!--						<scope>test</scope>-->
<!--		</dependency>-->
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caidaocloud</groupId>
			<artifactId>workflow-sdk</artifactId>
			<version>2.1.1-SNAPSHOT</version>
		</dependency>
		<!--		<dependency>-->
		<!--			<groupId>org.jetbrains.kotlin</groupId>-->
		<!--			<artifactId>kotlin-reflect</artifactId>-->
		<!--			<version>1.5.21</version>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--			<groupId>com.fasterxml.jackson.module</groupId>-->
		<!--			<artifactId>jackson-module-kotlin</artifactId>-->
		<!--			<version>2.16.0</version>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--			<groupId>com.fasterxml.jackson.core</groupId>-->
		<!--			<artifactId>jackson-annotations</artifactId>-->
		<!--			<version>2.13.4</version>-->
		<!--		</dependency>-->

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
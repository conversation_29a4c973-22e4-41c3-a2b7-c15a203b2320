package com.caidaocloud.compute.remote.framework.core;

import com.caidaocloud.compute.remote.framework.util.PortUtil;
import lombok.extern.slf4j.Slf4j;

import static com.caidaocloud.compute.remote.framework.util.PortUtil.isPortInUse;

/**
 *
 * <AUTHOR>
 * @date 2024/12/31
 */
@Slf4j
public abstract class DynamicPortInitializer implements RemoteInitializer {
	private final InitializerConfig initializerConfig;

	protected DynamicPortInitializer(InitializerConfig initializerConfig) {
		this.initializerConfig = initializerConfig;
		initAvailablePort();
	}

	public 	InitializerConfig getInitializerConfig() {
		return initializerConfig;
	}

	public void initAvailablePort() {
		if (initializerConfig.getPort() == 0) {
			return;
		}
		int startPort = initializerConfig.getPort();
		int endPort = startPort + initializerConfig.getPortRange();
		for (int port = startPort; port < endPort; port++) {
			if (!PortUtil.isPortInUse(port)) {
				log.info("vertx端口："+port);
				initializerConfig.setPort(port);
				return;
			}
		}
		throw new RuntimeException("No available ports in the specified range ["+startPort+","+(endPort-1)+"].");
	}

}

package com.caidaocloud.compute.remote.framework.core;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.Map;

import com.caidaocloud.compute.remote.framework.actor.RemoteAddress;
import com.caidaocloud.compute.remote.framework.scan.MethodMetadata;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
public class RemoteRequestFactoryBean  implements FactoryBean, ApplicationContextAware {
	private ApplicationContext context;


	private Class<?> clazz;

	@Override
	public Object getObject() throws Exception {
		RequestHandlerGenerator generator = context.getBean(RequestHandlerGenerator.class);
		Map<String, MethodMetadata> methodMetadataMap = parseMethodMetadata(clazz);
		return generator.createProxy(clazz, methodMetadataMap);
	}

	private Map<String, MethodMetadata> parseMethodMetadata(Class<?> clz) {
		Map<String, MethodMetadata> methodMetadataMap = new HashMap<>();
		for (Method method : clz.getDeclaredMethods()) {
			MethodMetadata metadata = new MethodMetadata(method);
			Parameter[] parameters = method.getParameters();
			for (int i = 0; i < parameters.length; i++) {
				Parameter parameter = parameters[i];
				if (parameter.getDeclaredAnnotation(RemoteAddress.class) != null) {
					metadata.setAddressIndex(i);
					break;
				}
			}

			methodMetadataMap.put(MethodMetadata.parseMethodName(method), metadata);
		}
		return methodMetadataMap;
	}

	@Override
	public Class<?> getObjectType() {
		return clazz;
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.context = applicationContext;
	}

	public RemoteRequestFactoryBean setClazz(Class<?> clazz) {
		this.clazz = clazz;
		return this;
	}
}
